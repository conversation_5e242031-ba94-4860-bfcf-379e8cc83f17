<template>
  <div class="user-login">
    <div class="container">
      <div class="login-card">
        <div class="bg-image">
          <img src="../assets/bg.png" alt="背景图片" />
        </div>

        <h1 class="title">欢迎使用本科技，姬霓太美调音科技</h1>

        <form @submit.prevent="handleSubmit" class="login-form">
          <div class="input-group">
            <input
              v-model="wechatId"
              type="text"
              placeholder="请输入手机号或者vx号注册"
              class="input-field"
              :class="{ error: hasError }"
              @input="clearError"
            />
            <div v-if="hasError" class="error-message">{{ errorMessage }}</div>
          </div>

          <button type="submit" class="login-btn" :disabled="isSubmitting">
            {{ isSubmitting ? "提交中..." : "注册" }}
          </button>
        </form>

        <div class="admin-link">
          <router-link to="/admin" class="admin-text">管理员入口</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  name: "UserLogin",
  data() {
    return {
      wechatId: "",
      hasError: false,
      errorMessage: "",
      isSubmitting: false,
    };
  },
  methods: {
    validateWechatId(id) {
      if (!id.trim()) {
        return "请输入内容";
      }
      return null;
    },

    clearError() {
      this.hasError = false;
      this.errorMessage = "";
    },

    async handleSubmit() {
      const error = this.validateWechatId(this.wechatId);
      if (error) {
        this.hasError = true;
        this.errorMessage = error;
        return;
      }

      this.isSubmitting = true;

      try {
        console.log("提交微信号:", this.wechatId.trim());
        const response = await axios.post(
          "/api/wechat",
          {
            wechatId: this.wechatId.trim(),
          },
          {
            headers: {
              "Content-Type": "application/json",
            },
            timeout: 10000,
          }
        );

        console.log("提交响应:", response.data);

        if (response.data.success) {
          // 跳转到欢迎页面
          this.$router.push("/welcome").catch((err) => {
            console.error("路由跳转失败:", err);
          });
        } else {
          this.hasError = true;
          this.errorMessage = response.data.message || "提交失败，请重试";
        }
      } catch (error) {
        console.error("提交微信号失败:", error);
        this.hasError = true;

        if (error.code === "ECONNABORTED") {
          this.errorMessage = "请求超时，请检查网络连接";
        } else if (error.response) {
          const status = error.response.status;
          const message = error.response.data?.message;

          if (status === 400) {
            this.errorMessage = message || "输入数据有误";
          } else if (status === 500) {
            this.errorMessage = "服务器内部错误，请稍后重试";
          } else {
            this.errorMessage = message || `提交失败 (${status})`;
          }
        } else if (error.request) {
          this.errorMessage = "无法连接到服务器，请检查后端服务是否启动";
        } else {
          this.errorMessage = "提交失败，请重试";
        }
      } finally {
        this.isSubmitting = false;
      }
    },
  },
};
</script>

<style scoped>
.user-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.bg-image {
  margin-bottom: 20px;
  text-align: center;
}

.bg-image img {
  max-width: 100%;
  height: auto;
  max-height: 150px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
  font-weight: 600;
}

.login-form {
  margin-bottom: 20px;
}

.input-group {
  margin-bottom: 20px;
  text-align: left;
}

.input-field {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.input-field:focus {
  outline: none;
  border-color: #667eea;
}

.input-field.error {
  border-color: #ff4757;
}

.error-message {
  color: #ff4757;
  font-size: 14px;
  margin-top: 5px;
}

.login-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.admin-link {
  margin-top: 20px;
}

.admin-text {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
}

.admin-text:hover {
  text-decoration: underline;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .user-login {
    padding: 15px;
    min-height: 100vh;
    display: flex;
    align-items: center;
  }

  .container {
    width: 100%;
    max-width: 100%;
  }

  .login-card {
    padding: 35px 25px;
    margin: 0;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .bg-image {
    margin-bottom: 15px;
  }

  .bg-image img {
    max-height: 120px;
    border-radius: 8px;
  }

  .title {
    font-size: 22px;
    margin-bottom: 35px;
    font-weight: 600;
    line-height: 1.3;
  }

  .input-field {
    padding: 16px 18px;
    font-size: 16px;
    border-radius: 12px;
    border: 2px solid #e8ecf0;
    margin-bottom: 8px;
  }

  .input-field:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .login-btn {
    padding: 16px;
    font-size: 17px;
    font-weight: 600;
    border-radius: 12px;
    margin-top: 10px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  .login-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
  }

  .admin-link {
    margin-top: 25px;
  }

  .admin-text {
    font-size: 15px;
    font-weight: 500;
  }

  .error-message {
    font-size: 13px;
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(255, 71, 87, 0.1);
    border-radius: 8px;
    border-left: 3px solid #ff4757;
  }
}
</style>
