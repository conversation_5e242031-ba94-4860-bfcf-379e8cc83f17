<template>
  <div class="admin-dashboard">
    <div class="container">
      <div class="stats">
        <div class="stat-card">
          <div class="stat-number">{{ wechatAccounts.length }}</div>
          <div class="stat-label">总收集数量</div>
        </div>
      </div>

      <div class="accounts-section">
        <div class="section-header">
          <h2>微信号列表</h2>
          <div class="header-actions">
            <button
              @click="exportToExcel"
              class="export-btn"
              :disabled="isLoading || wechatAccounts.length === 0"
            >
              导出Excel
            </button>
            <button
              @click="refreshData"
              class="refresh-btn"
              :disabled="isLoading"
            >
              {{ isLoading ? "刷新中..." : "刷新" }}
            </button>
          </div>
        </div>

        <div v-if="isLoading" class="loading">加载中...</div>

        <div v-else-if="loadError" class="error-state">
          <p class="error-message">{{ errorMessage }}</p>
          <button @click="refreshData" class="retry-btn">重试</button>
        </div>

        <div v-else-if="wechatAccounts.length === 0" class="empty-state">
          <p>暂无收集到的微信号</p>
        </div>

        <div v-else class="accounts-list">
          <div
            v-for="(account, index) in wechatAccounts"
            :key="account.id"
            class="account-card"
          >
            <div class="account-number">{{ index + 1 }}</div>
            <div class="account-info">
              <div class="wechat-id">{{ account.wechatId }}</div>
              <div class="submit-time">
                {{ formatTime(account.submitTime) }}
              </div>
              <div class="ip-address">
                IP: {{ account.ipAddress || "未知" }}
              </div>
            </div>
            <div class="action-buttons">
              <button
                @click="copyWechatId(account.wechatId, $event)"
                class="copy-btn"
              >
                复制
              </button>
              <button
                @click="deleteWechatId(account)"
                class="delete-btn"
                :disabled="deletingIds.has(account.id)"
              >
                {{ deletingIds.has(account.id) ? "删除中..." : "删除" }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

export default {
  name: "AdminDashboard",
  data() {
    return {
      wechatAccounts: [],
      isLoading: false,
      loadError: false,
      errorMessage: "",
      deletingIds: new Set(), // 正在删除的ID集合
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      this.isLoading = true;
      this.loadError = false;
      this.errorMessage = "";
      try {
        const response = await axios.get("/api/wechat");
        this.wechatAccounts = response.data.wechatAccounts || [];
      } catch (error) {
        console.error("加载数据失败:", error);
        this.loadError = true;
        this.errorMessage = "加载数据失败，请重试";
      } finally {
        this.isLoading = false;
      }
    },

    async refreshData() {
      await this.loadData();
    },

    formatTime(timeString) {
      const date = new Date(timeString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    },

    async copyWechatId(wechatId, event) {
      // 优化事件对象处理，使用 currentTarget 更可靠
      let button = null;

      if (event && event.currentTarget) {
        button = event.currentTarget;
      } else if (event && event.target) {
        button = event.target;
      } else {
        console.warn("复制操作缺少事件对象，尝试查找按钮");
        // 降级方案：通过DOM查找
        const buttons = document.querySelectorAll(".copy-btn");
        button = Array.from(buttons).find(
          (btn) =>
            btn.closest(".account-card")?.querySelector(".wechat-id")
              ?.textContent === wechatId
        );
      }

      if (!button) {
        console.error("无法找到复制按钮");
        return;
      }

      const originalText = button.textContent;

      try {
        await navigator.clipboard.writeText(wechatId);
        this.showCopySuccess(button, originalText);
      } catch (error) {
        console.error("复制失败，使用降级方案:", error);
        // 降级方案：使用传统方法复制
        try {
          const textArea = document.createElement("textarea");
          textArea.value = wechatId;
          textArea.style.position = "fixed";
          textArea.style.opacity = "0";
          textArea.style.left = "-9999px";
          document.body.appendChild(textArea);
          textArea.select();
          textArea.setSelectionRange(0, 99999); // 移动端兼容
          document.execCommand("copy");
          document.body.removeChild(textArea);
          this.showCopySuccess(button, originalText);
        } catch (fallbackError) {
          console.error("降级复制方案也失败:", fallbackError);
          this.showCopyError(button, originalText);
        }
      }
    },

    showCopySuccess(button, originalText) {
      button.textContent = "已复制";
      button.style.background = "#4CAF50";
      button.style.transform = "scale(0.95)";

      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = "";
        button.style.transform = "";
      }, 1500);
    },

    showCopyError(button, originalText) {
      button.textContent = "复制失败";
      button.style.background = "#ff4757";

      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = "";
      }, 2000);
    },

    async deleteWechatId(account) {
      // 确认删除
      if (!confirm(`确定要删除微信号 "${account.wechatId}" 吗？`)) {
        return;
      }

      this.deletingIds.add(account.id);

      try {
        const response = await axios.delete(`/api/wechat/${account.id}`);

        if (response.data.success) {
          // 从本地数据中移除
          this.wechatAccounts = this.wechatAccounts.filter(
            (item) => item.id !== account.id
          );
          console.log(`删除成功: ${account.wechatId}`);
        } else {
          console.error("删除失败:", response.data.message);
          alert(response.data.message || "删除失败，请重试");
        }
      } catch (error) {
        console.error("删除微信号失败:", error);
        alert("删除失败，请重试");
      } finally {
        this.deletingIds.delete(account.id);
      }
    },

    exportToExcel() {
      if (this.wechatAccounts.length === 0) {
        alert("暂无数据可导出");
        return;
      }

      try {
        // 准备导出数据
        const exportData = this.wechatAccounts.map((account, index) => ({
          序号: index + 1,
          微信号: account.wechatId,
          提交时间: this.formatTime(account.submitTime),
          IP地址: account.ipAddress || "未知",
          记录ID: account.id,
        }));

        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 创建工作表
        const ws = XLSX.utils.json_to_sheet(exportData);

        // 设置列宽
        const colWidths = [
          { wch: 8 }, // 序号
          { wch: 20 }, // 微信号
          { wch: 20 }, // 提交时间
          { wch: 18 }, // IP地址
          { wch: 40 }, // 记录ID
        ];
        ws["!cols"] = colWidths;

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, "微信号列表");

        // 生成文件名（包含当前时间）
        const now = new Date();
        const timestamp = now
          .toISOString()
          .slice(0, 19)
          .replace(/[:-]/g, "")
          .replace("T", "_");
        const filename = `微信号列表_${timestamp}.xlsx`;

        // 导出文件
        const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
        const blob = new Blob([wbout], { type: "application/octet-stream" });
        saveAs(blob, filename);

        console.log(`导出成功: ${filename}`);
      } catch (error) {
        console.error("导出Excel失败:", error);
        alert("导出失败，请重试");
      }
    },
  },
};
</script>

<style scoped>
.admin-dashboard {
  min-height: 100vh;
  padding: 20px;
  background-image: url("../assets/bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.stats {
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  display: inline-block;
  min-width: 200px;
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 16px;
  color: #666;
}

.accounts-section {
  background: white;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.export-btn {
  padding: 8px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  font-weight: 600;
}

.export-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.refresh-btn {
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.refresh-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading,
.empty-state,
.error-state {
  padding: 40px;
  text-align: center;
  color: #666;
}

.error-state .error-message {
  color: #ff4757;
  margin-bottom: 20px;
  font-size: 16px;
}

.retry-btn {
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.retry-btn:hover {
  background: #5a6fd8;
}

.accounts-list {
  max-height: 500px;
  overflow-y: auto;
}

/* 桌面端保持列表样式 */
.account-card {
  display: flex;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.3s;
  gap: 20px;
}

.account-card:hover {
  background: #f8f9fa;
}

.account-card:last-child {
  border-bottom: none;
}

.account-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
}

.account-info {
  flex: 1;
}

.wechat-id {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.submit-time {
  font-size: 14px;
  color: #666;
}

.ip-address {
  font-size: 13px;
  color: #888;
  font-family: "Courier New", monospace;
  margin-top: 2px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.copy-btn {
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.copy-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.delete-btn {
  padding: 8px 16px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.delete-btn:hover:not(:disabled) {
  background: #ff3838;
  transform: translateY(-1px);
}

.delete-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
    background-image: url("../assets/bg.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
  }

  .container {
    max-width: 100%;
  }

  .stats {
    margin-bottom: 24px;
  }

  .stat-card {
    width: 100%;
    box-sizing: border-box;
    padding: 28px 24px;
    border-radius: 16px;
    margin: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .stat-number {
    font-size: 36px;
    margin-bottom: 10px;
    font-weight: 700;
  }

  .stat-label {
    font-size: 16px;
    font-weight: 500;
  }

  .accounts-section {
    border-radius: 16px;
    overflow: visible;
    box-shadow: none;
    background: transparent;
  }

  .section-header {
    flex-direction: column;
    gap: 18px;
    padding: 22px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 8px;
  }

  .section-header h2 {
    font-size: 18px;
    text-align: center;
    margin: 0;
    font-weight: 600;
  }

  .header-actions {
    flex-direction: row;
    gap: 12px;
    justify-content: center;
    width: 100%;
  }

  .export-btn {
    flex: 1;
    padding: 12px 20px;
    font-size: 15px;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    min-height: 44px;
  }

  .export-btn:hover:not(:disabled) {
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
  }

  .refresh-btn {
    flex: 1;
    padding: 12px 20px;
    font-size: 15px;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    min-height: 44px;
  }

  .refresh-btn:hover:not(:disabled) {
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  .accounts-list {
    max-height: 65vh;
    padding: 16px;
    background: transparent;
    overflow-y: auto;
  }

  /* 移动端改为卡片式布局 */
  .account-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 16px;
    padding: 20px;
    border: none;
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    transition: all 0.3s ease;
    position: relative;
  }

  .account-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    background: white;
  }

  .account-card:last-child {
    margin-bottom: 0;
  }

  .account-number {
    position: absolute;
    top: -8px;
    left: -8px;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    z-index: 1;
  }

  .account-info {
    text-align: center;
    padding: 8px 0;
  }

  .wechat-id {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #1a202c;
    word-break: break-all;
    line-height: 1.4;
  }

  .submit-time {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    align-items: stretch;
  }

  .copy-btn {
    flex: 1;
    text-align: center;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 700;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 0.5px;
  }

  .copy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  .copy-btn:active {
    transform: translateY(0);
  }

  .delete-btn {
    flex: 1;
    text-align: center;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 700;
    border-radius: 12px;
    background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
    box-shadow: 0 4px 16px rgba(255, 71, 87, 0.3);
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 0.5px;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .delete-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
  }

  .delete-btn:active:not(:disabled) {
    transform: translateY(0);
  }

  .delete-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 16px rgba(255, 71, 87, 0.2);
  }

  .loading,
  .empty-state,
  .error-state {
    padding: 36px 24px;
    font-size: 16px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin: 16px;
  }

  .error-state .error-message {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .retry-btn {
    padding: 12px 24px;
    font-size: 15px;
    border-radius: 10px;
    font-weight: 600;
  }
}
</style>
