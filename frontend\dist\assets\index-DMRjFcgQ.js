(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function r(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function n(i){if(i.ep)return;i.ep=!0;const s=r(i);fetch(i.href,s)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Eo(e){const t=Object.create(null);for(const r of e.split(","))t[r]=1;return r=>r in t}const Xe={},Nn=[],Sr=()=>{},Yh=()=>!1,Gs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),wo=e=>e.startsWith("onUpdate:"),Rt=Object.assign,To=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},qh=Object.prototype.hasOwnProperty,He=(e,t)=>qh.call(e,t),Ae=Array.isArray,bn=e=>Ks(e)==="[object Map]",ol=e=>Ks(e)==="[object Set]",Ce=e=>typeof e=="function",at=e=>typeof e=="string",rn=e=>typeof e=="symbol",Je=e=>e!==null&&typeof e=="object",fl=e=>(Je(e)||Ce(e))&&Ce(e.then)&&Ce(e.catch),ll=Object.prototype.toString,Ks=e=>ll.call(e),Jh=e=>Ks(e).slice(8,-1),cl=e=>Ks(e)==="[object Object]",So=e=>at(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,hi=Eo(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Xs=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},Zh=/-(\w)/g,sr=Xs(e=>e.replace(Zh,(t,r)=>r?r.toUpperCase():"")),Qh=/\B([A-Z])/g,vn=Xs(e=>e.replace(Qh,"-$1").toLowerCase()),zs=Xs(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ta=Xs(e=>e?`on${zs(e)}`:""),qr=(e,t)=>!Object.is(e,t),us=(e,...t)=>{for(let r=0;r<e.length;r++)e[r](...t)},ul=(e,t,r,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:r})},Xa=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let p0;const Ys=()=>p0||(p0=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function qs(e){if(Ae(e)){const t={};for(let r=0;r<e.length;r++){const n=e[r],i=at(n)?n1(n):qs(n);if(i)for(const s in i)t[s]=i[s]}return t}else if(at(e)||Je(e))return e}const e1=/;(?![^(]*\))/g,t1=/:([^]+)/,r1=/\/\*[^]*?\*\//g;function n1(e){const t={};return e.replace(r1,"").split(e1).forEach(r=>{if(r){const n=r.split(t1);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function jn(e){let t="";if(at(e))t=e;else if(Ae(e))for(let r=0;r<e.length;r++){const n=jn(e[r]);n&&(t+=n+" ")}else if(Je(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}const i1="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",s1=Eo(i1);function hl(e){return!!e||e===""}const dl=e=>!!(e&&e.__v_isRef===!0),Mt=e=>at(e)?e:e==null?"":Ae(e)||Je(e)&&(e.toString===ll||!Ce(e.toString))?dl(e)?Mt(e.value):JSON.stringify(e,pl,2):String(e),pl=(e,t)=>dl(t)?pl(e,t.value):bn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((r,[n,i],s)=>(r[Sa(n,s)+" =>"]=i,r),{})}:ol(t)?{[`Set(${t.size})`]:[...t.values()].map(r=>Sa(r))}:rn(t)?Sa(t):Je(t)&&!Ae(t)&&!cl(t)?String(t):t,Sa=(e,t="")=>{var r;return rn(e)?`Symbol(${(r=e.description)!=null?r:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Lt;class a1{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Lt,!t&&Lt&&(this.index=(Lt.scopes||(Lt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].pause();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].resume();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].resume()}}run(t){if(this._active){const r=Lt;try{return Lt=this,t()}finally{Lt=r}}}on(){++this._on===1&&(this.prevScope=Lt,Lt=this)}off(){this._on>0&&--this._on===0&&(Lt=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let r,n;for(r=0,n=this.effects.length;r<n;r++)this.effects[r].stop();for(this.effects.length=0,r=0,n=this.cleanups.length;r<n;r++)this.cleanups[r]();if(this.cleanups.length=0,this.scopes){for(r=0,n=this.scopes.length;r<n;r++)this.scopes[r].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function o1(){return Lt}let ze;const ya=new WeakSet;class xl{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Lt&&Lt.active&&Lt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ya.has(this)&&(ya.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||gl(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,x0(this),vl(this);const t=ze,r=hr;ze=this,hr=!0;try{return this.fn()}finally{_l(this),ze=t,hr=r,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Fo(t);this.deps=this.depsTail=void 0,x0(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ya.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){za(this)&&this.run()}get dirty(){return za(this)}}let ml=0,di,pi;function gl(e,t=!1){if(e.flags|=8,t){e.next=pi,pi=e;return}e.next=di,di=e}function yo(){ml++}function Ao(){if(--ml>0)return;if(pi){let t=pi;for(pi=void 0;t;){const r=t.next;t.next=void 0,t.flags&=-9,t=r}}let e;for(;di;){let t=di;for(di=void 0;t;){const r=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=r}}if(e)throw e}function vl(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function _l(e){let t,r=e.depsTail,n=r;for(;n;){const i=n.prevDep;n.version===-1?(n===r&&(r=i),Fo(n),f1(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=i}e.deps=t,e.depsTail=r}function za(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(El(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function El(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Fi)||(e.globalVersion=Fi,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!za(e))))return;e.flags|=2;const t=e.dep,r=ze,n=hr;ze=e,hr=!0;try{vl(e);const i=e.fn(e._value);(t.version===0||qr(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{ze=r,hr=n,_l(e),e.flags&=-3}}function Fo(e,t=!1){const{dep:r,prevSub:n,nextSub:i}=e;if(n&&(n.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=n,e.nextSub=void 0),r.subs===e&&(r.subs=n,!n&&r.computed)){r.computed.flags&=-5;for(let s=r.computed.deps;s;s=s.nextDep)Fo(s,!0)}!t&&!--r.sc&&r.map&&r.map.delete(r.key)}function f1(e){const{prevDep:t,nextDep:r}=e;t&&(t.nextDep=r,e.prevDep=void 0),r&&(r.prevDep=t,e.nextDep=void 0)}let hr=!0;const wl=[];function Lr(){wl.push(hr),hr=!1}function Mr(){const e=wl.pop();hr=e===void 0?!0:e}function x0(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const r=ze;ze=void 0;try{t()}finally{ze=r}}}let Fi=0;class l1{constructor(t,r){this.sub=t,this.dep=r,this.version=r.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Co{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ze||!hr||ze===this.computed)return;let r=this.activeLink;if(r===void 0||r.sub!==ze)r=this.activeLink=new l1(ze,this),ze.deps?(r.prevDep=ze.depsTail,ze.depsTail.nextDep=r,ze.depsTail=r):ze.deps=ze.depsTail=r,Tl(r);else if(r.version===-1&&(r.version=this.version,r.nextDep)){const n=r.nextDep;n.prevDep=r.prevDep,r.prevDep&&(r.prevDep.nextDep=n),r.prevDep=ze.depsTail,r.nextDep=void 0,ze.depsTail.nextDep=r,ze.depsTail=r,ze.deps===r&&(ze.deps=n)}return r}trigger(t){this.version++,Fi++,this.notify(t)}notify(t){yo();try{for(let r=this.subs;r;r=r.prevSub)r.sub.notify()&&r.sub.dep.notify()}finally{Ao()}}}function Tl(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)Tl(n)}const r=e.dep.subs;r!==e&&(e.prevSub=r,r&&(r.nextSub=e)),e.dep.subs=e}}const Ya=new WeakMap,un=Symbol(""),qa=Symbol(""),Ci=Symbol("");function St(e,t,r){if(hr&&ze){let n=Ya.get(e);n||Ya.set(e,n=new Map);let i=n.get(r);i||(n.set(r,i=new Co),i.map=n,i.key=r),i.track()}}function Pr(e,t,r,n,i,s){const a=Ya.get(e);if(!a){Fi++;return}const o=f=>{f&&f.trigger()};if(yo(),t==="clear")a.forEach(o);else{const f=Ae(e),l=f&&So(r);if(f&&r==="length"){const c=Number(n);a.forEach((u,h)=>{(h==="length"||h===Ci||!rn(h)&&h>=c)&&o(u)})}else switch((r!==void 0||a.has(void 0))&&o(a.get(r)),l&&o(a.get(Ci)),t){case"add":f?l&&o(a.get("length")):(o(a.get(un)),bn(e)&&o(a.get(qa)));break;case"delete":f||(o(a.get(un)),bn(e)&&o(a.get(qa)));break;case"set":bn(e)&&o(a.get(un));break}}Ao()}function yn(e){const t=Ue(e);return t===e?t:(St(t,"iterate",Ci),ir(e)?t:t.map(xt))}function Js(e){return St(e=Ue(e),"iterate",Ci),e}const c1={__proto__:null,[Symbol.iterator](){return Aa(this,Symbol.iterator,xt)},concat(...e){return yn(this).concat(...e.map(t=>Ae(t)?yn(t):t))},entries(){return Aa(this,"entries",e=>(e[1]=xt(e[1]),e))},every(e,t){return Cr(this,"every",e,t,void 0,arguments)},filter(e,t){return Cr(this,"filter",e,t,r=>r.map(xt),arguments)},find(e,t){return Cr(this,"find",e,t,xt,arguments)},findIndex(e,t){return Cr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Cr(this,"findLast",e,t,xt,arguments)},findLastIndex(e,t){return Cr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Cr(this,"forEach",e,t,void 0,arguments)},includes(...e){return Fa(this,"includes",e)},indexOf(...e){return Fa(this,"indexOf",e)},join(e){return yn(this).join(e)},lastIndexOf(...e){return Fa(this,"lastIndexOf",e)},map(e,t){return Cr(this,"map",e,t,void 0,arguments)},pop(){return ii(this,"pop")},push(...e){return ii(this,"push",e)},reduce(e,...t){return m0(this,"reduce",e,t)},reduceRight(e,...t){return m0(this,"reduceRight",e,t)},shift(){return ii(this,"shift")},some(e,t){return Cr(this,"some",e,t,void 0,arguments)},splice(...e){return ii(this,"splice",e)},toReversed(){return yn(this).toReversed()},toSorted(e){return yn(this).toSorted(e)},toSpliced(...e){return yn(this).toSpliced(...e)},unshift(...e){return ii(this,"unshift",e)},values(){return Aa(this,"values",xt)}};function Aa(e,t,r){const n=Js(e),i=n[t]();return n!==e&&!ir(e)&&(i._next=i.next,i.next=()=>{const s=i._next();return s.value&&(s.value=r(s.value)),s}),i}const u1=Array.prototype;function Cr(e,t,r,n,i,s){const a=Js(e),o=a!==e&&!ir(e),f=a[t];if(f!==u1[t]){const u=f.apply(e,s);return o?xt(u):u}let l=r;a!==e&&(o?l=function(u,h){return r.call(this,xt(u),h,e)}:r.length>2&&(l=function(u,h){return r.call(this,u,h,e)}));const c=f.call(a,l,n);return o&&i?i(c):c}function m0(e,t,r,n){const i=Js(e);let s=r;return i!==e&&(ir(e)?r.length>3&&(s=function(a,o,f){return r.call(this,a,o,f,e)}):s=function(a,o,f){return r.call(this,a,xt(o),f,e)}),i[t](s,...n)}function Fa(e,t,r){const n=Ue(e);St(n,"iterate",Ci);const i=n[t](...r);return(i===-1||i===!1)&&Po(r[0])?(r[0]=Ue(r[0]),n[t](...r)):i}function ii(e,t,r=[]){Lr(),yo();const n=Ue(e)[t].apply(e,r);return Ao(),Mr(),n}const h1=Eo("__proto__,__v_isRef,__isVue"),Sl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(rn));function d1(e){rn(e)||(e=String(e));const t=Ue(this);return St(t,"has",e),t.hasOwnProperty(e)}class yl{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,n){if(r==="__v_skip")return t.__v_skip;const i=this._isReadonly,s=this._isShallow;if(r==="__v_isReactive")return!i;if(r==="__v_isReadonly")return i;if(r==="__v_isShallow")return s;if(r==="__v_raw")return n===(i?s?S1:Ol:s?Cl:Fl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const a=Ae(t);if(!i){let f;if(a&&(f=c1[r]))return f;if(r==="hasOwnProperty")return d1}const o=Reflect.get(t,r,Ft(t)?t:n);return(rn(r)?Sl.has(r):h1(r))||(i||St(t,"get",r),s)?o:Ft(o)?a&&So(r)?o:o.value:Je(o)?i?Pl(o):Zs(o):o}}class Al extends yl{constructor(t=!1){super(!1,t)}set(t,r,n,i){let s=t[r];if(!this._isShallow){const f=Jr(s);if(!ir(n)&&!Jr(n)&&(s=Ue(s),n=Ue(n)),!Ae(t)&&Ft(s)&&!Ft(n))return f?!1:(s.value=n,!0)}const a=Ae(t)&&So(r)?Number(r)<t.length:He(t,r),o=Reflect.set(t,r,n,Ft(t)?t:i);return t===Ue(i)&&(a?qr(n,s)&&Pr(t,"set",r,n):Pr(t,"add",r,n)),o}deleteProperty(t,r){const n=He(t,r);t[r];const i=Reflect.deleteProperty(t,r);return i&&n&&Pr(t,"delete",r,void 0),i}has(t,r){const n=Reflect.has(t,r);return(!rn(r)||!Sl.has(r))&&St(t,"has",r),n}ownKeys(t){return St(t,"iterate",Ae(t)?"length":un),Reflect.ownKeys(t)}}class p1 extends yl{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const x1=new Al,m1=new p1,g1=new Al(!0);const Ja=e=>e,es=e=>Reflect.getPrototypeOf(e);function v1(e,t,r){return function(...n){const i=this.__v_raw,s=Ue(i),a=bn(s),o=e==="entries"||e===Symbol.iterator&&a,f=e==="keys"&&a,l=i[e](...n),c=r?Ja:t?Es:xt;return!t&&St(s,"iterate",f?qa:un),{next(){const{value:u,done:h}=l.next();return h?{value:u,done:h}:{value:o?[c(u[0]),c(u[1])]:c(u),done:h}},[Symbol.iterator](){return this}}}}function ts(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function _1(e,t){const r={get(i){const s=this.__v_raw,a=Ue(s),o=Ue(i);e||(qr(i,o)&&St(a,"get",i),St(a,"get",o));const{has:f}=es(a),l=t?Ja:e?Es:xt;if(f.call(a,i))return l(s.get(i));if(f.call(a,o))return l(s.get(o));s!==a&&s.get(i)},get size(){const i=this.__v_raw;return!e&&St(Ue(i),"iterate",un),Reflect.get(i,"size",i)},has(i){const s=this.__v_raw,a=Ue(s),o=Ue(i);return e||(qr(i,o)&&St(a,"has",i),St(a,"has",o)),i===o?s.has(i):s.has(i)||s.has(o)},forEach(i,s){const a=this,o=a.__v_raw,f=Ue(o),l=t?Ja:e?Es:xt;return!e&&St(f,"iterate",un),o.forEach((c,u)=>i.call(s,l(c),l(u),a))}};return Rt(r,e?{add:ts("add"),set:ts("set"),delete:ts("delete"),clear:ts("clear")}:{add(i){!t&&!ir(i)&&!Jr(i)&&(i=Ue(i));const s=Ue(this);return es(s).has.call(s,i)||(s.add(i),Pr(s,"add",i,i)),this},set(i,s){!t&&!ir(s)&&!Jr(s)&&(s=Ue(s));const a=Ue(this),{has:o,get:f}=es(a);let l=o.call(a,i);l||(i=Ue(i),l=o.call(a,i));const c=f.call(a,i);return a.set(i,s),l?qr(s,c)&&Pr(a,"set",i,s):Pr(a,"add",i,s),this},delete(i){const s=Ue(this),{has:a,get:o}=es(s);let f=a.call(s,i);f||(i=Ue(i),f=a.call(s,i)),o&&o.call(s,i);const l=s.delete(i);return f&&Pr(s,"delete",i,void 0),l},clear(){const i=Ue(this),s=i.size!==0,a=i.clear();return s&&Pr(i,"clear",void 0,void 0),a}}),["keys","values","entries",Symbol.iterator].forEach(i=>{r[i]=v1(i,e,t)}),r}function Oo(e,t){const r=_1(e,t);return(n,i,s)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?n:Reflect.get(He(r,i)&&i in n?r:n,i,s)}const E1={get:Oo(!1,!1)},w1={get:Oo(!1,!0)},T1={get:Oo(!0,!1)};const Fl=new WeakMap,Cl=new WeakMap,Ol=new WeakMap,S1=new WeakMap;function y1(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function A1(e){return e.__v_skip||!Object.isExtensible(e)?0:y1(Jh(e))}function Zs(e){return Jr(e)?e:Ro(e,!1,x1,E1,Fl)}function Rl(e){return Ro(e,!1,g1,w1,Cl)}function Pl(e){return Ro(e,!0,m1,T1,Ol)}function Ro(e,t,r,n,i){if(!Je(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=A1(e);if(s===0)return e;const a=i.get(e);if(a)return a;const o=new Proxy(e,s===2?n:r);return i.set(e,o),o}function kn(e){return Jr(e)?kn(e.__v_raw):!!(e&&e.__v_isReactive)}function Jr(e){return!!(e&&e.__v_isReadonly)}function ir(e){return!!(e&&e.__v_isShallow)}function Po(e){return e?!!e.__v_raw:!1}function Ue(e){const t=e&&e.__v_raw;return t?Ue(t):e}function F1(e){return!He(e,"__v_skip")&&Object.isExtensible(e)&&ul(e,"__v_skip",!0),e}const xt=e=>Je(e)?Zs(e):e,Es=e=>Je(e)?Pl(e):e;function Ft(e){return e?e.__v_isRef===!0:!1}function C1(e){return Dl(e,!1)}function O1(e){return Dl(e,!0)}function Dl(e,t){return Ft(e)?e:new R1(e,t)}class R1{constructor(t,r){this.dep=new Co,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=r?t:Ue(t),this._value=r?t:xt(t),this.__v_isShallow=r}get value(){return this.dep.track(),this._value}set value(t){const r=this._rawValue,n=this.__v_isShallow||ir(t)||Jr(t);t=n?t:Ue(t),qr(t,r)&&(this._rawValue=t,this._value=n?t:xt(t),this.dep.trigger())}}function Ln(e){return Ft(e)?e.value:e}const P1={get:(e,t,r)=>t==="__v_raw"?e:Ln(Reflect.get(e,t,r)),set:(e,t,r,n)=>{const i=e[t];return Ft(i)&&!Ft(r)?(i.value=r,!0):Reflect.set(e,t,r,n)}};function Il(e){return kn(e)?e:new Proxy(e,P1)}class D1{constructor(t,r,n){this.fn=t,this.setter=r,this._value=void 0,this.dep=new Co(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Fi-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!r,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&ze!==this)return gl(this,!0),!0}get value(){const t=this.dep.track();return El(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function I1(e,t,r=!1){let n,i;return Ce(e)?n=e:(n=e.get,i=e.set),new D1(n,i,r)}const rs={},ws=new WeakMap;let fn;function N1(e,t=!1,r=fn){if(r){let n=ws.get(r);n||ws.set(r,n=[]),n.push(e)}}function b1(e,t,r=Xe){const{immediate:n,deep:i,once:s,scheduler:a,augmentJob:o,call:f}=r,l=R=>i?R:ir(R)||i===!1||i===0?Dr(R,1):Dr(R);let c,u,h,d,x=!1,p=!1;if(Ft(e)?(u=()=>e.value,x=ir(e)):kn(e)?(u=()=>l(e),x=!0):Ae(e)?(p=!0,x=e.some(R=>kn(R)||ir(R)),u=()=>e.map(R=>{if(Ft(R))return R.value;if(kn(R))return l(R);if(Ce(R))return f?f(R,2):R()})):Ce(e)?t?u=f?()=>f(e,2):e:u=()=>{if(h){Lr();try{h()}finally{Mr()}}const R=fn;fn=c;try{return f?f(e,3,[d]):e(d)}finally{fn=R}}:u=Sr,t&&i){const R=u,z=i===!0?1/0:i;u=()=>Dr(R(),z)}const m=o1(),C=()=>{c.stop(),m&&m.active&&To(m.effects,c)};if(s&&t){const R=t;t=(...z)=>{R(...z),C()}}let F=p?new Array(e.length).fill(rs):rs;const y=R=>{if(!(!(c.flags&1)||!c.dirty&&!R))if(t){const z=c.run();if(i||x||(p?z.some((se,D)=>qr(se,F[D])):qr(z,F))){h&&h();const se=fn;fn=c;try{const D=[z,F===rs?void 0:p&&F[0]===rs?[]:F,d];F=z,f?f(t,3,D):t(...D)}finally{fn=se}}}else c.run()};return o&&o(y),c=new xl(u),c.scheduler=a?()=>a(y,!1):y,d=R=>N1(R,!1,c),h=c.onStop=()=>{const R=ws.get(c);if(R){if(f)f(R,4);else for(const z of R)z();ws.delete(c)}},t?n?y(!0):F=c.run():a?a(y.bind(null,!0),!0):c.run(),C.pause=c.pause.bind(c),C.resume=c.resume.bind(c),C.stop=C,C}function Dr(e,t=1/0,r){if(t<=0||!Je(e)||e.__v_skip||(r=r||new Set,r.has(e)))return e;if(r.add(e),t--,Ft(e))Dr(e.value,t,r);else if(Ae(e))for(let n=0;n<e.length;n++)Dr(e[n],t,r);else if(ol(e)||bn(e))e.forEach(n=>{Dr(n,t,r)});else if(cl(e)){for(const n in e)Dr(e[n],t,r);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&Dr(e[n],t,r)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Wi(e,t,r,n){try{return n?e(...n):e()}catch(i){Qs(i,t,r)}}function yr(e,t,r,n){if(Ce(e)){const i=Wi(e,t,r,n);return i&&fl(i)&&i.catch(s=>{Qs(s,t,r)}),i}if(Ae(e)){const i=[];for(let s=0;s<e.length;s++)i.push(yr(e[s],t,r,n));return i}}function Qs(e,t,r,n=!0){const i=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:a}=t&&t.appContext.config||Xe;if(t){let o=t.parent;const f=t.proxy,l=`https://vuejs.org/error-reference/#runtime-${r}`;for(;o;){const c=o.ec;if(c){for(let u=0;u<c.length;u++)if(c[u](e,f,l)===!1)return}o=o.parent}if(s){Lr(),Wi(s,null,10,[e,f,l]),Mr();return}}k1(e,r,i,n,a)}function k1(e,t,r,n=!0,i=!1){if(i)throw e;console.error(e)}const bt=[];let Er=-1;const Mn=[];let Gr=null,On=0;const Nl=Promise.resolve();let Ts=null;function bl(e){const t=Ts||Nl;return e?t.then(this?e.bind(this):e):t}function L1(e){let t=Er+1,r=bt.length;for(;t<r;){const n=t+r>>>1,i=bt[n],s=Oi(i);s<e||s===e&&i.flags&2?t=n+1:r=n}return t}function Do(e){if(!(e.flags&1)){const t=Oi(e),r=bt[bt.length-1];!r||!(e.flags&2)&&t>=Oi(r)?bt.push(e):bt.splice(L1(t),0,e),e.flags|=1,kl()}}function kl(){Ts||(Ts=Nl.then(Ml))}function M1(e){Ae(e)?Mn.push(...e):Gr&&e.id===-1?Gr.splice(On+1,0,e):e.flags&1||(Mn.push(e),e.flags|=1),kl()}function g0(e,t,r=Er+1){for(;r<bt.length;r++){const n=bt[r];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;bt.splice(r,1),r--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function Ll(e){if(Mn.length){const t=[...new Set(Mn)].sort((r,n)=>Oi(r)-Oi(n));if(Mn.length=0,Gr){Gr.push(...t);return}for(Gr=t,On=0;On<Gr.length;On++){const r=Gr[On];r.flags&4&&(r.flags&=-2),r.flags&8||r(),r.flags&=-2}Gr=null,On=0}}const Oi=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ml(e){try{for(Er=0;Er<bt.length;Er++){const t=bt[Er];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Wi(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Er<bt.length;Er++){const t=bt[Er];t&&(t.flags&=-2)}Er=-1,bt.length=0,Ll(),Ts=null,(bt.length||Mn.length)&&Ml()}}let Zt=null,Bl=null;function Ss(e){const t=Zt;return Zt=e,Bl=e&&e.type.__scopeId||null,t}function Io(e,t=Zt,r){if(!t||e._n)return e;const n=(...i)=>{n._d&&C0(-1);const s=Ss(t);let a;try{a=e(...i)}finally{Ss(s),n._d&&C0(1)}return a};return n._n=!0,n._c=!0,n._d=!0,n}function Za(e,t){if(Zt===null)return e;const r=na(Zt),n=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[s,a,o,f=Xe]=t[i];s&&(Ce(s)&&(s={mounted:s,updated:s}),s.deep&&Dr(a),n.push({dir:s,instance:r,value:a,oldValue:void 0,arg:o,modifiers:f}))}return e}function an(e,t,r,n){const i=e.dirs,s=t&&t.dirs;for(let a=0;a<i.length;a++){const o=i[a];s&&(o.oldValue=s[a].value);let f=o.dir[n];f&&(Lr(),yr(f,r,8,[e.el,o,e,t]),Mr())}}const B1=Symbol("_vte"),U1=e=>e.__isTeleport;function No(e,t){e.shapeFlag&6&&e.component?(e.transition=t,No(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Ul(e,t){return Ce(e)?Rt({name:e.name},t,{setup:e}):e}function Hl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function ys(e,t,r,n,i=!1){if(Ae(e)){e.forEach((x,p)=>ys(x,t&&(Ae(t)?t[p]:t),r,n,i));return}if(xi(n)&&!i){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&ys(e,t,r,n.component.subTree);return}const s=n.shapeFlag&4?na(n.component):n.el,a=i?null:s,{i:o,r:f}=e,l=t&&t.r,c=o.refs===Xe?o.refs={}:o.refs,u=o.setupState,h=Ue(u),d=u===Xe?()=>!1:x=>He(h,x);if(l!=null&&l!==f&&(at(l)?(c[l]=null,d(l)&&(u[l]=null)):Ft(l)&&(l.value=null)),Ce(f))Wi(f,o,12,[a,c]);else{const x=at(f),p=Ft(f);if(x||p){const m=()=>{if(e.f){const C=x?d(f)?u[f]:c[f]:f.value;i?Ae(C)&&To(C,s):Ae(C)?C.includes(s)||C.push(s):x?(c[f]=[s],d(f)&&(u[f]=c[f])):(f.value=[s],e.k&&(c[e.k]=f.value))}else x?(c[f]=a,d(f)&&(u[f]=a)):p&&(f.value=a,e.k&&(c[e.k]=a))};a?(m.id=-1,Kt(m,r)):m()}}}Ys().requestIdleCallback;Ys().cancelIdleCallback;const xi=e=>!!e.type.__asyncLoader,Wl=e=>e.type.__isKeepAlive;function H1(e,t){Vl(e,"a",t)}function W1(e,t){Vl(e,"da",t)}function Vl(e,t,r=yt){const n=e.__wdc||(e.__wdc=()=>{let i=r;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(ea(t,n,r),r){let i=r.parent;for(;i&&i.parent;)Wl(i.parent.vnode)&&V1(n,t,r,i),i=i.parent}}function V1(e,t,r,n){const i=ea(t,e,n,!0);jl(()=>{To(n[t],i)},r)}function ea(e,t,r=yt,n=!1){if(r){const i=r[e]||(r[e]=[]),s=t.__weh||(t.__weh=(...a)=>{Lr();const o=Vi(r),f=yr(t,r,e,a);return o(),Mr(),f});return n?i.unshift(s):i.push(s),s}}const Hr=e=>(t,r=yt)=>{(!Pi||e==="sp")&&ea(e,(...n)=>t(...n),r)},j1=Hr("bm"),$1=Hr("m"),G1=Hr("bu"),K1=Hr("u"),X1=Hr("bum"),jl=Hr("um"),z1=Hr("sp"),Y1=Hr("rtg"),q1=Hr("rtc");function J1(e,t=yt){ea("ec",e,t)}const Z1="components";function bo(e,t){return ed(Z1,e,!0,t)||e}const Q1=Symbol.for("v-ndc");function ed(e,t,r=!0,n=!1){const i=Zt||yt;if(i){const s=i.type;{const o=jd(s,!1);if(o&&(o===t||o===sr(t)||o===zs(sr(t))))return s}const a=v0(i[e]||s[e],t)||v0(i.appContext[e],t);return!a&&n?s:a}}function v0(e,t){return e&&(e[t]||e[sr(t)]||e[zs(sr(t))])}function td(e,t,r,n){let i;const s=r,a=Ae(e);if(a||at(e)){const o=a&&kn(e);let f=!1,l=!1;o&&(f=!ir(e),l=Jr(e),e=Js(e)),i=new Array(e.length);for(let c=0,u=e.length;c<u;c++)i[c]=t(f?l?Es(xt(e[c])):xt(e[c]):e[c],c,void 0,s)}else if(typeof e=="number"){i=new Array(e);for(let o=0;o<e;o++)i[o]=t(o+1,o,void 0,s)}else if(Je(e))if(e[Symbol.iterator])i=Array.from(e,(o,f)=>t(o,f,void 0,s));else{const o=Object.keys(e);i=new Array(o.length);for(let f=0,l=o.length;f<l;f++){const c=o[f];i[f]=t(e[c],c,f,s)}}else i=[];return i}const Qa=e=>e?cc(e)?na(e):Qa(e.parent):null,mi=Rt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Qa(e.parent),$root:e=>Qa(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Gl(e),$forceUpdate:e=>e.f||(e.f=()=>{Do(e.update)}),$nextTick:e=>e.n||(e.n=bl.bind(e.proxy)),$watch:e=>Td.bind(e)}),Ca=(e,t)=>e!==Xe&&!e.__isScriptSetup&&He(e,t),rd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:r,setupState:n,data:i,props:s,accessCache:a,type:o,appContext:f}=e;let l;if(t[0]!=="$"){const d=a[t];if(d!==void 0)switch(d){case 1:return n[t];case 2:return i[t];case 4:return r[t];case 3:return s[t]}else{if(Ca(n,t))return a[t]=1,n[t];if(i!==Xe&&He(i,t))return a[t]=2,i[t];if((l=e.propsOptions[0])&&He(l,t))return a[t]=3,s[t];if(r!==Xe&&He(r,t))return a[t]=4,r[t];eo&&(a[t]=0)}}const c=mi[t];let u,h;if(c)return t==="$attrs"&&St(e.attrs,"get",""),c(e);if((u=o.__cssModules)&&(u=u[t]))return u;if(r!==Xe&&He(r,t))return a[t]=4,r[t];if(h=f.config.globalProperties,He(h,t))return h[t]},set({_:e},t,r){const{data:n,setupState:i,ctx:s}=e;return Ca(i,t)?(i[t]=r,!0):n!==Xe&&He(n,t)?(n[t]=r,!0):He(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:n,appContext:i,propsOptions:s}},a){let o;return!!r[a]||e!==Xe&&He(e,a)||Ca(t,a)||(o=s[0])&&He(o,a)||He(n,a)||He(mi,a)||He(i.config.globalProperties,a)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:He(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}};function _0(e){return Ae(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}let eo=!0;function nd(e){const t=Gl(e),r=e.proxy,n=e.ctx;eo=!1,t.beforeCreate&&E0(t.beforeCreate,e,"bc");const{data:i,computed:s,methods:a,watch:o,provide:f,inject:l,created:c,beforeMount:u,mounted:h,beforeUpdate:d,updated:x,activated:p,deactivated:m,beforeDestroy:C,beforeUnmount:F,destroyed:y,unmounted:R,render:z,renderTracked:se,renderTriggered:D,errorCaptured:Y,serverPrefetch:M,expose:ee,inheritAttrs:re,components:ne,directives:de,filters:be}=t;if(l&&id(l,n,null),a)for(const Te in a){const Oe=a[Te];Ce(Oe)&&(n[Te]=Oe.bind(r))}if(i){const Te=i.call(r,r);Je(Te)&&(e.data=Zs(Te))}if(eo=!0,s)for(const Te in s){const Oe=s[Te],Ge=Ce(Oe)?Oe.bind(r,r):Ce(Oe.get)?Oe.get.bind(r,r):Sr,nt=!Ce(Oe)&&Ce(Oe.set)?Oe.set.bind(r):Sr,qe=lr({get:Ge,set:nt});Object.defineProperty(n,Te,{enumerable:!0,configurable:!0,get:()=>qe.value,set:O=>qe.value=O})}if(o)for(const Te in o)$l(o[Te],n,r,Te);if(f){const Te=Ce(f)?f.call(r):f;Reflect.ownKeys(Te).forEach(Oe=>{hs(Oe,Te[Oe])})}c&&E0(c,e,"c");function Ne(Te,Oe){Ae(Oe)?Oe.forEach(Ge=>Te(Ge.bind(r))):Oe&&Te(Oe.bind(r))}if(Ne(j1,u),Ne($1,h),Ne(G1,d),Ne(K1,x),Ne(H1,p),Ne(W1,m),Ne(J1,Y),Ne(q1,se),Ne(Y1,D),Ne(X1,F),Ne(jl,R),Ne(z1,M),Ae(ee))if(ee.length){const Te=e.exposed||(e.exposed={});ee.forEach(Oe=>{Object.defineProperty(Te,Oe,{get:()=>r[Oe],set:Ge=>r[Oe]=Ge})})}else e.exposed||(e.exposed={});z&&e.render===Sr&&(e.render=z),re!=null&&(e.inheritAttrs=re),ne&&(e.components=ne),de&&(e.directives=de),M&&Hl(e)}function id(e,t,r=Sr){Ae(e)&&(e=to(e));for(const n in e){const i=e[n];let s;Je(i)?"default"in i?s=Ir(i.from||n,i.default,!0):s=Ir(i.from||n):s=Ir(i),Ft(s)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>s.value,set:a=>s.value=a}):t[n]=s}}function E0(e,t,r){yr(Ae(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,r)}function $l(e,t,r,n){let i=n.includes(".")?ic(r,n):()=>r[n];if(at(e)){const s=t[e];Ce(s)&&ds(i,s)}else if(Ce(e))ds(i,e.bind(r));else if(Je(e))if(Ae(e))e.forEach(s=>$l(s,t,r,n));else{const s=Ce(e.handler)?e.handler.bind(r):t[e.handler];Ce(s)&&ds(i,s,e)}}function Gl(e){const t=e.type,{mixins:r,extends:n}=t,{mixins:i,optionsCache:s,config:{optionMergeStrategies:a}}=e.appContext,o=s.get(t);let f;return o?f=o:!i.length&&!r&&!n?f=t:(f={},i.length&&i.forEach(l=>As(f,l,a,!0)),As(f,t,a)),Je(t)&&s.set(t,f),f}function As(e,t,r,n=!1){const{mixins:i,extends:s}=t;s&&As(e,s,r,!0),i&&i.forEach(a=>As(e,a,r,!0));for(const a in t)if(!(n&&a==="expose")){const o=sd[a]||r&&r[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const sd={data:w0,props:T0,emits:T0,methods:ci,computed:ci,beforeCreate:Dt,created:Dt,beforeMount:Dt,mounted:Dt,beforeUpdate:Dt,updated:Dt,beforeDestroy:Dt,beforeUnmount:Dt,destroyed:Dt,unmounted:Dt,activated:Dt,deactivated:Dt,errorCaptured:Dt,serverPrefetch:Dt,components:ci,directives:ci,watch:od,provide:w0,inject:ad};function w0(e,t){return t?e?function(){return Rt(Ce(e)?e.call(this,this):e,Ce(t)?t.call(this,this):t)}:t:e}function ad(e,t){return ci(to(e),to(t))}function to(e){if(Ae(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function Dt(e,t){return e?[...new Set([].concat(e,t))]:t}function ci(e,t){return e?Rt(Object.create(null),e,t):t}function T0(e,t){return e?Ae(e)&&Ae(t)?[...new Set([...e,...t])]:Rt(Object.create(null),_0(e),_0(t??{})):t}function od(e,t){if(!e)return t;if(!t)return e;const r=Rt(Object.create(null),e);for(const n in t)r[n]=Dt(e[n],t[n]);return r}function Kl(){return{app:null,config:{isNativeTag:Yh,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let fd=0;function ld(e,t){return function(n,i=null){Ce(n)||(n=Rt({},n)),i!=null&&!Je(i)&&(i=null);const s=Kl(),a=new WeakSet,o=[];let f=!1;const l=s.app={_uid:fd++,_component:n,_props:i,_container:null,_context:s,_instance:null,version:Gd,get config(){return s.config},set config(c){},use(c,...u){return a.has(c)||(c&&Ce(c.install)?(a.add(c),c.install(l,...u)):Ce(c)&&(a.add(c),c(l,...u))),l},mixin(c){return s.mixins.includes(c)||s.mixins.push(c),l},component(c,u){return u?(s.components[c]=u,l):s.components[c]},directive(c,u){return u?(s.directives[c]=u,l):s.directives[c]},mount(c,u,h){if(!f){const d=l._ceVNode||vt(n,i);return d.appContext=s,h===!0?h="svg":h===!1&&(h=void 0),e(d,c,h),f=!0,l._container=c,c.__vue_app__=l,na(d.component)}},onUnmount(c){o.push(c)},unmount(){f&&(yr(o,l._instance,16),e(null,l._container),delete l._container.__vue_app__)},provide(c,u){return s.provides[c]=u,l},runWithContext(c){const u=Bn;Bn=l;try{return c()}finally{Bn=u}}};return l}}let Bn=null;function hs(e,t){if(yt){let r=yt.provides;const n=yt.parent&&yt.parent.provides;n===r&&(r=yt.provides=Object.create(n)),r[e]=t}}function Ir(e,t,r=!1){const n=yt||Zt;if(n||Bn){let i=Bn?Bn._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return r&&Ce(t)?t.call(n&&n.proxy):t}}const Xl={},zl=()=>Object.create(Xl),Yl=e=>Object.getPrototypeOf(e)===Xl;function cd(e,t,r,n=!1){const i={},s=zl();e.propsDefaults=Object.create(null),ql(e,t,i,s);for(const a in e.propsOptions[0])a in i||(i[a]=void 0);r?e.props=n?i:Rl(i):e.type.props?e.props=i:e.props=s,e.attrs=s}function ud(e,t,r,n){const{props:i,attrs:s,vnode:{patchFlag:a}}=e,o=Ue(i),[f]=e.propsOptions;let l=!1;if((n||a>0)&&!(a&16)){if(a&8){const c=e.vnode.dynamicProps;for(let u=0;u<c.length;u++){let h=c[u];if(ta(e.emitsOptions,h))continue;const d=t[h];if(f)if(He(s,h))d!==s[h]&&(s[h]=d,l=!0);else{const x=sr(h);i[x]=ro(f,o,x,d,e,!1)}else d!==s[h]&&(s[h]=d,l=!0)}}}else{ql(e,t,i,s)&&(l=!0);let c;for(const u in o)(!t||!He(t,u)&&((c=vn(u))===u||!He(t,c)))&&(f?r&&(r[u]!==void 0||r[c]!==void 0)&&(i[u]=ro(f,o,u,void 0,e,!0)):delete i[u]);if(s!==o)for(const u in s)(!t||!He(t,u))&&(delete s[u],l=!0)}l&&Pr(e.attrs,"set","")}function ql(e,t,r,n){const[i,s]=e.propsOptions;let a=!1,o;if(t)for(let f in t){if(hi(f))continue;const l=t[f];let c;i&&He(i,c=sr(f))?!s||!s.includes(c)?r[c]=l:(o||(o={}))[c]=l:ta(e.emitsOptions,f)||(!(f in n)||l!==n[f])&&(n[f]=l,a=!0)}if(s){const f=Ue(r),l=o||Xe;for(let c=0;c<s.length;c++){const u=s[c];r[u]=ro(i,f,u,l[u],e,!He(l,u))}}return a}function ro(e,t,r,n,i,s){const a=e[r];if(a!=null){const o=He(a,"default");if(o&&n===void 0){const f=a.default;if(a.type!==Function&&!a.skipFactory&&Ce(f)){const{propsDefaults:l}=i;if(r in l)n=l[r];else{const c=Vi(i);n=l[r]=f.call(null,t),c()}}else n=f;i.ce&&i.ce._setProp(r,n)}a[0]&&(s&&!o?n=!1:a[1]&&(n===""||n===vn(r))&&(n=!0))}return n}const hd=new WeakMap;function Jl(e,t,r=!1){const n=r?hd:t.propsCache,i=n.get(e);if(i)return i;const s=e.props,a={},o=[];let f=!1;if(!Ce(e)){const c=u=>{f=!0;const[h,d]=Jl(u,t,!0);Rt(a,h),d&&o.push(...d)};!r&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!s&&!f)return Je(e)&&n.set(e,Nn),Nn;if(Ae(s))for(let c=0;c<s.length;c++){const u=sr(s[c]);S0(u)&&(a[u]=Xe)}else if(s)for(const c in s){const u=sr(c);if(S0(u)){const h=s[c],d=a[u]=Ae(h)||Ce(h)?{type:h}:Rt({},h),x=d.type;let p=!1,m=!0;if(Ae(x))for(let C=0;C<x.length;++C){const F=x[C],y=Ce(F)&&F.name;if(y==="Boolean"){p=!0;break}else y==="String"&&(m=!1)}else p=Ce(x)&&x.name==="Boolean";d[0]=p,d[1]=m,(p||He(d,"default"))&&o.push(u)}}const l=[a,o];return Je(e)&&n.set(e,l),l}function S0(e){return e[0]!=="$"&&!hi(e)}const ko=e=>e[0]==="_"||e==="$stable",Lo=e=>Ae(e)?e.map(Tr):[Tr(e)],dd=(e,t,r)=>{if(t._n)return t;const n=Io((...i)=>Lo(t(...i)),r);return n._c=!1,n},Zl=(e,t,r)=>{const n=e._ctx;for(const i in e){if(ko(i))continue;const s=e[i];if(Ce(s))t[i]=dd(i,s,n);else if(s!=null){const a=Lo(s);t[i]=()=>a}}},Ql=(e,t)=>{const r=Lo(t);e.slots.default=()=>r},ec=(e,t,r)=>{for(const n in t)(r||!ko(n))&&(e[n]=t[n])},pd=(e,t,r)=>{const n=e.slots=zl();if(e.vnode.shapeFlag&32){const i=t._;i?(ec(n,t,r),r&&ul(n,"_",i,!0)):Zl(t,n)}else t&&Ql(e,t)},xd=(e,t,r)=>{const{vnode:n,slots:i}=e;let s=!0,a=Xe;if(n.shapeFlag&32){const o=t._;o?r&&o===1?s=!1:ec(i,t,r):(s=!t.$stable,Zl(t,i)),a=t}else t&&(Ql(e,t),a={default:1});if(s)for(const o in i)!ko(o)&&a[o]==null&&delete i[o]},Kt=Rd;function md(e){return gd(e)}function gd(e,t){const r=Ys();r.__VUE__=!0;const{insert:n,remove:i,patchProp:s,createElement:a,createText:o,createComment:f,setText:l,setElementText:c,parentNode:u,nextSibling:h,setScopeId:d=Sr,insertStaticContent:x}=e,p=(E,A,P,J=null,K=null,b=null,ce=void 0,oe=null,fe=!!A.dynamicChildren)=>{if(E===A)return;E&&!si(E,A)&&(J=k(E),O(E,K,b,!0),E=null),A.patchFlag===-2&&(fe=!1,A.dynamicChildren=null);const{type:ie,ref:Ee,shapeFlag:xe}=A;switch(ie){case ra:m(E,A,P,J);break;case Zr:C(E,A,P,J);break;case ps:E==null&&F(A,P,J,ce);break;case wr:ne(E,A,P,J,K,b,ce,oe,fe);break;default:xe&1?z(E,A,P,J,K,b,ce,oe,fe):xe&6?de(E,A,P,J,K,b,ce,oe,fe):(xe&64||xe&128)&&ie.process(E,A,P,J,K,b,ce,oe,fe,G)}Ee!=null&&K&&ys(Ee,E&&E.ref,b,A||E,!A)},m=(E,A,P,J)=>{if(E==null)n(A.el=o(A.children),P,J);else{const K=A.el=E.el;A.children!==E.children&&l(K,A.children)}},C=(E,A,P,J)=>{E==null?n(A.el=f(A.children||""),P,J):A.el=E.el},F=(E,A,P,J)=>{[E.el,E.anchor]=x(E.children,A,P,J,E.el,E.anchor)},y=({el:E,anchor:A},P,J)=>{let K;for(;E&&E!==A;)K=h(E),n(E,P,J),E=K;n(A,P,J)},R=({el:E,anchor:A})=>{let P;for(;E&&E!==A;)P=h(E),i(E),E=P;i(A)},z=(E,A,P,J,K,b,ce,oe,fe)=>{A.type==="svg"?ce="svg":A.type==="math"&&(ce="mathml"),E==null?se(A,P,J,K,b,ce,oe,fe):M(E,A,K,b,ce,oe,fe)},se=(E,A,P,J,K,b,ce,oe)=>{let fe,ie;const{props:Ee,shapeFlag:xe,transition:ve,dirs:Se}=E;if(fe=E.el=a(E.type,b,Ee&&Ee.is,Ee),xe&8?c(fe,E.children):xe&16&&Y(E.children,fe,null,J,K,Oa(E,b),ce,oe),Se&&an(E,null,J,"created"),D(fe,E,E.scopeId,ce,J),Ee){for(const Me in Ee)Me!=="value"&&!hi(Me)&&s(fe,Me,null,Ee[Me],b,J);"value"in Ee&&s(fe,"value",null,Ee.value,b),(ie=Ee.onVnodeBeforeMount)&&mr(ie,J,E)}Se&&an(E,null,J,"beforeMount");const De=vd(K,ve);De&&ve.beforeEnter(fe),n(fe,A,P),((ie=Ee&&Ee.onVnodeMounted)||De||Se)&&Kt(()=>{ie&&mr(ie,J,E),De&&ve.enter(fe),Se&&an(E,null,J,"mounted")},K)},D=(E,A,P,J,K)=>{if(P&&d(E,P),J)for(let b=0;b<J.length;b++)d(E,J[b]);if(K){let b=K.subTree;if(A===b||ac(b.type)&&(b.ssContent===A||b.ssFallback===A)){const ce=K.vnode;D(E,ce,ce.scopeId,ce.slotScopeIds,K.parent)}}},Y=(E,A,P,J,K,b,ce,oe,fe=0)=>{for(let ie=fe;ie<E.length;ie++){const Ee=E[ie]=oe?Kr(E[ie]):Tr(E[ie]);p(null,Ee,A,P,J,K,b,ce,oe)}},M=(E,A,P,J,K,b,ce)=>{const oe=A.el=E.el;let{patchFlag:fe,dynamicChildren:ie,dirs:Ee}=A;fe|=E.patchFlag&16;const xe=E.props||Xe,ve=A.props||Xe;let Se;if(P&&on(P,!1),(Se=ve.onVnodeBeforeUpdate)&&mr(Se,P,A,E),Ee&&an(A,E,P,"beforeUpdate"),P&&on(P,!0),(xe.innerHTML&&ve.innerHTML==null||xe.textContent&&ve.textContent==null)&&c(oe,""),ie?ee(E.dynamicChildren,ie,oe,P,J,Oa(A,K),b):ce||Oe(E,A,oe,null,P,J,Oa(A,K),b,!1),fe>0){if(fe&16)re(oe,xe,ve,P,K);else if(fe&2&&xe.class!==ve.class&&s(oe,"class",null,ve.class,K),fe&4&&s(oe,"style",xe.style,ve.style,K),fe&8){const De=A.dynamicProps;for(let Me=0;Me<De.length;Me++){const Ie=De[Me],Pt=xe[Ie],ht=ve[Ie];(ht!==Pt||Ie==="value")&&s(oe,Ie,Pt,ht,K,P)}}fe&1&&E.children!==A.children&&c(oe,A.children)}else!ce&&ie==null&&re(oe,xe,ve,P,K);((Se=ve.onVnodeUpdated)||Ee)&&Kt(()=>{Se&&mr(Se,P,A,E),Ee&&an(A,E,P,"updated")},J)},ee=(E,A,P,J,K,b,ce)=>{for(let oe=0;oe<A.length;oe++){const fe=E[oe],ie=A[oe],Ee=fe.el&&(fe.type===wr||!si(fe,ie)||fe.shapeFlag&198)?u(fe.el):P;p(fe,ie,Ee,null,J,K,b,ce,!0)}},re=(E,A,P,J,K)=>{if(A!==P){if(A!==Xe)for(const b in A)!hi(b)&&!(b in P)&&s(E,b,A[b],null,K,J);for(const b in P){if(hi(b))continue;const ce=P[b],oe=A[b];ce!==oe&&b!=="value"&&s(E,b,oe,ce,K,J)}"value"in P&&s(E,"value",A.value,P.value,K)}},ne=(E,A,P,J,K,b,ce,oe,fe)=>{const ie=A.el=E?E.el:o(""),Ee=A.anchor=E?E.anchor:o("");let{patchFlag:xe,dynamicChildren:ve,slotScopeIds:Se}=A;Se&&(oe=oe?oe.concat(Se):Se),E==null?(n(ie,P,J),n(Ee,P,J),Y(A.children||[],P,Ee,K,b,ce,oe,fe)):xe>0&&xe&64&&ve&&E.dynamicChildren?(ee(E.dynamicChildren,ve,P,K,b,ce,oe),(A.key!=null||K&&A===K.subTree)&&tc(E,A,!0)):Oe(E,A,P,Ee,K,b,ce,oe,fe)},de=(E,A,P,J,K,b,ce,oe,fe)=>{A.slotScopeIds=oe,E==null?A.shapeFlag&512?K.ctx.activate(A,P,J,ce,fe):be(A,P,J,K,b,ce,fe):ye(E,A,fe)},be=(E,A,P,J,K,b,ce)=>{const oe=E.component=Bd(E,J,K);if(Wl(E)&&(oe.ctx.renderer=G),Ud(oe,!1,ce),oe.asyncDep){if(K&&K.registerDep(oe,Ne,ce),!E.el){const fe=oe.subTree=vt(Zr);C(null,fe,A,P)}}else Ne(oe,E,A,P,K,b,ce)},ye=(E,A,P)=>{const J=A.component=E.component;if(Cd(E,A,P))if(J.asyncDep&&!J.asyncResolved){Te(J,A,P);return}else J.next=A,J.update();else A.el=E.el,J.vnode=A},Ne=(E,A,P,J,K,b,ce)=>{const oe=()=>{if(E.isMounted){let{next:xe,bu:ve,u:Se,parent:De,vnode:Me}=E;{const Gt=rc(E);if(Gt){xe&&(xe.el=Me.el,Te(E,xe,ce)),Gt.asyncDep.then(()=>{E.isUnmounted||oe()});return}}let Ie=xe,Pt;on(E,!1),xe?(xe.el=Me.el,Te(E,xe,ce)):xe=Me,ve&&us(ve),(Pt=xe.props&&xe.props.onVnodeBeforeUpdate)&&mr(Pt,De,xe,Me),on(E,!0);const ht=A0(E),$t=E.subTree;E.subTree=ht,p($t,ht,u($t.el),k($t),E,K,b),xe.el=ht.el,Ie===null&&Od(E,ht.el),Se&&Kt(Se,K),(Pt=xe.props&&xe.props.onVnodeUpdated)&&Kt(()=>mr(Pt,De,xe,Me),K)}else{let xe;const{el:ve,props:Se}=A,{bm:De,m:Me,parent:Ie,root:Pt,type:ht}=E,$t=xi(A);on(E,!1),De&&us(De),!$t&&(xe=Se&&Se.onVnodeBeforeMount)&&mr(xe,Ie,A),on(E,!0);{Pt.ce&&Pt.ce._injectChildStyle(ht);const Gt=E.subTree=A0(E);p(null,Gt,P,J,E,K,b),A.el=Gt.el}if(Me&&Kt(Me,K),!$t&&(xe=Se&&Se.onVnodeMounted)){const Gt=A;Kt(()=>mr(xe,Ie,Gt),K)}(A.shapeFlag&256||Ie&&xi(Ie.vnode)&&Ie.vnode.shapeFlag&256)&&E.a&&Kt(E.a,K),E.isMounted=!0,A=P=J=null}};E.scope.on();const fe=E.effect=new xl(oe);E.scope.off();const ie=E.update=fe.run.bind(fe),Ee=E.job=fe.runIfDirty.bind(fe);Ee.i=E,Ee.id=E.uid,fe.scheduler=()=>Do(Ee),on(E,!0),ie()},Te=(E,A,P)=>{A.component=E;const J=E.vnode.props;E.vnode=A,E.next=null,ud(E,A.props,J,P),xd(E,A.children,P),Lr(),g0(E),Mr()},Oe=(E,A,P,J,K,b,ce,oe,fe=!1)=>{const ie=E&&E.children,Ee=E?E.shapeFlag:0,xe=A.children,{patchFlag:ve,shapeFlag:Se}=A;if(ve>0){if(ve&128){nt(ie,xe,P,J,K,b,ce,oe,fe);return}else if(ve&256){Ge(ie,xe,P,J,K,b,ce,oe,fe);return}}Se&8?(Ee&16&&Q(ie,K,b),xe!==ie&&c(P,xe)):Ee&16?Se&16?nt(ie,xe,P,J,K,b,ce,oe,fe):Q(ie,K,b,!0):(Ee&8&&c(P,""),Se&16&&Y(xe,P,J,K,b,ce,oe,fe))},Ge=(E,A,P,J,K,b,ce,oe,fe)=>{E=E||Nn,A=A||Nn;const ie=E.length,Ee=A.length,xe=Math.min(ie,Ee);let ve;for(ve=0;ve<xe;ve++){const Se=A[ve]=fe?Kr(A[ve]):Tr(A[ve]);p(E[ve],Se,P,null,K,b,ce,oe,fe)}ie>Ee?Q(E,K,b,!0,!1,xe):Y(A,P,J,K,b,ce,oe,fe,xe)},nt=(E,A,P,J,K,b,ce,oe,fe)=>{let ie=0;const Ee=A.length;let xe=E.length-1,ve=Ee-1;for(;ie<=xe&&ie<=ve;){const Se=E[ie],De=A[ie]=fe?Kr(A[ie]):Tr(A[ie]);if(si(Se,De))p(Se,De,P,null,K,b,ce,oe,fe);else break;ie++}for(;ie<=xe&&ie<=ve;){const Se=E[xe],De=A[ve]=fe?Kr(A[ve]):Tr(A[ve]);if(si(Se,De))p(Se,De,P,null,K,b,ce,oe,fe);else break;xe--,ve--}if(ie>xe){if(ie<=ve){const Se=ve+1,De=Se<Ee?A[Se].el:J;for(;ie<=ve;)p(null,A[ie]=fe?Kr(A[ie]):Tr(A[ie]),P,De,K,b,ce,oe,fe),ie++}}else if(ie>ve)for(;ie<=xe;)O(E[ie],K,b,!0),ie++;else{const Se=ie,De=ie,Me=new Map;for(ie=De;ie<=ve;ie++){const wt=A[ie]=fe?Kr(A[ie]):Tr(A[ie]);wt.key!=null&&Me.set(wt.key,ie)}let Ie,Pt=0;const ht=ve-De+1;let $t=!1,Gt=0;const Vr=new Array(ht);for(ie=0;ie<ht;ie++)Vr[ie]=0;for(ie=Se;ie<=xe;ie++){const wt=E[ie];if(Pt>=ht){O(wt,K,b,!0);continue}let tr;if(wt.key!=null)tr=Me.get(wt.key);else for(Ie=De;Ie<=ve;Ie++)if(Vr[Ie-De]===0&&si(wt,A[Ie])){tr=Ie;break}tr===void 0?O(wt,K,b,!0):(Vr[tr-De]=ie+1,tr>=Gt?Gt=tr:$t=!0,p(wt,A[tr],P,null,K,b,ce,oe,fe),Pt++)}const ei=$t?_d(Vr):Nn;for(Ie=ei.length-1,ie=ht-1;ie>=0;ie--){const wt=De+ie,tr=A[wt],Zi=wt+1<Ee?A[wt+1].el:J;Vr[ie]===0?p(null,tr,P,Zi,K,b,ce,oe,fe):$t&&(Ie<0||ie!==ei[Ie]?qe(tr,P,Zi,2):Ie--)}}},qe=(E,A,P,J,K=null)=>{const{el:b,type:ce,transition:oe,children:fe,shapeFlag:ie}=E;if(ie&6){qe(E.component.subTree,A,P,J);return}if(ie&128){E.suspense.move(A,P,J);return}if(ie&64){ce.move(E,A,P,G);return}if(ce===wr){n(b,A,P);for(let xe=0;xe<fe.length;xe++)qe(fe[xe],A,P,J);n(E.anchor,A,P);return}if(ce===ps){y(E,A,P);return}if(J!==2&&ie&1&&oe)if(J===0)oe.beforeEnter(b),n(b,A,P),Kt(()=>oe.enter(b),K);else{const{leave:xe,delayLeave:ve,afterLeave:Se}=oe,De=()=>{E.ctx.isUnmounted?i(b):n(b,A,P)},Me=()=>{xe(b,()=>{De(),Se&&Se()})};ve?ve(b,De,Me):Me()}else n(b,A,P)},O=(E,A,P,J=!1,K=!1)=>{const{type:b,props:ce,ref:oe,children:fe,dynamicChildren:ie,shapeFlag:Ee,patchFlag:xe,dirs:ve,cacheIndex:Se}=E;if(xe===-2&&(K=!1),oe!=null&&(Lr(),ys(oe,null,P,E,!0),Mr()),Se!=null&&(A.renderCache[Se]=void 0),Ee&256){A.ctx.deactivate(E);return}const De=Ee&1&&ve,Me=!xi(E);let Ie;if(Me&&(Ie=ce&&ce.onVnodeBeforeUnmount)&&mr(Ie,A,E),Ee&6)I(E.component,P,J);else{if(Ee&128){E.suspense.unmount(P,J);return}De&&an(E,null,A,"beforeUnmount"),Ee&64?E.type.remove(E,A,P,G,J):ie&&!ie.hasOnce&&(b!==wr||xe>0&&xe&64)?Q(ie,A,P,!1,!0):(b===wr&&xe&384||!K&&Ee&16)&&Q(fe,A,P),J&&H(E)}(Me&&(Ie=ce&&ce.onVnodeUnmounted)||De)&&Kt(()=>{Ie&&mr(Ie,A,E),De&&an(E,null,A,"unmounted")},P)},H=E=>{const{type:A,el:P,anchor:J,transition:K}=E;if(A===wr){N(P,J);return}if(A===ps){R(E);return}const b=()=>{i(P),K&&!K.persisted&&K.afterLeave&&K.afterLeave()};if(E.shapeFlag&1&&K&&!K.persisted){const{leave:ce,delayLeave:oe}=K,fe=()=>ce(P,b);oe?oe(E.el,b,fe):fe()}else b()},N=(E,A)=>{let P;for(;E!==A;)P=h(E),i(E),E=P;i(A)},I=(E,A,P)=>{const{bum:J,scope:K,job:b,subTree:ce,um:oe,m:fe,a:ie,parent:Ee,slots:{__:xe}}=E;y0(fe),y0(ie),J&&us(J),Ee&&Ae(xe)&&xe.forEach(ve=>{Ee.renderCache[ve]=void 0}),K.stop(),b&&(b.flags|=8,O(ce,E,A,P)),oe&&Kt(oe,A),Kt(()=>{E.isUnmounted=!0},A),A&&A.pendingBranch&&!A.isUnmounted&&E.asyncDep&&!E.asyncResolved&&E.suspenseId===A.pendingId&&(A.deps--,A.deps===0&&A.resolve())},Q=(E,A,P,J=!1,K=!1,b=0)=>{for(let ce=b;ce<E.length;ce++)O(E[ce],A,P,J,K)},k=E=>{if(E.shapeFlag&6)return k(E.component.subTree);if(E.shapeFlag&128)return E.suspense.next();const A=h(E.anchor||E.el),P=A&&A[B1];return P?h(P):A};let q=!1;const j=(E,A,P)=>{E==null?A._vnode&&O(A._vnode,null,null,!0):p(A._vnode||null,E,A,null,null,null,P),A._vnode=E,q||(q=!0,g0(),Ll(),q=!1)},G={p,um:O,m:qe,r:H,mt:be,mc:Y,pc:Oe,pbc:ee,n:k,o:e};return{render:j,hydrate:void 0,createApp:ld(j)}}function Oa({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function on({effect:e,job:t},r){r?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function vd(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function tc(e,t,r=!1){const n=e.children,i=t.children;if(Ae(n)&&Ae(i))for(let s=0;s<n.length;s++){const a=n[s];let o=i[s];o.shapeFlag&1&&!o.dynamicChildren&&((o.patchFlag<=0||o.patchFlag===32)&&(o=i[s]=Kr(i[s]),o.el=a.el),!r&&o.patchFlag!==-2&&tc(a,o)),o.type===ra&&(o.el=a.el),o.type===Zr&&!o.el&&(o.el=a.el)}}function _d(e){const t=e.slice(),r=[0];let n,i,s,a,o;const f=e.length;for(n=0;n<f;n++){const l=e[n];if(l!==0){if(i=r[r.length-1],e[i]<l){t[n]=i,r.push(n);continue}for(s=0,a=r.length-1;s<a;)o=s+a>>1,e[r[o]]<l?s=o+1:a=o;l<e[r[s]]&&(s>0&&(t[n]=r[s-1]),r[s]=n)}}for(s=r.length,a=r[s-1];s-- >0;)r[s]=a,a=t[a];return r}function rc(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:rc(t)}function y0(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Ed=Symbol.for("v-scx"),wd=()=>Ir(Ed);function ds(e,t,r){return nc(e,t,r)}function nc(e,t,r=Xe){const{immediate:n,deep:i,flush:s,once:a}=r,o=Rt({},r),f=t&&n||!t&&s!=="post";let l;if(Pi){if(s==="sync"){const d=wd();l=d.__watcherHandles||(d.__watcherHandles=[])}else if(!f){const d=()=>{};return d.stop=Sr,d.resume=Sr,d.pause=Sr,d}}const c=yt;o.call=(d,x,p)=>yr(d,c,x,p);let u=!1;s==="post"?o.scheduler=d=>{Kt(d,c&&c.suspense)}:s!=="sync"&&(u=!0,o.scheduler=(d,x)=>{x?d():Do(d)}),o.augmentJob=d=>{t&&(d.flags|=4),u&&(d.flags|=2,c&&(d.id=c.uid,d.i=c))};const h=b1(e,t,o);return Pi&&(l?l.push(h):f&&h()),h}function Td(e,t,r){const n=this.proxy,i=at(e)?e.includes(".")?ic(n,e):()=>n[e]:e.bind(n,n);let s;Ce(t)?s=t:(s=t.handler,r=t);const a=Vi(this),o=nc(i,s.bind(n),r);return a(),o}function ic(e,t){const r=t.split(".");return()=>{let n=e;for(let i=0;i<r.length&&n;i++)n=n[r[i]];return n}}const Sd=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${sr(t)}Modifiers`]||e[`${vn(t)}Modifiers`];function yd(e,t,...r){if(e.isUnmounted)return;const n=e.vnode.props||Xe;let i=r;const s=t.startsWith("update:"),a=s&&Sd(n,t.slice(7));a&&(a.trim&&(i=r.map(c=>at(c)?c.trim():c)),a.number&&(i=r.map(Xa)));let o,f=n[o=Ta(t)]||n[o=Ta(sr(t))];!f&&s&&(f=n[o=Ta(vn(t))]),f&&yr(f,e,6,i);const l=n[o+"Once"];if(l){if(!e.emitted)e.emitted={};else if(e.emitted[o])return;e.emitted[o]=!0,yr(l,e,6,i)}}function sc(e,t,r=!1){const n=t.emitsCache,i=n.get(e);if(i!==void 0)return i;const s=e.emits;let a={},o=!1;if(!Ce(e)){const f=l=>{const c=sc(l,t,!0);c&&(o=!0,Rt(a,c))};!r&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!s&&!o?(Je(e)&&n.set(e,null),null):(Ae(s)?s.forEach(f=>a[f]=null):Rt(a,s),Je(e)&&n.set(e,a),a)}function ta(e,t){return!e||!Gs(t)?!1:(t=t.slice(2).replace(/Once$/,""),He(e,t[0].toLowerCase()+t.slice(1))||He(e,vn(t))||He(e,t))}function A0(e){const{type:t,vnode:r,proxy:n,withProxy:i,propsOptions:[s],slots:a,attrs:o,emit:f,render:l,renderCache:c,props:u,data:h,setupState:d,ctx:x,inheritAttrs:p}=e,m=Ss(e);let C,F;try{if(r.shapeFlag&4){const R=i||n,z=R;C=Tr(l.call(z,R,c,u,d,h,x)),F=o}else{const R=t;C=Tr(R.length>1?R(u,{attrs:o,slots:a,emit:f}):R(u,null)),F=t.props?o:Ad(o)}}catch(R){gi.length=0,Qs(R,e,1),C=vt(Zr)}let y=C;if(F&&p!==!1){const R=Object.keys(F),{shapeFlag:z}=y;R.length&&z&7&&(s&&R.some(wo)&&(F=Fd(F,s)),y=$n(y,F,!1,!0))}return r.dirs&&(y=$n(y,null,!1,!0),y.dirs=y.dirs?y.dirs.concat(r.dirs):r.dirs),r.transition&&No(y,r.transition),C=y,Ss(m),C}const Ad=e=>{let t;for(const r in e)(r==="class"||r==="style"||Gs(r))&&((t||(t={}))[r]=e[r]);return t},Fd=(e,t)=>{const r={};for(const n in e)(!wo(n)||!(n.slice(9)in t))&&(r[n]=e[n]);return r};function Cd(e,t,r){const{props:n,children:i,component:s}=e,{props:a,children:o,patchFlag:f}=t,l=s.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&f>=0){if(f&1024)return!0;if(f&16)return n?F0(n,a,l):!!a;if(f&8){const c=t.dynamicProps;for(let u=0;u<c.length;u++){const h=c[u];if(a[h]!==n[h]&&!ta(l,h))return!0}}}else return(i||o)&&(!o||!o.$stable)?!0:n===a?!1:n?a?F0(n,a,l):!0:!!a;return!1}function F0(e,t,r){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let i=0;i<n.length;i++){const s=n[i];if(t[s]!==e[s]&&!ta(r,s))return!0}return!1}function Od({vnode:e,parent:t},r){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=r,t=t.parent;else break}}const ac=e=>e.__isSuspense;function Rd(e,t){t&&t.pendingBranch?Ae(e)?t.effects.push(...e):t.effects.push(e):M1(e)}const wr=Symbol.for("v-fgt"),ra=Symbol.for("v-txt"),Zr=Symbol.for("v-cmt"),ps=Symbol.for("v-stc"),gi=[];let Qt=null;function Bt(e=!1){gi.push(Qt=e?null:[])}function Pd(){gi.pop(),Qt=gi[gi.length-1]||null}let Ri=1;function C0(e,t=!1){Ri+=e,e<0&&Qt&&t&&(Qt.hasOnce=!0)}function oc(e){return e.dynamicChildren=Ri>0?Qt||Nn:null,Pd(),Ri>0&&Qt&&Qt.push(e),e}function zt(e,t,r,n,i,s){return oc(we(e,t,r,n,i,s,!0))}function Dd(e,t,r,n,i){return oc(vt(e,t,r,n,i,!0))}function Fs(e){return e?e.__v_isVNode===!0:!1}function si(e,t){return e.type===t.type&&e.key===t.key}const fc=({key:e})=>e??null,xs=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?at(e)||Ft(e)||Ce(e)?{i:Zt,r:e,k:t,f:!!r}:e:null);function we(e,t=null,r=null,n=0,i=null,s=e===wr?0:1,a=!1,o=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&fc(t),ref:t&&xs(t),scopeId:Bl,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:n,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Zt};return o?(Bo(f,r),s&128&&e.normalize(f)):r&&(f.shapeFlag|=at(r)?8:16),Ri>0&&!a&&Qt&&(f.patchFlag>0||s&6)&&f.patchFlag!==32&&Qt.push(f),f}const vt=Id;function Id(e,t=null,r=null,n=0,i=null,s=!1){if((!e||e===Q1)&&(e=Zr),Fs(e)){const o=$n(e,t,!0);return r&&Bo(o,r),Ri>0&&!s&&Qt&&(o.shapeFlag&6?Qt[Qt.indexOf(e)]=o:Qt.push(o)),o.patchFlag=-2,o}if($d(e)&&(e=e.__vccOpts),t){t=Nd(t);let{class:o,style:f}=t;o&&!at(o)&&(t.class=jn(o)),Je(f)&&(Po(f)&&!Ae(f)&&(f=Rt({},f)),t.style=qs(f))}const a=at(e)?1:ac(e)?128:U1(e)?64:Je(e)?4:Ce(e)?2:0;return we(e,t,r,n,i,a,s,!0)}function Nd(e){return e?Po(e)||Yl(e)?Rt({},e):e:null}function $n(e,t,r=!1,n=!1){const{props:i,ref:s,patchFlag:a,children:o,transition:f}=e,l=t?kd(i||{},t):i,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&fc(l),ref:t&&t.ref?r&&s?Ae(s)?s.concat(xs(t)):[s,xs(t)]:xs(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==wr?a===-1?16:a|16:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&$n(e.ssContent),ssFallback:e.ssFallback&&$n(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&n&&No(c,f.clone(c)),c}function Mo(e=" ",t=0){return vt(ra,null,e,t)}function bd(e,t){const r=vt(ps,null,e);return r.staticCount=t,r}function lc(e="",t=!1){return t?(Bt(),Dd(Zr,null,e)):vt(Zr,null,e)}function Tr(e){return e==null||typeof e=="boolean"?vt(Zr):Ae(e)?vt(wr,null,e.slice()):Fs(e)?Kr(e):vt(ra,null,String(e))}function Kr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:$n(e)}function Bo(e,t){let r=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(Ae(t))r=16;else if(typeof t=="object")if(n&65){const i=t.default;i&&(i._c&&(i._d=!1),Bo(e,i()),i._c&&(i._d=!0));return}else{r=32;const i=t._;!i&&!Yl(t)?t._ctx=Zt:i===3&&Zt&&(Zt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Ce(t)?(t={default:t,_ctx:Zt},r=32):(t=String(t),n&64?(r=16,t=[Mo(t)]):r=8);e.children=t,e.shapeFlag|=r}function kd(...e){const t={};for(let r=0;r<e.length;r++){const n=e[r];for(const i in n)if(i==="class")t.class!==n.class&&(t.class=jn([t.class,n.class]));else if(i==="style")t.style=qs([t.style,n.style]);else if(Gs(i)){const s=t[i],a=n[i];a&&s!==a&&!(Ae(s)&&s.includes(a))&&(t[i]=s?[].concat(s,a):a)}else i!==""&&(t[i]=n[i])}return t}function mr(e,t,r,n=null){yr(e,t,7,[r,n])}const Ld=Kl();let Md=0;function Bd(e,t,r){const n=e.type,i=(t?t.appContext:e.appContext)||Ld,s={uid:Md++,vnode:e,type:n,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new a1(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Jl(n,i),emitsOptions:sc(n,i),emit:null,emitted:null,propsDefaults:Xe,inheritAttrs:n.inheritAttrs,ctx:Xe,data:Xe,props:Xe,attrs:Xe,slots:Xe,refs:Xe,setupState:Xe,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=yd.bind(null,s),e.ce&&e.ce(s),s}let yt=null,Cs,no;{const e=Ys(),t=(r,n)=>{let i;return(i=e[r])||(i=e[r]=[]),i.push(n),s=>{i.length>1?i.forEach(a=>a(s)):i[0](s)}};Cs=t("__VUE_INSTANCE_SETTERS__",r=>yt=r),no=t("__VUE_SSR_SETTERS__",r=>Pi=r)}const Vi=e=>{const t=yt;return Cs(e),e.scope.on(),()=>{e.scope.off(),Cs(t)}},O0=()=>{yt&&yt.scope.off(),Cs(null)};function cc(e){return e.vnode.shapeFlag&4}let Pi=!1;function Ud(e,t=!1,r=!1){t&&no(t);const{props:n,children:i}=e.vnode,s=cc(e);cd(e,n,s,t),pd(e,i,r||t);const a=s?Hd(e,t):void 0;return t&&no(!1),a}function Hd(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,rd);const{setup:n}=r;if(n){Lr();const i=e.setupContext=n.length>1?Vd(e):null,s=Vi(e),a=Wi(n,e,0,[e.props,i]),o=fl(a);if(Mr(),s(),(o||e.sp)&&!xi(e)&&Hl(e),o){if(a.then(O0,O0),t)return a.then(f=>{R0(e,f)}).catch(f=>{Qs(f,e,0)});e.asyncDep=a}else R0(e,a)}else uc(e)}function R0(e,t,r){Ce(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Je(t)&&(e.setupState=Il(t)),uc(e)}function uc(e,t,r){const n=e.type;e.render||(e.render=n.render||Sr);{const i=Vi(e);Lr();try{nd(e)}finally{Mr(),i()}}}const Wd={get(e,t){return St(e,"get",""),e[t]}};function Vd(e){const t=r=>{e.exposed=r||{}};return{attrs:new Proxy(e.attrs,Wd),slots:e.slots,emit:e.emit,expose:t}}function na(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Il(F1(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in mi)return mi[r](e)},has(t,r){return r in t||r in mi}})):e.proxy}function jd(e,t=!0){return Ce(e)?e.displayName||e.name:e.name||t&&e.__name}function $d(e){return Ce(e)&&"__vccOpts"in e}const lr=(e,t)=>I1(e,t,Pi);function hc(e,t,r){const n=arguments.length;return n===2?Je(t)&&!Ae(t)?Fs(t)?vt(e,null,[t]):vt(e,t):vt(e,null,t):(n>3?r=Array.prototype.slice.call(arguments,2):n===3&&Fs(r)&&(r=[r]),vt(e,t,r))}const Gd="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let io;const P0=typeof window<"u"&&window.trustedTypes;if(P0)try{io=P0.createPolicy("vue",{createHTML:e=>e})}catch{}const dc=io?e=>io.createHTML(e):e=>e,Kd="http://www.w3.org/2000/svg",Xd="http://www.w3.org/1998/Math/MathML",Rr=typeof document<"u"?document:null,D0=Rr&&Rr.createElement("template"),zd={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,n)=>{const i=t==="svg"?Rr.createElementNS(Kd,e):t==="mathml"?Rr.createElementNS(Xd,e):r?Rr.createElement(e,{is:r}):Rr.createElement(e);return e==="select"&&n&&n.multiple!=null&&i.setAttribute("multiple",n.multiple),i},createText:e=>Rr.createTextNode(e),createComment:e=>Rr.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Rr.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,n,i,s){const a=r?r.previousSibling:t.lastChild;if(i&&(i===s||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),r),!(i===s||!(i=i.nextSibling)););else{D0.innerHTML=dc(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const o=D0.content;if(n==="svg"||n==="mathml"){const f=o.firstChild;for(;f.firstChild;)o.appendChild(f.firstChild);o.removeChild(f)}t.insertBefore(o,r)}return[a?a.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},Yd=Symbol("_vtc");function qd(e,t,r){const n=e[Yd];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const I0=Symbol("_vod"),Jd=Symbol("_vsh"),Zd=Symbol(""),Qd=/(^|;)\s*display\s*:/;function ep(e,t,r){const n=e.style,i=at(r);let s=!1;if(r&&!i){if(t)if(at(t))for(const a of t.split(";")){const o=a.slice(0,a.indexOf(":")).trim();r[o]==null&&ms(n,o,"")}else for(const a in t)r[a]==null&&ms(n,a,"");for(const a in r)a==="display"&&(s=!0),ms(n,a,r[a])}else if(i){if(t!==r){const a=n[Zd];a&&(r+=";"+a),n.cssText=r,s=Qd.test(r)}}else t&&e.removeAttribute("style");I0 in e&&(e[I0]=s?n.display:"",e[Jd]&&(n.display="none"))}const N0=/\s*!important$/;function ms(e,t,r){if(Ae(r))r.forEach(n=>ms(e,t,n));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const n=tp(e,t);N0.test(r)?e.setProperty(vn(n),r.replace(N0,""),"important"):e[n]=r}}const b0=["Webkit","Moz","ms"],Ra={};function tp(e,t){const r=Ra[t];if(r)return r;let n=sr(t);if(n!=="filter"&&n in e)return Ra[t]=n;n=zs(n);for(let i=0;i<b0.length;i++){const s=b0[i]+n;if(s in e)return Ra[t]=s}return t}const k0="http://www.w3.org/1999/xlink";function L0(e,t,r,n,i,s=s1(t)){n&&t.startsWith("xlink:")?r==null?e.removeAttributeNS(k0,t.slice(6,t.length)):e.setAttributeNS(k0,t,r):r==null||s&&!hl(r)?e.removeAttribute(t):e.setAttribute(t,s?"":rn(r)?String(r):r)}function M0(e,t,r,n,i){if(t==="innerHTML"||t==="textContent"){r!=null&&(e[t]=t==="innerHTML"?dc(r):r);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const o=s==="OPTION"?e.getAttribute("value")||"":e.value,f=r==null?e.type==="checkbox"?"on":"":String(r);(o!==f||!("_value"in e))&&(e.value=f),r==null&&e.removeAttribute(t),e._value=r;return}let a=!1;if(r===""||r==null){const o=typeof e[t];o==="boolean"?r=hl(r):r==null&&o==="string"?(r="",a=!0):o==="number"&&(r=0,a=!0)}try{e[t]=r}catch{}a&&e.removeAttribute(i||t)}function Rn(e,t,r,n){e.addEventListener(t,r,n)}function rp(e,t,r,n){e.removeEventListener(t,r,n)}const B0=Symbol("_vei");function np(e,t,r,n,i=null){const s=e[B0]||(e[B0]={}),a=s[t];if(n&&a)a.value=n;else{const[o,f]=ip(t);if(n){const l=s[t]=op(n,i);Rn(e,o,l,f)}else a&&(rp(e,o,a,f),s[t]=void 0)}}const U0=/(?:Once|Passive|Capture)$/;function ip(e){let t;if(U0.test(e)){t={};let n;for(;n=e.match(U0);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):vn(e.slice(2)),t]}let Pa=0;const sp=Promise.resolve(),ap=()=>Pa||(sp.then(()=>Pa=0),Pa=Date.now());function op(e,t){const r=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=r.attached)return;yr(fp(n,r.value),t,5,[n])};return r.value=e,r.attached=ap(),r}function fp(e,t){if(Ae(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(n=>i=>!i._stopped&&n&&n(i))}else return t}const H0=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,lp=(e,t,r,n,i,s)=>{const a=i==="svg";t==="class"?qd(e,n,a):t==="style"?ep(e,r,n):Gs(t)?wo(t)||np(e,t,r,n,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):cp(e,t,n,a))?(M0(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&L0(e,t,n,a,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!at(n))?M0(e,sr(t),n,s,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),L0(e,t,n,a))};function cp(e,t,r,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&H0(t)&&Ce(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return H0(t)&&at(r)?!1:t in e}const W0=e=>{const t=e.props["onUpdate:modelValue"]||!1;return Ae(t)?r=>us(t,r):t};function up(e){e.target.composing=!0}function V0(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Da=Symbol("_assign"),so={created(e,{modifiers:{lazy:t,trim:r,number:n}},i){e[Da]=W0(i);const s=n||i.props&&i.props.type==="number";Rn(e,t?"change":"input",a=>{if(a.target.composing)return;let o=e.value;r&&(o=o.trim()),s&&(o=Xa(o)),e[Da](o)}),r&&Rn(e,"change",()=>{e.value=e.value.trim()}),t||(Rn(e,"compositionstart",up),Rn(e,"compositionend",V0),Rn(e,"change",V0))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:r,modifiers:{lazy:n,trim:i,number:s}},a){if(e[Da]=W0(a),e.composing)return;const o=(s||e.type==="number")&&!/^0\d/.test(e.value)?Xa(e.value):e.value,f=t??"";o!==f&&(document.activeElement===e&&e.type!=="range"&&(n&&t===r||i&&e.value.trim()===f)||(e.value=f))}},hp=["ctrl","shift","alt","meta"],dp={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>hp.some(r=>e[`${r}Key`]&&!t.includes(r))},pc=(e,t)=>{const r=e._withMods||(e._withMods={}),n=t.join(".");return r[n]||(r[n]=(i,...s)=>{for(let a=0;a<t.length;a++){const o=dp[t[a]];if(o&&o(i,t))return}return e(i,...s)})},pp=Rt({patchProp:lp},zd);let j0;function xp(){return j0||(j0=md(pp))}const mp=(...e)=>{const t=xp().createApp(...e),{mount:r}=t;return t.mount=n=>{const i=vp(n);if(!i)return;const s=t._component;!Ce(s)&&!s.render&&!s.template&&(s.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const a=r(i,!1,gp(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),a},t};function gp(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function vp(e){return at(e)?document.querySelector(e):e}const ji=(e,t)=>{const r=e.__vccOpts||e;for(const[n,i]of t)r[n]=i;return r},_p={name:"App"},Ep={id:"app"};function wp(e,t,r,n,i,s){const a=bo("router-view");return Bt(),zt("div",Ep,[vt(a)])}const Tp=ji(_p,[["render",wp]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Pn=typeof document<"u";function xc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Sp(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&xc(e.default)}const Be=Object.assign;function Ia(e,t){const r={};for(const n in t){const i=t[n];r[n]=pr(i)?i.map(e):e(i)}return r}const vi=()=>{},pr=Array.isArray,mc=/#/g,yp=/&/g,Ap=/\//g,Fp=/=/g,Cp=/\?/g,gc=/\+/g,Op=/%5B/g,Rp=/%5D/g,vc=/%5E/g,Pp=/%60/g,_c=/%7B/g,Dp=/%7C/g,Ec=/%7D/g,Ip=/%20/g;function Uo(e){return encodeURI(""+e).replace(Dp,"|").replace(Op,"[").replace(Rp,"]")}function Np(e){return Uo(e).replace(_c,"{").replace(Ec,"}").replace(vc,"^")}function ao(e){return Uo(e).replace(gc,"%2B").replace(Ip,"+").replace(mc,"%23").replace(yp,"%26").replace(Pp,"`").replace(_c,"{").replace(Ec,"}").replace(vc,"^")}function bp(e){return ao(e).replace(Fp,"%3D")}function kp(e){return Uo(e).replace(mc,"%23").replace(Cp,"%3F")}function Lp(e){return e==null?"":kp(e).replace(Ap,"%2F")}function Di(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Mp=/\/$/,Bp=e=>e.replace(Mp,"");function Na(e,t,r="/"){let n,i={},s="",a="";const o=t.indexOf("#");let f=t.indexOf("?");return o<f&&o>=0&&(f=-1),f>-1&&(n=t.slice(0,f),s=t.slice(f+1,o>-1?o:t.length),i=e(s)),o>-1&&(n=n||t.slice(0,o),a=t.slice(o,t.length)),n=Vp(n??t,r),{fullPath:n+(s&&"?")+s+a,path:n,query:i,hash:Di(a)}}function Up(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}function $0(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Hp(e,t,r){const n=t.matched.length-1,i=r.matched.length-1;return n>-1&&n===i&&Gn(t.matched[n],r.matched[i])&&wc(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}function Gn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function wc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!Wp(e[r],t[r]))return!1;return!0}function Wp(e,t){return pr(e)?G0(e,t):pr(t)?G0(t,e):e===t}function G0(e,t){return pr(t)?e.length===t.length&&e.every((r,n)=>r===t[n]):e.length===1&&e[0]===t}function Vp(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),n=e.split("/"),i=n[n.length-1];(i===".."||i===".")&&n.push("");let s=r.length-1,a,o;for(a=0;a<n.length;a++)if(o=n[a],o!==".")if(o==="..")s>1&&s--;else break;return r.slice(0,s).join("/")+"/"+n.slice(a).join("/")}const jr={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ii;(function(e){e.pop="pop",e.push="push"})(Ii||(Ii={}));var _i;(function(e){e.back="back",e.forward="forward",e.unknown=""})(_i||(_i={}));function jp(e){if(!e)if(Pn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Bp(e)}const $p=/^[^#]+#/;function Gp(e,t){return e.replace($p,"#")+t}function Kp(e,t){const r=document.documentElement.getBoundingClientRect(),n=e.getBoundingClientRect();return{behavior:t.behavior,left:n.left-r.left-(t.left||0),top:n.top-r.top-(t.top||0)}}const ia=()=>({left:window.scrollX,top:window.scrollY});function Xp(e){let t;if("el"in e){const r=e.el,n=typeof r=="string"&&r.startsWith("#"),i=typeof r=="string"?n?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!i)return;t=Kp(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function K0(e,t){return(history.state?history.state.position-t:-1)+e}const oo=new Map;function zp(e,t){oo.set(e,t)}function Yp(e){const t=oo.get(e);return oo.delete(e),t}let qp=()=>location.protocol+"//"+location.host;function Tc(e,t){const{pathname:r,search:n,hash:i}=t,s=e.indexOf("#");if(s>-1){let o=i.includes(e.slice(s))?e.slice(s).length:1,f=i.slice(o);return f[0]!=="/"&&(f="/"+f),$0(f,"")}return $0(r,e)+n+i}function Jp(e,t,r,n){let i=[],s=[],a=null;const o=({state:h})=>{const d=Tc(e,location),x=r.value,p=t.value;let m=0;if(h){if(r.value=d,t.value=h,a&&a===x){a=null;return}m=p?h.position-p.position:0}else n(d);i.forEach(C=>{C(r.value,x,{delta:m,type:Ii.pop,direction:m?m>0?_i.forward:_i.back:_i.unknown})})};function f(){a=r.value}function l(h){i.push(h);const d=()=>{const x=i.indexOf(h);x>-1&&i.splice(x,1)};return s.push(d),d}function c(){const{history:h}=window;h.state&&h.replaceState(Be({},h.state,{scroll:ia()}),"")}function u(){for(const h of s)h();s=[],window.removeEventListener("popstate",o),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",o),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:f,listen:l,destroy:u}}function X0(e,t,r,n=!1,i=!1){return{back:e,current:t,forward:r,replaced:n,position:window.history.length,scroll:i?ia():null}}function Zp(e){const{history:t,location:r}=window,n={value:Tc(e,r)},i={value:t.state};i.value||s(n.value,{back:null,current:n.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(f,l,c){const u=e.indexOf("#"),h=u>-1?(r.host&&document.querySelector("base")?e:e.slice(u))+f:qp()+e+f;try{t[c?"replaceState":"pushState"](l,"",h),i.value=l}catch(d){console.error(d),r[c?"replace":"assign"](h)}}function a(f,l){const c=Be({},t.state,X0(i.value.back,f,i.value.forward,!0),l,{position:i.value.position});s(f,c,!0),n.value=f}function o(f,l){const c=Be({},i.value,t.state,{forward:f,scroll:ia()});s(c.current,c,!0);const u=Be({},X0(n.value,f,null),{position:c.position+1},l);s(f,u,!1),n.value=f}return{location:n,state:i,push:o,replace:a}}function Qp(e){e=jp(e);const t=Zp(e),r=Jp(e,t.state,t.location,t.replace);function n(s,a=!0){a||r.pauseListeners(),history.go(s)}const i=Be({location:"",base:e,go:n,createHref:Gp.bind(null,e)},t,r);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function ex(e){return typeof e=="string"||e&&typeof e=="object"}function Sc(e){return typeof e=="string"||typeof e=="symbol"}const yc=Symbol("");var z0;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(z0||(z0={}));function Kn(e,t){return Be(new Error,{type:e,[yc]:!0},t)}function Or(e,t){return e instanceof Error&&yc in e&&(t==null||!!(e.type&t))}const Y0="[^/]+?",tx={sensitive:!1,strict:!1,start:!0,end:!0},rx=/[.+*?^${}()[\]/\\]/g;function nx(e,t){const r=Be({},tx,t),n=[];let i=r.start?"^":"";const s=[];for(const l of e){const c=l.length?[]:[90];r.strict&&!l.length&&(i+="/");for(let u=0;u<l.length;u++){const h=l[u];let d=40+(r.sensitive?.25:0);if(h.type===0)u||(i+="/"),i+=h.value.replace(rx,"\\$&"),d+=40;else if(h.type===1){const{value:x,repeatable:p,optional:m,regexp:C}=h;s.push({name:x,repeatable:p,optional:m});const F=C||Y0;if(F!==Y0){d+=10;try{new RegExp(`(${F})`)}catch(R){throw new Error(`Invalid custom RegExp for param "${x}" (${F}): `+R.message)}}let y=p?`((?:${F})(?:/(?:${F}))*)`:`(${F})`;u||(y=m&&l.length<2?`(?:/${y})`:"/"+y),m&&(y+="?"),i+=y,d+=20,m&&(d+=-8),p&&(d+=-20),F===".*"&&(d+=-50)}c.push(d)}n.push(c)}if(r.strict&&r.end){const l=n.length-1;n[l][n[l].length-1]+=.7000000000000001}r.strict||(i+="/?"),r.end?i+="$":r.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const a=new RegExp(i,r.sensitive?"":"i");function o(l){const c=l.match(a),u={};if(!c)return null;for(let h=1;h<c.length;h++){const d=c[h]||"",x=s[h-1];u[x.name]=d&&x.repeatable?d.split("/"):d}return u}function f(l){let c="",u=!1;for(const h of e){(!u||!c.endsWith("/"))&&(c+="/"),u=!1;for(const d of h)if(d.type===0)c+=d.value;else if(d.type===1){const{value:x,repeatable:p,optional:m}=d,C=x in l?l[x]:"";if(pr(C)&&!p)throw new Error(`Provided param "${x}" is an array but it is not repeatable (* or + modifiers)`);const F=pr(C)?C.join("/"):C;if(!F)if(m)h.length<2&&(c.endsWith("/")?c=c.slice(0,-1):u=!0);else throw new Error(`Missing required param "${x}"`);c+=F}}return c||"/"}return{re:a,score:n,keys:s,parse:o,stringify:f}}function ix(e,t){let r=0;for(;r<e.length&&r<t.length;){const n=t[r]-e[r];if(n)return n;r++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ac(e,t){let r=0;const n=e.score,i=t.score;for(;r<n.length&&r<i.length;){const s=ix(n[r],i[r]);if(s)return s;r++}if(Math.abs(i.length-n.length)===1){if(q0(n))return 1;if(q0(i))return-1}return i.length-n.length}function q0(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const sx={type:0,value:""},ax=/[a-zA-Z0-9_]/;function ox(e){if(!e)return[[]];if(e==="/")return[[sx]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(d){throw new Error(`ERR (${r})/"${l}": ${d}`)}let r=0,n=r;const i=[];let s;function a(){s&&i.push(s),s=[]}let o=0,f,l="",c="";function u(){l&&(r===0?s.push({type:0,value:l}):r===1||r===2||r===3?(s.length>1&&(f==="*"||f==="+")&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:l,regexp:c,repeatable:f==="*"||f==="+",optional:f==="*"||f==="?"})):t("Invalid state to consume buffer"),l="")}function h(){l+=f}for(;o<e.length;){if(f=e[o++],f==="\\"&&r!==2){n=r,r=4;continue}switch(r){case 0:f==="/"?(l&&u(),a()):f===":"?(u(),r=1):h();break;case 4:h(),r=n;break;case 1:f==="("?r=2:ax.test(f)?h():(u(),r=0,f!=="*"&&f!=="?"&&f!=="+"&&o--);break;case 2:f===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+f:r=3:c+=f;break;case 3:u(),r=0,f!=="*"&&f!=="?"&&f!=="+"&&o--,c="";break;default:t("Unknown state");break}}return r===2&&t(`Unfinished custom RegExp for param "${l}"`),u(),a(),i}function fx(e,t,r){const n=nx(ox(e.path),r),i=Be(n,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function lx(e,t){const r=[],n=new Map;t=ef({strict:!1,end:!0,sensitive:!1},t);function i(u){return n.get(u)}function s(u,h,d){const x=!d,p=Z0(u);p.aliasOf=d&&d.record;const m=ef(t,u),C=[p];if("alias"in u){const R=typeof u.alias=="string"?[u.alias]:u.alias;for(const z of R)C.push(Z0(Be({},p,{components:d?d.record.components:p.components,path:z,aliasOf:d?d.record:p})))}let F,y;for(const R of C){const{path:z}=R;if(h&&z[0]!=="/"){const se=h.record.path,D=se[se.length-1]==="/"?"":"/";R.path=h.record.path+(z&&D+z)}if(F=fx(R,h,m),d?d.alias.push(F):(y=y||F,y!==F&&y.alias.push(F),x&&u.name&&!Q0(F)&&a(u.name)),Fc(F)&&f(F),p.children){const se=p.children;for(let D=0;D<se.length;D++)s(se[D],F,d&&d.children[D])}d=d||F}return y?()=>{a(y)}:vi}function a(u){if(Sc(u)){const h=n.get(u);h&&(n.delete(u),r.splice(r.indexOf(h),1),h.children.forEach(a),h.alias.forEach(a))}else{const h=r.indexOf(u);h>-1&&(r.splice(h,1),u.record.name&&n.delete(u.record.name),u.children.forEach(a),u.alias.forEach(a))}}function o(){return r}function f(u){const h=hx(u,r);r.splice(h,0,u),u.record.name&&!Q0(u)&&n.set(u.record.name,u)}function l(u,h){let d,x={},p,m;if("name"in u&&u.name){if(d=n.get(u.name),!d)throw Kn(1,{location:u});m=d.record.name,x=Be(J0(h.params,d.keys.filter(y=>!y.optional).concat(d.parent?d.parent.keys.filter(y=>y.optional):[]).map(y=>y.name)),u.params&&J0(u.params,d.keys.map(y=>y.name))),p=d.stringify(x)}else if(u.path!=null)p=u.path,d=r.find(y=>y.re.test(p)),d&&(x=d.parse(p),m=d.record.name);else{if(d=h.name?n.get(h.name):r.find(y=>y.re.test(h.path)),!d)throw Kn(1,{location:u,currentLocation:h});m=d.record.name,x=Be({},h.params,u.params),p=d.stringify(x)}const C=[];let F=d;for(;F;)C.unshift(F.record),F=F.parent;return{name:m,path:p,params:x,matched:C,meta:ux(C)}}e.forEach(u=>s(u));function c(){r.length=0,n.clear()}return{addRoute:s,resolve:l,removeRoute:a,clearRoutes:c,getRoutes:o,getRecordMatcher:i}}function J0(e,t){const r={};for(const n of t)n in e&&(r[n]=e[n]);return r}function Z0(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:cx(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function cx(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const n in e.components)t[n]=typeof r=="object"?r[n]:r;return t}function Q0(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ux(e){return e.reduce((t,r)=>Be(t,r.meta),{})}function ef(e,t){const r={};for(const n in e)r[n]=n in t?t[n]:e[n];return r}function hx(e,t){let r=0,n=t.length;for(;r!==n;){const s=r+n>>1;Ac(e,t[s])<0?n=s:r=s+1}const i=dx(e);return i&&(n=t.lastIndexOf(i,n-1)),n}function dx(e){let t=e;for(;t=t.parent;)if(Fc(t)&&Ac(e,t)===0)return t}function Fc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function px(e){const t={};if(e===""||e==="?")return t;const n=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<n.length;++i){const s=n[i].replace(gc," "),a=s.indexOf("="),o=Di(a<0?s:s.slice(0,a)),f=a<0?null:Di(s.slice(a+1));if(o in t){let l=t[o];pr(l)||(l=t[o]=[l]),l.push(f)}else t[o]=f}return t}function tf(e){let t="";for(let r in e){const n=e[r];if(r=bp(r),n==null){n!==void 0&&(t+=(t.length?"&":"")+r);continue}(pr(n)?n.map(s=>s&&ao(s)):[n&&ao(n)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+r,s!=null&&(t+="="+s))})}return t}function xx(e){const t={};for(const r in e){const n=e[r];n!==void 0&&(t[r]=pr(n)?n.map(i=>i==null?null:""+i):n==null?n:""+n)}return t}const mx=Symbol(""),rf=Symbol(""),Ho=Symbol(""),Cc=Symbol(""),fo=Symbol("");function ai(){let e=[];function t(n){return e.push(n),()=>{const i=e.indexOf(n);i>-1&&e.splice(i,1)}}function r(){e=[]}return{add:t,list:()=>e.slice(),reset:r}}function Xr(e,t,r,n,i,s=a=>a()){const a=n&&(n.enterCallbacks[i]=n.enterCallbacks[i]||[]);return()=>new Promise((o,f)=>{const l=h=>{h===!1?f(Kn(4,{from:r,to:t})):h instanceof Error?f(h):ex(h)?f(Kn(2,{from:t,to:h})):(a&&n.enterCallbacks[i]===a&&typeof h=="function"&&a.push(h),o())},c=s(()=>e.call(n&&n.instances[i],t,r,l));let u=Promise.resolve(c);e.length<3&&(u=u.then(l)),u.catch(h=>f(h))})}function ba(e,t,r,n,i=s=>s()){const s=[];for(const a of e)for(const o in a.components){let f=a.components[o];if(!(t!=="beforeRouteEnter"&&!a.instances[o]))if(xc(f)){const c=(f.__vccOpts||f)[t];c&&s.push(Xr(c,r,n,a,o,i))}else{let l=f();s.push(()=>l.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${o}" at "${a.path}"`);const u=Sp(c)?c.default:c;a.mods[o]=c,a.components[o]=u;const d=(u.__vccOpts||u)[t];return d&&Xr(d,r,n,a,o,i)()}))}}return s}function nf(e){const t=Ir(Ho),r=Ir(Cc),n=lr(()=>{const f=Ln(e.to);return t.resolve(f)}),i=lr(()=>{const{matched:f}=n.value,{length:l}=f,c=f[l-1],u=r.matched;if(!c||!u.length)return-1;const h=u.findIndex(Gn.bind(null,c));if(h>-1)return h;const d=sf(f[l-2]);return l>1&&sf(c)===d&&u[u.length-1].path!==d?u.findIndex(Gn.bind(null,f[l-2])):h}),s=lr(()=>i.value>-1&&wx(r.params,n.value.params)),a=lr(()=>i.value>-1&&i.value===r.matched.length-1&&wc(r.params,n.value.params));function o(f={}){if(Ex(f)){const l=t[Ln(e.replace)?"replace":"push"](Ln(e.to)).catch(vi);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>l),l}return Promise.resolve()}return{route:n,href:lr(()=>n.value.href),isActive:s,isExactActive:a,navigate:o}}function gx(e){return e.length===1?e[0]:e}const vx=Ul({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:nf,setup(e,{slots:t}){const r=Zs(nf(e)),{options:n}=Ir(Ho),i=lr(()=>({[af(e.activeClass,n.linkActiveClass,"router-link-active")]:r.isActive,[af(e.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const s=t.default&&gx(t.default(r));return e.custom?s:hc("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:i.value},s)}}}),_x=vx;function Ex(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function wx(e,t){for(const r in t){const n=t[r],i=e[r];if(typeof n=="string"){if(n!==i)return!1}else if(!pr(i)||i.length!==n.length||n.some((s,a)=>s!==i[a]))return!1}return!0}function sf(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const af=(e,t,r)=>e??t??r,Tx=Ul({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const n=Ir(fo),i=lr(()=>e.route||n.value),s=Ir(rf,0),a=lr(()=>{let l=Ln(s);const{matched:c}=i.value;let u;for(;(u=c[l])&&!u.components;)l++;return l}),o=lr(()=>i.value.matched[a.value]);hs(rf,lr(()=>a.value+1)),hs(mx,o),hs(fo,i);const f=C1();return ds(()=>[f.value,o.value,e.name],([l,c,u],[h,d,x])=>{c&&(c.instances[u]=l,d&&d!==c&&l&&l===h&&(c.leaveGuards.size||(c.leaveGuards=d.leaveGuards),c.updateGuards.size||(c.updateGuards=d.updateGuards))),l&&c&&(!d||!Gn(c,d)||!h)&&(c.enterCallbacks[u]||[]).forEach(p=>p(l))},{flush:"post"}),()=>{const l=i.value,c=e.name,u=o.value,h=u&&u.components[c];if(!h)return of(r.default,{Component:h,route:l});const d=u.props[c],x=d?d===!0?l.params:typeof d=="function"?d(l):d:null,m=hc(h,Be({},x,t,{onVnodeUnmounted:C=>{C.component.isUnmounted&&(u.instances[c]=null)},ref:f}));return of(r.default,{Component:m,route:l})||m}}});function of(e,t){if(!e)return null;const r=e(t);return r.length===1?r[0]:r}const Sx=Tx;function yx(e){const t=lx(e.routes,e),r=e.parseQuery||px,n=e.stringifyQuery||tf,i=e.history,s=ai(),a=ai(),o=ai(),f=O1(jr);let l=jr;Pn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Ia.bind(null,k=>""+k),u=Ia.bind(null,Lp),h=Ia.bind(null,Di);function d(k,q){let j,G;return Sc(k)?(j=t.getRecordMatcher(k),G=q):G=k,t.addRoute(G,j)}function x(k){const q=t.getRecordMatcher(k);q&&t.removeRoute(q)}function p(){return t.getRoutes().map(k=>k.record)}function m(k){return!!t.getRecordMatcher(k)}function C(k,q){if(q=Be({},q||f.value),typeof k=="string"){const P=Na(r,k,q.path),J=t.resolve({path:P.path},q),K=i.createHref(P.fullPath);return Be(P,J,{params:h(J.params),hash:Di(P.hash),redirectedFrom:void 0,href:K})}let j;if(k.path!=null)j=Be({},k,{path:Na(r,k.path,q.path).path});else{const P=Be({},k.params);for(const J in P)P[J]==null&&delete P[J];j=Be({},k,{params:u(P)}),q.params=u(q.params)}const G=t.resolve(j,q),_e=k.hash||"";G.params=c(h(G.params));const E=Up(n,Be({},k,{hash:Np(_e),path:G.path})),A=i.createHref(E);return Be({fullPath:E,hash:_e,query:n===tf?xx(k.query):k.query||{}},G,{redirectedFrom:void 0,href:A})}function F(k){return typeof k=="string"?Na(r,k,f.value.path):Be({},k)}function y(k,q){if(l!==k)return Kn(8,{from:q,to:k})}function R(k){return D(k)}function z(k){return R(Be(F(k),{replace:!0}))}function se(k){const q=k.matched[k.matched.length-1];if(q&&q.redirect){const{redirect:j}=q;let G=typeof j=="function"?j(k):j;return typeof G=="string"&&(G=G.includes("?")||G.includes("#")?G=F(G):{path:G},G.params={}),Be({query:k.query,hash:k.hash,params:G.path!=null?{}:k.params},G)}}function D(k,q){const j=l=C(k),G=f.value,_e=k.state,E=k.force,A=k.replace===!0,P=se(j);if(P)return D(Be(F(P),{state:typeof P=="object"?Be({},_e,P.state):_e,force:E,replace:A}),q||j);const J=j;J.redirectedFrom=q;let K;return!E&&Hp(n,G,j)&&(K=Kn(16,{to:J,from:G}),qe(G,G,!0,!1)),(K?Promise.resolve(K):ee(J,G)).catch(b=>Or(b)?Or(b,2)?b:nt(b):Oe(b,J,G)).then(b=>{if(b){if(Or(b,2))return D(Be({replace:A},F(b.to),{state:typeof b.to=="object"?Be({},_e,b.to.state):_e,force:E}),q||J)}else b=ne(J,G,!0,A,_e);return re(J,G,b),b})}function Y(k,q){const j=y(k,q);return j?Promise.reject(j):Promise.resolve()}function M(k){const q=N.values().next().value;return q&&typeof q.runWithContext=="function"?q.runWithContext(k):k()}function ee(k,q){let j;const[G,_e,E]=Ax(k,q);j=ba(G.reverse(),"beforeRouteLeave",k,q);for(const P of G)P.leaveGuards.forEach(J=>{j.push(Xr(J,k,q))});const A=Y.bind(null,k,q);return j.push(A),Q(j).then(()=>{j=[];for(const P of s.list())j.push(Xr(P,k,q));return j.push(A),Q(j)}).then(()=>{j=ba(_e,"beforeRouteUpdate",k,q);for(const P of _e)P.updateGuards.forEach(J=>{j.push(Xr(J,k,q))});return j.push(A),Q(j)}).then(()=>{j=[];for(const P of E)if(P.beforeEnter)if(pr(P.beforeEnter))for(const J of P.beforeEnter)j.push(Xr(J,k,q));else j.push(Xr(P.beforeEnter,k,q));return j.push(A),Q(j)}).then(()=>(k.matched.forEach(P=>P.enterCallbacks={}),j=ba(E,"beforeRouteEnter",k,q,M),j.push(A),Q(j))).then(()=>{j=[];for(const P of a.list())j.push(Xr(P,k,q));return j.push(A),Q(j)}).catch(P=>Or(P,8)?P:Promise.reject(P))}function re(k,q,j){o.list().forEach(G=>M(()=>G(k,q,j)))}function ne(k,q,j,G,_e){const E=y(k,q);if(E)return E;const A=q===jr,P=Pn?history.state:{};j&&(G||A?i.replace(k.fullPath,Be({scroll:A&&P&&P.scroll},_e)):i.push(k.fullPath,_e)),f.value=k,qe(k,q,j,A),nt()}let de;function be(){de||(de=i.listen((k,q,j)=>{if(!I.listening)return;const G=C(k),_e=se(G);if(_e){D(Be(_e,{replace:!0,force:!0}),G).catch(vi);return}l=G;const E=f.value;Pn&&zp(K0(E.fullPath,j.delta),ia()),ee(G,E).catch(A=>Or(A,12)?A:Or(A,2)?(D(Be(F(A.to),{force:!0}),G).then(P=>{Or(P,20)&&!j.delta&&j.type===Ii.pop&&i.go(-1,!1)}).catch(vi),Promise.reject()):(j.delta&&i.go(-j.delta,!1),Oe(A,G,E))).then(A=>{A=A||ne(G,E,!1),A&&(j.delta&&!Or(A,8)?i.go(-j.delta,!1):j.type===Ii.pop&&Or(A,20)&&i.go(-1,!1)),re(G,E,A)}).catch(vi)}))}let ye=ai(),Ne=ai(),Te;function Oe(k,q,j){nt(k);const G=Ne.list();return G.length?G.forEach(_e=>_e(k,q,j)):console.error(k),Promise.reject(k)}function Ge(){return Te&&f.value!==jr?Promise.resolve():new Promise((k,q)=>{ye.add([k,q])})}function nt(k){return Te||(Te=!k,be(),ye.list().forEach(([q,j])=>k?j(k):q()),ye.reset()),k}function qe(k,q,j,G){const{scrollBehavior:_e}=e;if(!Pn||!_e)return Promise.resolve();const E=!j&&Yp(K0(k.fullPath,0))||(G||!j)&&history.state&&history.state.scroll||null;return bl().then(()=>_e(k,q,E)).then(A=>A&&Xp(A)).catch(A=>Oe(A,k,q))}const O=k=>i.go(k);let H;const N=new Set,I={currentRoute:f,listening:!0,addRoute:d,removeRoute:x,clearRoutes:t.clearRoutes,hasRoute:m,getRoutes:p,resolve:C,options:e,push:R,replace:z,go:O,back:()=>O(-1),forward:()=>O(1),beforeEach:s.add,beforeResolve:a.add,afterEach:o.add,onError:Ne.add,isReady:Ge,install(k){const q=this;k.component("RouterLink",_x),k.component("RouterView",Sx),k.config.globalProperties.$router=q,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>Ln(f)}),Pn&&!H&&f.value===jr&&(H=!0,R(i.location).catch(_e=>{}));const j={};for(const _e in jr)Object.defineProperty(j,_e,{get:()=>f.value[_e],enumerable:!0});k.provide(Ho,q),k.provide(Cc,Rl(j)),k.provide(fo,f);const G=k.unmount;N.add(k),k.unmount=function(){N.delete(k),N.size<1&&(l=jr,de&&de(),de=null,f.value=jr,H=!1,Te=!1),G()}}};function Q(k){return k.reduce((q,j)=>q.then(()=>M(j)),Promise.resolve())}return I}function Ax(e,t){const r=[],n=[],i=[],s=Math.max(t.matched.length,e.matched.length);for(let a=0;a<s;a++){const o=t.matched[a];o&&(e.matched.find(l=>Gn(l,o))?n.push(o):r.push(o));const f=e.matched[a];f&&(t.matched.find(l=>Gn(l,f))||i.push(f))}return[r,n,i]}function Oc(e,t){return function(){return e.apply(t,arguments)}}const{toString:Fx}=Object.prototype,{getPrototypeOf:Wo}=Object,{iterator:sa,toStringTag:Rc}=Symbol,aa=(e=>t=>{const r=Fx.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),xr=e=>(e=e.toLowerCase(),t=>aa(t)===e),oa=e=>t=>typeof t===e,{isArray:zn}=Array,Ni=oa("undefined");function Cx(e){return e!==null&&!Ni(e)&&e.constructor!==null&&!Ni(e.constructor)&&Ut(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Pc=xr("ArrayBuffer");function Ox(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Pc(e.buffer),t}const Rx=oa("string"),Ut=oa("function"),Dc=oa("number"),fa=e=>e!==null&&typeof e=="object",Px=e=>e===!0||e===!1,gs=e=>{if(aa(e)!=="object")return!1;const t=Wo(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Rc in e)&&!(sa in e)},Dx=xr("Date"),Ix=xr("File"),Nx=xr("Blob"),bx=xr("FileList"),kx=e=>fa(e)&&Ut(e.pipe),Lx=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ut(e.append)&&((t=aa(e))==="formdata"||t==="object"&&Ut(e.toString)&&e.toString()==="[object FormData]"))},Mx=xr("URLSearchParams"),[Bx,Ux,Hx,Wx]=["ReadableStream","Request","Response","Headers"].map(xr),Vx=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function $i(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,i;if(typeof e!="object"&&(e=[e]),zn(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{const s=r?Object.getOwnPropertyNames(e):Object.keys(e),a=s.length;let o;for(n=0;n<a;n++)o=s[n],t.call(null,e[o],o,e)}}function Ic(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,i;for(;n-- >0;)if(i=r[n],t===i.toLowerCase())return i;return null}const cn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Nc=e=>!Ni(e)&&e!==cn;function lo(){const{caseless:e}=Nc(this)&&this||{},t={},r=(n,i)=>{const s=e&&Ic(t,i)||i;gs(t[s])&&gs(n)?t[s]=lo(t[s],n):gs(n)?t[s]=lo({},n):zn(n)?t[s]=n.slice():t[s]=n};for(let n=0,i=arguments.length;n<i;n++)arguments[n]&&$i(arguments[n],r);return t}const jx=(e,t,r,{allOwnKeys:n}={})=>($i(t,(i,s)=>{r&&Ut(i)?e[s]=Oc(i,r):e[s]=i},{allOwnKeys:n}),e),$x=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Gx=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Kx=(e,t,r,n)=>{let i,s,a;const o={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)a=i[s],(!n||n(a,e,t))&&!o[a]&&(t[a]=e[a],o[a]=!0);e=r!==!1&&Wo(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},Xx=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},zx=e=>{if(!e)return null;if(zn(e))return e;let t=e.length;if(!Dc(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Yx=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Wo(Uint8Array)),qx=(e,t)=>{const n=(e&&e[sa]).call(e);let i;for(;(i=n.next())&&!i.done;){const s=i.value;t.call(e,s[0],s[1])}},Jx=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},Zx=xr("HTMLFormElement"),Qx=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,i){return n.toUpperCase()+i}),ff=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),em=xr("RegExp"),bc=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};$i(r,(i,s)=>{let a;(a=t(i,s,e))!==!1&&(n[s]=a||i)}),Object.defineProperties(e,n)},tm=e=>{bc(e,(t,r)=>{if(Ut(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(Ut(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},rm=(e,t)=>{const r={},n=i=>{i.forEach(s=>{r[s]=!0})};return zn(e)?n(e):n(String(e).split(t)),r},nm=()=>{},im=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function sm(e){return!!(e&&Ut(e.append)&&e[Rc]==="FormData"&&e[sa])}const am=e=>{const t=new Array(10),r=(n,i)=>{if(fa(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[i]=n;const s=zn(n)?[]:{};return $i(n,(a,o)=>{const f=r(a,i+1);!Ni(f)&&(s[o]=f)}),t[i]=void 0,s}}return n};return r(e,0)},om=xr("AsyncFunction"),fm=e=>e&&(fa(e)||Ut(e))&&Ut(e.then)&&Ut(e.catch),kc=((e,t)=>e?setImmediate:t?((r,n)=>(cn.addEventListener("message",({source:i,data:s})=>{i===cn&&s===r&&n.length&&n.shift()()},!1),i=>{n.push(i),cn.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Ut(cn.postMessage)),lm=typeof queueMicrotask<"u"?queueMicrotask.bind(cn):typeof process<"u"&&process.nextTick||kc,cm=e=>e!=null&&Ut(e[sa]),V={isArray:zn,isArrayBuffer:Pc,isBuffer:Cx,isFormData:Lx,isArrayBufferView:Ox,isString:Rx,isNumber:Dc,isBoolean:Px,isObject:fa,isPlainObject:gs,isReadableStream:Bx,isRequest:Ux,isResponse:Hx,isHeaders:Wx,isUndefined:Ni,isDate:Dx,isFile:Ix,isBlob:Nx,isRegExp:em,isFunction:Ut,isStream:kx,isURLSearchParams:Mx,isTypedArray:Yx,isFileList:bx,forEach:$i,merge:lo,extend:jx,trim:Vx,stripBOM:$x,inherits:Gx,toFlatObject:Kx,kindOf:aa,kindOfTest:xr,endsWith:Xx,toArray:zx,forEachEntry:qx,matchAll:Jx,isHTMLForm:Zx,hasOwnProperty:ff,hasOwnProp:ff,reduceDescriptors:bc,freezeMethods:tm,toObjectSet:rm,toCamelCase:Qx,noop:nm,toFiniteNumber:im,findKey:Ic,global:cn,isContextDefined:Nc,isSpecCompliantForm:sm,toJSONObject:am,isAsyncFn:om,isThenable:fm,setImmediate:kc,asap:lm,isIterable:cm};function Re(e,t,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}V.inherits(Re,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:V.toJSONObject(this.config),code:this.code,status:this.status}}});const Lc=Re.prototype,Mc={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Mc[e]={value:e}});Object.defineProperties(Re,Mc);Object.defineProperty(Lc,"isAxiosError",{value:!0});Re.from=(e,t,r,n,i,s)=>{const a=Object.create(Lc);return V.toFlatObject(e,a,function(f){return f!==Error.prototype},o=>o!=="isAxiosError"),Re.call(a,e.message,t,r,n,i),a.cause=e,a.name=e.name,s&&Object.assign(a,s),a};const um=null;function co(e){return V.isPlainObject(e)||V.isArray(e)}function Bc(e){return V.endsWith(e,"[]")?e.slice(0,-2):e}function lf(e,t,r){return e?e.concat(t).map(function(i,s){return i=Bc(i),!r&&s?"["+i+"]":i}).join(r?".":""):t}function hm(e){return V.isArray(e)&&!e.some(co)}const dm=V.toFlatObject(V,{},null,function(t){return/^is[A-Z]/.test(t)});function la(e,t,r){if(!V.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=V.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,m){return!V.isUndefined(m[p])});const n=r.metaTokens,i=r.visitor||c,s=r.dots,a=r.indexes,f=(r.Blob||typeof Blob<"u"&&Blob)&&V.isSpecCompliantForm(t);if(!V.isFunction(i))throw new TypeError("visitor must be a function");function l(x){if(x===null)return"";if(V.isDate(x))return x.toISOString();if(!f&&V.isBlob(x))throw new Re("Blob is not supported. Use a Buffer instead.");return V.isArrayBuffer(x)||V.isTypedArray(x)?f&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function c(x,p,m){let C=x;if(x&&!m&&typeof x=="object"){if(V.endsWith(p,"{}"))p=n?p:p.slice(0,-2),x=JSON.stringify(x);else if(V.isArray(x)&&hm(x)||(V.isFileList(x)||V.endsWith(p,"[]"))&&(C=V.toArray(x)))return p=Bc(p),C.forEach(function(y,R){!(V.isUndefined(y)||y===null)&&t.append(a===!0?lf([p],R,s):a===null?p:p+"[]",l(y))}),!1}return co(x)?!0:(t.append(lf(m,p,s),l(x)),!1)}const u=[],h=Object.assign(dm,{defaultVisitor:c,convertValue:l,isVisitable:co});function d(x,p){if(!V.isUndefined(x)){if(u.indexOf(x)!==-1)throw Error("Circular reference detected in "+p.join("."));u.push(x),V.forEach(x,function(C,F){(!(V.isUndefined(C)||C===null)&&i.call(t,C,V.isString(F)?F.trim():F,p,h))===!0&&d(C,p?p.concat(F):[F])}),u.pop()}}if(!V.isObject(e))throw new TypeError("data must be an object");return d(e),t}function cf(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function Vo(e,t){this._pairs=[],e&&la(e,this,t)}const Uc=Vo.prototype;Uc.append=function(t,r){this._pairs.push([t,r])};Uc.toString=function(t){const r=t?function(n){return t.call(this,n,cf)}:cf;return this._pairs.map(function(i){return r(i[0])+"="+r(i[1])},"").join("&")};function pm(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Hc(e,t,r){if(!t)return e;const n=r&&r.encode||pm;V.isFunction(r)&&(r={serialize:r});const i=r&&r.serialize;let s;if(i?s=i(t,r):s=V.isURLSearchParams(t)?t.toString():new Vo(t,r).toString(n),s){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class uf{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){V.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Wc={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},xm=typeof URLSearchParams<"u"?URLSearchParams:Vo,mm=typeof FormData<"u"?FormData:null,gm=typeof Blob<"u"?Blob:null,vm={isBrowser:!0,classes:{URLSearchParams:xm,FormData:mm,Blob:gm},protocols:["http","https","file","blob","url","data"]},jo=typeof window<"u"&&typeof document<"u",uo=typeof navigator=="object"&&navigator||void 0,_m=jo&&(!uo||["ReactNative","NativeScript","NS"].indexOf(uo.product)<0),Em=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",wm=jo&&window.location.href||"http://localhost",Tm=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:jo,hasStandardBrowserEnv:_m,hasStandardBrowserWebWorkerEnv:Em,navigator:uo,origin:wm},Symbol.toStringTag,{value:"Module"})),At={...Tm,...vm};function Sm(e,t){return la(e,new At.classes.URLSearchParams,Object.assign({visitor:function(r,n,i,s){return At.isNode&&V.isBuffer(r)?(this.append(n,r.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function ym(e){return V.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Am(e){const t={},r=Object.keys(e);let n;const i=r.length;let s;for(n=0;n<i;n++)s=r[n],t[s]=e[s];return t}function Vc(e){function t(r,n,i,s){let a=r[s++];if(a==="__proto__")return!0;const o=Number.isFinite(+a),f=s>=r.length;return a=!a&&V.isArray(i)?i.length:a,f?(V.hasOwnProp(i,a)?i[a]=[i[a],n]:i[a]=n,!o):((!i[a]||!V.isObject(i[a]))&&(i[a]=[]),t(r,n,i[a],s)&&V.isArray(i[a])&&(i[a]=Am(i[a])),!o)}if(V.isFormData(e)&&V.isFunction(e.entries)){const r={};return V.forEachEntry(e,(n,i)=>{t(ym(n),i,r,0)}),r}return null}function Fm(e,t,r){if(V.isString(e))try{return(t||JSON.parse)(e),V.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const Gi={transitional:Wc,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",i=n.indexOf("application/json")>-1,s=V.isObject(t);if(s&&V.isHTMLForm(t)&&(t=new FormData(t)),V.isFormData(t))return i?JSON.stringify(Vc(t)):t;if(V.isArrayBuffer(t)||V.isBuffer(t)||V.isStream(t)||V.isFile(t)||V.isBlob(t)||V.isReadableStream(t))return t;if(V.isArrayBufferView(t))return t.buffer;if(V.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Sm(t,this.formSerializer).toString();if((o=V.isFileList(t))||n.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return la(o?{"files[]":t}:t,f&&new f,this.formSerializer)}}return s||i?(r.setContentType("application/json",!1),Fm(t)):t}],transformResponse:[function(t){const r=this.transitional||Gi.transitional,n=r&&r.forcedJSONParsing,i=this.responseType==="json";if(V.isResponse(t)||V.isReadableStream(t))return t;if(t&&V.isString(t)&&(n&&!this.responseType||i)){const a=!(r&&r.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(o){if(a)throw o.name==="SyntaxError"?Re.from(o,Re.ERR_BAD_RESPONSE,this,null,this.response):o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:At.classes.FormData,Blob:At.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};V.forEach(["delete","get","head","post","put","patch"],e=>{Gi.headers[e]={}});const Cm=V.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Om=e=>{const t={};let r,n,i;return e&&e.split(`
`).forEach(function(a){i=a.indexOf(":"),r=a.substring(0,i).trim().toLowerCase(),n=a.substring(i+1).trim(),!(!r||t[r]&&Cm[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},hf=Symbol("internals");function oi(e){return e&&String(e).trim().toLowerCase()}function vs(e){return e===!1||e==null?e:V.isArray(e)?e.map(vs):String(e)}function Rm(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Pm=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ka(e,t,r,n,i){if(V.isFunction(n))return n.call(this,t,r);if(i&&(t=r),!!V.isString(t)){if(V.isString(n))return t.indexOf(n)!==-1;if(V.isRegExp(n))return n.test(t)}}function Dm(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function Im(e,t){const r=V.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(i,s,a){return this[n].call(this,t,i,s,a)},configurable:!0})})}let Ht=class{constructor(t){t&&this.set(t)}set(t,r,n){const i=this;function s(o,f,l){const c=oi(f);if(!c)throw new Error("header name must be a non-empty string");const u=V.findKey(i,c);(!u||i[u]===void 0||l===!0||l===void 0&&i[u]!==!1)&&(i[u||f]=vs(o))}const a=(o,f)=>V.forEach(o,(l,c)=>s(l,c,f));if(V.isPlainObject(t)||t instanceof this.constructor)a(t,r);else if(V.isString(t)&&(t=t.trim())&&!Pm(t))a(Om(t),r);else if(V.isObject(t)&&V.isIterable(t)){let o={},f,l;for(const c of t){if(!V.isArray(c))throw TypeError("Object iterator must return a key-value pair");o[l=c[0]]=(f=o[l])?V.isArray(f)?[...f,c[1]]:[f,c[1]]:c[1]}a(o,r)}else t!=null&&s(r,t,n);return this}get(t,r){if(t=oi(t),t){const n=V.findKey(this,t);if(n){const i=this[n];if(!r)return i;if(r===!0)return Rm(i);if(V.isFunction(r))return r.call(this,i,n);if(V.isRegExp(r))return r.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=oi(t),t){const n=V.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||ka(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let i=!1;function s(a){if(a=oi(a),a){const o=V.findKey(n,a);o&&(!r||ka(n,n[o],o,r))&&(delete n[o],i=!0)}}return V.isArray(t)?t.forEach(s):s(t),i}clear(t){const r=Object.keys(this);let n=r.length,i=!1;for(;n--;){const s=r[n];(!t||ka(this,this[s],s,t,!0))&&(delete this[s],i=!0)}return i}normalize(t){const r=this,n={};return V.forEach(this,(i,s)=>{const a=V.findKey(n,s);if(a){r[a]=vs(i),delete r[s];return}const o=t?Dm(s):String(s).trim();o!==s&&delete r[s],r[o]=vs(i),n[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return V.forEach(this,(n,i)=>{n!=null&&n!==!1&&(r[i]=t&&V.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(i=>n.set(i)),n}static accessor(t){const n=(this[hf]=this[hf]={accessors:{}}).accessors,i=this.prototype;function s(a){const o=oi(a);n[o]||(Im(i,a),n[o]=!0)}return V.isArray(t)?t.forEach(s):s(t),this}};Ht.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);V.reduceDescriptors(Ht.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});V.freezeMethods(Ht);function La(e,t){const r=this||Gi,n=t||r,i=Ht.from(n.headers);let s=n.data;return V.forEach(e,function(o){s=o.call(r,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function jc(e){return!!(e&&e.__CANCEL__)}function Yn(e,t,r){Re.call(this,e??"canceled",Re.ERR_CANCELED,t,r),this.name="CanceledError"}V.inherits(Yn,Re,{__CANCEL__:!0});function $c(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new Re("Request failed with status code "+r.status,[Re.ERR_BAD_REQUEST,Re.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Nm(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function bm(e,t){e=e||10;const r=new Array(e),n=new Array(e);let i=0,s=0,a;return t=t!==void 0?t:1e3,function(f){const l=Date.now(),c=n[s];a||(a=l),r[i]=f,n[i]=l;let u=s,h=0;for(;u!==i;)h+=r[u++],u=u%e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),l-a<t)return;const d=c&&l-c;return d?Math.round(h*1e3/d):void 0}}function km(e,t){let r=0,n=1e3/t,i,s;const a=(l,c=Date.now())=>{r=c,i=null,s&&(clearTimeout(s),s=null),e.apply(null,l)};return[(...l)=>{const c=Date.now(),u=c-r;u>=n?a(l,c):(i=l,s||(s=setTimeout(()=>{s=null,a(i)},n-u)))},()=>i&&a(i)]}const Os=(e,t,r=3)=>{let n=0;const i=bm(50,250);return km(s=>{const a=s.loaded,o=s.lengthComputable?s.total:void 0,f=a-n,l=i(f),c=a<=o;n=a;const u={loaded:a,total:o,progress:o?a/o:void 0,bytes:f,rate:l||void 0,estimated:l&&o&&c?(o-a)/l:void 0,event:s,lengthComputable:o!=null,[t?"download":"upload"]:!0};e(u)},r)},df=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},pf=e=>(...t)=>V.asap(()=>e(...t)),Lm=At.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,At.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(At.origin),At.navigator&&/(msie|trident)/i.test(At.navigator.userAgent)):()=>!0,Mm=At.hasStandardBrowserEnv?{write(e,t,r,n,i,s){const a=[e+"="+encodeURIComponent(t)];V.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),V.isString(n)&&a.push("path="+n),V.isString(i)&&a.push("domain="+i),s===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Bm(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Um(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Gc(e,t,r){let n=!Bm(t);return e&&(n||r==!1)?Um(e,t):t}const xf=e=>e instanceof Ht?{...e}:e;function pn(e,t){t=t||{};const r={};function n(l,c,u,h){return V.isPlainObject(l)&&V.isPlainObject(c)?V.merge.call({caseless:h},l,c):V.isPlainObject(c)?V.merge({},c):V.isArray(c)?c.slice():c}function i(l,c,u,h){if(V.isUndefined(c)){if(!V.isUndefined(l))return n(void 0,l,u,h)}else return n(l,c,u,h)}function s(l,c){if(!V.isUndefined(c))return n(void 0,c)}function a(l,c){if(V.isUndefined(c)){if(!V.isUndefined(l))return n(void 0,l)}else return n(void 0,c)}function o(l,c,u){if(u in t)return n(l,c);if(u in e)return n(void 0,l)}const f={url:s,method:s,data:s,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:o,headers:(l,c,u)=>i(xf(l),xf(c),u,!0)};return V.forEach(Object.keys(Object.assign({},e,t)),function(c){const u=f[c]||i,h=u(e[c],t[c],c);V.isUndefined(h)&&u!==o||(r[c]=h)}),r}const Kc=e=>{const t=pn({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:i,xsrfCookieName:s,headers:a,auth:o}=t;t.headers=a=Ht.from(a),t.url=Hc(Gc(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),o&&a.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):"")));let f;if(V.isFormData(r)){if(At.hasStandardBrowserEnv||At.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((f=a.getContentType())!==!1){const[l,...c]=f?f.split(";").map(u=>u.trim()).filter(Boolean):[];a.setContentType([l||"multipart/form-data",...c].join("; "))}}if(At.hasStandardBrowserEnv&&(n&&V.isFunction(n)&&(n=n(t)),n||n!==!1&&Lm(t.url))){const l=i&&s&&Mm.read(s);l&&a.set(i,l)}return t},Hm=typeof XMLHttpRequest<"u",Wm=Hm&&function(e){return new Promise(function(r,n){const i=Kc(e);let s=i.data;const a=Ht.from(i.headers).normalize();let{responseType:o,onUploadProgress:f,onDownloadProgress:l}=i,c,u,h,d,x;function p(){d&&d(),x&&x(),i.cancelToken&&i.cancelToken.unsubscribe(c),i.signal&&i.signal.removeEventListener("abort",c)}let m=new XMLHttpRequest;m.open(i.method.toUpperCase(),i.url,!0),m.timeout=i.timeout;function C(){if(!m)return;const y=Ht.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),z={data:!o||o==="text"||o==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:y,config:e,request:m};$c(function(D){r(D),p()},function(D){n(D),p()},z),m=null}"onloadend"in m?m.onloadend=C:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(C)},m.onabort=function(){m&&(n(new Re("Request aborted",Re.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Re("Network Error",Re.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let R=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const z=i.transitional||Wc;i.timeoutErrorMessage&&(R=i.timeoutErrorMessage),n(new Re(R,z.clarifyTimeoutError?Re.ETIMEDOUT:Re.ECONNABORTED,e,m)),m=null},s===void 0&&a.setContentType(null),"setRequestHeader"in m&&V.forEach(a.toJSON(),function(R,z){m.setRequestHeader(z,R)}),V.isUndefined(i.withCredentials)||(m.withCredentials=!!i.withCredentials),o&&o!=="json"&&(m.responseType=i.responseType),l&&([h,x]=Os(l,!0),m.addEventListener("progress",h)),f&&m.upload&&([u,d]=Os(f),m.upload.addEventListener("progress",u),m.upload.addEventListener("loadend",d)),(i.cancelToken||i.signal)&&(c=y=>{m&&(n(!y||y.type?new Yn(null,e,m):y),m.abort(),m=null)},i.cancelToken&&i.cancelToken.subscribe(c),i.signal&&(i.signal.aborted?c():i.signal.addEventListener("abort",c)));const F=Nm(i.url);if(F&&At.protocols.indexOf(F)===-1){n(new Re("Unsupported protocol "+F+":",Re.ERR_BAD_REQUEST,e));return}m.send(s||null)})},Vm=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,i;const s=function(l){if(!i){i=!0,o();const c=l instanceof Error?l:this.reason;n.abort(c instanceof Re?c:new Yn(c instanceof Error?c.message:c))}};let a=t&&setTimeout(()=>{a=null,s(new Re(`timeout ${t} of ms exceeded`,Re.ETIMEDOUT))},t);const o=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(s):l.removeEventListener("abort",s)}),e=null)};e.forEach(l=>l.addEventListener("abort",s));const{signal:f}=n;return f.unsubscribe=()=>V.asap(o),f}},jm=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,i;for(;n<r;)i=n+t,yield e.slice(n,i),n=i},$m=async function*(e,t){for await(const r of Gm(e))yield*jm(r,t)},Gm=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},mf=(e,t,r,n)=>{const i=$m(e,t);let s=0,a,o=f=>{a||(a=!0,n&&n(f))};return new ReadableStream({async pull(f){try{const{done:l,value:c}=await i.next();if(l){o(),f.close();return}let u=c.byteLength;if(r){let h=s+=u;r(h)}f.enqueue(new Uint8Array(c))}catch(l){throw o(l),l}},cancel(f){return o(f),i.return()}},{highWaterMark:2})},ca=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Xc=ca&&typeof ReadableStream=="function",Km=ca&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),zc=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Xm=Xc&&zc(()=>{let e=!1;const t=new Request(At.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),gf=64*1024,ho=Xc&&zc(()=>V.isReadableStream(new Response("").body)),Rs={stream:ho&&(e=>e.body)};ca&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Rs[t]&&(Rs[t]=V.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new Re(`Response type '${t}' is not supported`,Re.ERR_NOT_SUPPORT,n)})})})(new Response);const zm=async e=>{if(e==null)return 0;if(V.isBlob(e))return e.size;if(V.isSpecCompliantForm(e))return(await new Request(At.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(V.isArrayBufferView(e)||V.isArrayBuffer(e))return e.byteLength;if(V.isURLSearchParams(e)&&(e=e+""),V.isString(e))return(await Km(e)).byteLength},Ym=async(e,t)=>{const r=V.toFiniteNumber(e.getContentLength());return r??zm(t)},qm=ca&&(async e=>{let{url:t,method:r,data:n,signal:i,cancelToken:s,timeout:a,onDownloadProgress:o,onUploadProgress:f,responseType:l,headers:c,withCredentials:u="same-origin",fetchOptions:h}=Kc(e);l=l?(l+"").toLowerCase():"text";let d=Vm([i,s&&s.toAbortSignal()],a),x;const p=d&&d.unsubscribe&&(()=>{d.unsubscribe()});let m;try{if(f&&Xm&&r!=="get"&&r!=="head"&&(m=await Ym(c,n))!==0){let z=new Request(t,{method:"POST",body:n,duplex:"half"}),se;if(V.isFormData(n)&&(se=z.headers.get("content-type"))&&c.setContentType(se),z.body){const[D,Y]=df(m,Os(pf(f)));n=mf(z.body,gf,D,Y)}}V.isString(u)||(u=u?"include":"omit");const C="credentials"in Request.prototype;x=new Request(t,{...h,signal:d,method:r.toUpperCase(),headers:c.normalize().toJSON(),body:n,duplex:"half",credentials:C?u:void 0});let F=await fetch(x);const y=ho&&(l==="stream"||l==="response");if(ho&&(o||y&&p)){const z={};["status","statusText","headers"].forEach(M=>{z[M]=F[M]});const se=V.toFiniteNumber(F.headers.get("content-length")),[D,Y]=o&&df(se,Os(pf(o),!0))||[];F=new Response(mf(F.body,gf,D,()=>{Y&&Y(),p&&p()}),z)}l=l||"text";let R=await Rs[V.findKey(Rs,l)||"text"](F,e);return!y&&p&&p(),await new Promise((z,se)=>{$c(z,se,{data:R,headers:Ht.from(F.headers),status:F.status,statusText:F.statusText,config:e,request:x})})}catch(C){throw p&&p(),C&&C.name==="TypeError"&&/Load failed|fetch/i.test(C.message)?Object.assign(new Re("Network Error",Re.ERR_NETWORK,e,x),{cause:C.cause||C}):Re.from(C,C&&C.code,e,x)}}),po={http:um,xhr:Wm,fetch:qm};V.forEach(po,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const vf=e=>`- ${e}`,Jm=e=>V.isFunction(e)||e===null||e===!1,Yc={getAdapter:e=>{e=V.isArray(e)?e:[e];const{length:t}=e;let r,n;const i={};for(let s=0;s<t;s++){r=e[s];let a;if(n=r,!Jm(r)&&(n=po[(a=String(r)).toLowerCase()],n===void 0))throw new Re(`Unknown adapter '${a}'`);if(n)break;i[a||"#"+s]=n}if(!n){const s=Object.entries(i).map(([o,f])=>`adapter ${o} `+(f===!1?"is not supported by the environment":"is not available in the build"));let a=t?s.length>1?`since :
`+s.map(vf).join(`
`):" "+vf(s[0]):"as no adapter specified";throw new Re("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return n},adapters:po};function Ma(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Yn(null,e)}function _f(e){return Ma(e),e.headers=Ht.from(e.headers),e.data=La.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Yc.getAdapter(e.adapter||Gi.adapter)(e).then(function(n){return Ma(e),n.data=La.call(e,e.transformResponse,n),n.headers=Ht.from(n.headers),n},function(n){return jc(n)||(Ma(e),n&&n.response&&(n.response.data=La.call(e,e.transformResponse,n.response),n.response.headers=Ht.from(n.response.headers))),Promise.reject(n)})}const qc="1.9.0",ua={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ua[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Ef={};ua.transitional=function(t,r,n){function i(s,a){return"[Axios v"+qc+"] Transitional option '"+s+"'"+a+(n?". "+n:"")}return(s,a,o)=>{if(t===!1)throw new Re(i(a," has been removed"+(r?" in "+r:"")),Re.ERR_DEPRECATED);return r&&!Ef[a]&&(Ef[a]=!0,console.warn(i(a," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(s,a,o):!0}};ua.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function Zm(e,t,r){if(typeof e!="object")throw new Re("options must be an object",Re.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let i=n.length;for(;i-- >0;){const s=n[i],a=t[s];if(a){const o=e[s],f=o===void 0||a(o,s,e);if(f!==!0)throw new Re("option "+s+" must be "+f,Re.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new Re("Unknown option "+s,Re.ERR_BAD_OPTION)}}const _s={assertOptions:Zm,validators:ua},gr=_s.validators;let hn=class{constructor(t){this.defaults=t||{},this.interceptors={request:new uf,response:new uf}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const s=i.stack?i.stack.replace(/^.+\n/,""):"";try{n.stack?s&&!String(n.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+s):n.stack=s}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=pn(this.defaults,r);const{transitional:n,paramsSerializer:i,headers:s}=r;n!==void 0&&_s.assertOptions(n,{silentJSONParsing:gr.transitional(gr.boolean),forcedJSONParsing:gr.transitional(gr.boolean),clarifyTimeoutError:gr.transitional(gr.boolean)},!1),i!=null&&(V.isFunction(i)?r.paramsSerializer={serialize:i}:_s.assertOptions(i,{encode:gr.function,serialize:gr.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),_s.assertOptions(r,{baseUrl:gr.spelling("baseURL"),withXsrfToken:gr.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let a=s&&V.merge(s.common,s[r.method]);s&&V.forEach(["delete","get","head","post","put","patch","common"],x=>{delete s[x]}),r.headers=Ht.concat(a,s);const o=[];let f=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(r)===!1||(f=f&&p.synchronous,o.unshift(p.fulfilled,p.rejected))});const l=[];this.interceptors.response.forEach(function(p){l.push(p.fulfilled,p.rejected)});let c,u=0,h;if(!f){const x=[_f.bind(this),void 0];for(x.unshift.apply(x,o),x.push.apply(x,l),h=x.length,c=Promise.resolve(r);u<h;)c=c.then(x[u++],x[u++]);return c}h=o.length;let d=r;for(u=0;u<h;){const x=o[u++],p=o[u++];try{d=x(d)}catch(m){p.call(this,m);break}}try{c=_f.call(this,d)}catch(x){return Promise.reject(x)}for(u=0,h=l.length;u<h;)c=c.then(l[u++],l[u++]);return c}getUri(t){t=pn(this.defaults,t);const r=Gc(t.baseURL,t.url,t.allowAbsoluteUrls);return Hc(r,t.params,t.paramsSerializer)}};V.forEach(["delete","get","head","options"],function(t){hn.prototype[t]=function(r,n){return this.request(pn(n||{},{method:t,url:r,data:(n||{}).data}))}});V.forEach(["post","put","patch"],function(t){function r(n){return function(s,a,o){return this.request(pn(o||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:s,data:a}))}}hn.prototype[t]=r(),hn.prototype[t+"Form"]=r(!0)});let Qm=class Jc{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(s){r=s});const n=this;this.promise.then(i=>{if(!n._listeners)return;let s=n._listeners.length;for(;s-- >0;)n._listeners[s](i);n._listeners=null}),this.promise.then=i=>{let s;const a=new Promise(o=>{n.subscribe(o),s=o}).then(i);return a.cancel=function(){n.unsubscribe(s)},a},t(function(s,a,o){n.reason||(n.reason=new Yn(s,a,o),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Jc(function(i){t=i}),cancel:t}}};function eg(e){return function(r){return e.apply(null,r)}}function tg(e){return V.isObject(e)&&e.isAxiosError===!0}const xo={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(xo).forEach(([e,t])=>{xo[t]=e});function Zc(e){const t=new hn(e),r=Oc(hn.prototype.request,t);return V.extend(r,hn.prototype,t,{allOwnKeys:!0}),V.extend(r,t,null,{allOwnKeys:!0}),r.create=function(i){return Zc(pn(e,i))},r}const Ze=Zc(Gi);Ze.Axios=hn;Ze.CanceledError=Yn;Ze.CancelToken=Qm;Ze.isCancel=jc;Ze.VERSION=qc;Ze.toFormData=la;Ze.AxiosError=Re;Ze.Cancel=Ze.CanceledError;Ze.all=function(t){return Promise.all(t)};Ze.spread=eg;Ze.isAxiosError=tg;Ze.mergeConfig=pn;Ze.AxiosHeaders=Ht;Ze.formToJSON=e=>Vc(V.isHTMLForm(e)?new FormData(e):e);Ze.getAdapter=Yc.getAdapter;Ze.HttpStatusCode=xo;Ze.default=Ze;const{Axios:Z4,AxiosError:Q4,CanceledError:ey,isCancel:ty,CancelToken:ry,VERSION:ny,all:iy,Cancel:sy,isAxiosError:ay,spread:oy,toFormData:fy,AxiosHeaders:ly,HttpStatusCode:cy,formToJSON:uy,getAdapter:hy,mergeConfig:dy}=Ze,rg="/assets/bg-D4rG9oRZ.png",ng={name:"UserLogin",data(){return{wechatId:"",hasError:!1,errorMessage:"",isSubmitting:!1}},methods:{validateWechatId(e){return e.trim()?null:"请输入内容"},clearError(){this.hasError=!1,this.errorMessage=""},async handleSubmit(){var t;const e=this.validateWechatId(this.wechatId);if(e){this.hasError=!0,this.errorMessage=e;return}this.isSubmitting=!0;try{console.log("提交微信号:",this.wechatId.trim());const r=await Ze.post("/api/wechat",{wechatId:this.wechatId.trim()},{headers:{"Content-Type":"application/json"},timeout:1e4});console.log("提交响应:",r.data),r.data.success?this.$router.push("/welcome").catch(n=>{console.error("路由跳转失败:",n)}):(this.hasError=!0,this.errorMessage=r.data.message||"提交失败，请重试")}catch(r){if(console.error("提交微信号失败:",r),this.hasError=!0,r.code==="ECONNABORTED")this.errorMessage="请求超时，请检查网络连接";else if(r.response){const n=r.response.status,i=(t=r.response.data)==null?void 0:t.message;n===400?this.errorMessage=i||"输入数据有误":n===500?this.errorMessage="服务器内部错误，请稍后重试":this.errorMessage=i||`提交失败 (${n})`}else r.request?this.errorMessage="无法连接到服务器，请检查后端服务是否启动":this.errorMessage="提交失败，请重试"}finally{this.isSubmitting=!1}}}},ig={class:"user-login"},sg={class:"container"},ag={class:"login-card"},og={class:"input-group"},fg={key:0,class:"error-message"},lg=["disabled"],cg={class:"admin-link"};function ug(e,t,r,n,i,s){const a=bo("router-link");return Bt(),zt("div",ig,[we("div",sg,[we("div",ag,[t[4]||(t[4]=we("div",{class:"bg-image"},[we("img",{src:rg,alt:"背景图片"})],-1)),t[5]||(t[5]=we("h1",{class:"title"},"欢迎使用本科技，姬霓太美调音科技",-1)),we("form",{onSubmit:t[2]||(t[2]=pc((...o)=>s.handleSubmit&&s.handleSubmit(...o),["prevent"])),class:"login-form"},[we("div",og,[Za(we("input",{"onUpdate:modelValue":t[0]||(t[0]=o=>i.wechatId=o),type:"text",placeholder:"请输入手机号或者vx号注册",class:jn(["input-field",{error:i.hasError}]),onInput:t[1]||(t[1]=(...o)=>s.clearError&&s.clearError(...o))},null,34),[[so,i.wechatId]]),i.hasError?(Bt(),zt("div",fg,Mt(i.errorMessage),1)):lc("",!0)]),we("button",{type:"submit",class:"login-btn",disabled:i.isSubmitting},Mt(i.isSubmitting?"提交中...":"注册"),9,lg)],32),we("div",cg,[vt(a,{to:"/admin",class:"admin-text"},{default:Io(()=>t[3]||(t[3]=[Mo("管理员入口")])),_:1,__:[3]})])])])])}const hg=ji(ng,[["render",ug],["__scopeId","data-v-fe4648a7"]]),dg={name:"Welcome",data(){return{countdown:2,timer:null,targetUrl:"http://shengka.nfdrl.cn"}},computed:{progressWidth(){return(2-this.countdown)/2*100}},mounted(){this.startCountdown()},beforeUnmount(){this.timer&&clearInterval(this.timer)},methods:{startCountdown(){this.timer=setInterval(()=>{this.countdown--,this.countdown<=0&&(clearInterval(this.timer),this.redirectToTarget())},1e3)},redirectToTarget(){console.log("跳转到目标网址:",this.targetUrl),window.location.href=this.targetUrl},redirectNow(){this.timer&&clearInterval(this.timer),this.redirectToTarget()}}},pg={class:"welcome"},xg={class:"container"},mg={class:"welcome-card"},gg={class:"redirect-info"},vg={class:"redirect-message"},_g={class:"progress-bar"},Eg={class:"actions"};function wg(e,t,r,n,i,s){return Bt(),zt("div",pg,[we("div",xg,[we("div",mg,[t[1]||(t[1]=bd('<div class="success-icon" data-v-3871c8de><svg width="80" height="80" viewBox="0 0 24 24" fill="none" data-v-3871c8de><circle cx="12" cy="12" r="10" stroke="#4CAF50" stroke-width="2" data-v-3871c8de></circle><path d="m9 12 2 2 4-4" stroke="#4CAF50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-3871c8de></path></svg></div><h1 class="welcome-title" data-v-3871c8de>欢迎你！</h1><p class="welcome-message" data-v-3871c8de>注册成功</p>',3)),we("div",gg,[we("p",vg,Mt(i.countdown>0?"自动打开软件...":"正在跳转..."),1),we("div",_g,[we("div",{class:"progress-fill",style:qs({width:s.progressWidth+"%"})},null,4)])]),we("div",Eg,[we("button",{onClick:t[0]||(t[0]=(...a)=>s.redirectNow&&s.redirectNow(...a)),class:"redirect-btn"},"打开软件")])])])])}const Tg=ji(dg,[["render",wg],["__scopeId","data-v-3871c8de"]]),Sg={name:"AdminLogin",data(){return{username:"",password:"",hasError:!1,errorMessage:"",isLogging:!1}},methods:{clearError(){this.hasError=!1,this.errorMessage=""},async handleLogin(){var e;if(!this.username.trim()||!this.password.trim()){this.hasError=!0,this.errorMessage="请输入用户名和密码";return}this.isLogging=!0;try{console.log("发送登录请求...");const t=await Ze.post("/api/admin/login",{username:this.username.trim(),password:this.password.trim()},{headers:{"Content-Type":"application/json"},timeout:1e4});console.log("登录响应:",t.data),t.data.success?(localStorage.setItem("adminLoggedIn","true"),this.$router.push("/admin/dashboard").catch(r=>{console.error("路由跳转失败:",r)})):(this.hasError=!0,this.errorMessage=t.data.message||"用户名或密码错误")}catch(t){if(console.error("管理员登录失败:",t),this.hasError=!0,t.code==="ECONNABORTED")this.errorMessage="请求超时，请检查网络连接";else if(t.response){const r=t.response.status,n=(e=t.response.data)==null?void 0:e.message;r===401?this.errorMessage=n||"用户名或密码错误":r===500?this.errorMessage="服务器内部错误，请稍后重试":this.errorMessage=n||`请求失败 (${r})`}else t.request?this.errorMessage="无法连接到服务器，请检查后端服务是否启动":this.errorMessage="登录失败，请重试"}finally{this.isLogging=!1}}}},yg={class:"admin-login"},Ag={class:"container"},Fg={class:"login-card"},Cg={class:"input-group"},Og={class:"input-group"},Rg={key:0,class:"error-message"},Pg=["disabled"],Dg={class:"back-link"};function Ig(e,t,r,n,i,s){const a=bo("router-link");return Bt(),zt("div",yg,[we("div",Ag,[we("div",Fg,[t[5]||(t[5]=we("h1",{class:"title"},"管理员登录",-1)),we("form",{onSubmit:t[3]||(t[3]=pc((...o)=>s.handleLogin&&s.handleLogin(...o),["prevent"])),class:"login-form"},[we("div",Cg,[Za(we("input",{"onUpdate:modelValue":t[0]||(t[0]=o=>i.username=o),type:"text",placeholder:"用户名",class:jn(["input-field",{error:i.hasError}])},null,2),[[so,i.username]])]),we("div",Og,[Za(we("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>i.password=o),type:"password",placeholder:"密码",class:jn(["input-field",{error:i.hasError}]),onInput:t[2]||(t[2]=(...o)=>s.clearError&&s.clearError(...o))},null,34),[[so,i.password]]),i.hasError?(Bt(),zt("div",Rg,Mt(i.errorMessage),1)):lc("",!0)]),we("button",{type:"submit",class:"login-btn",disabled:i.isLogging},Mt(i.isLogging?"登录中...":"登录"),9,Pg)],32),we("div",Dg,[vt(a,{to:"/",class:"back-text"},{default:Io(()=>t[4]||(t[4]=[Mo("返回用户页面")])),_:1,__:[4]})])])])])}const Ng=ji(Sg,[["render",Ig],["__scopeId","data-v-ba345abb"]]);/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var Ps={};Ps.version="0.18.5";var Qc=1252,bg=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],eu=function(e){bg.indexOf(e)!=-1&&(Qc=e)};function kg(){eu(1252)}var bi=function(e){eu(e)};function Lg(){bi(1200),kg()}var ns=function(t){return String.fromCharCode(t)},wf=function(t){return String.fromCharCode(t)},Ds,zr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function ki(e){for(var t="",r=0,n=0,i=0,s=0,a=0,o=0,f=0,l=0;l<e.length;)r=e.charCodeAt(l++),s=r>>2,n=e.charCodeAt(l++),a=(r&3)<<4|n>>4,i=e.charCodeAt(l++),o=(n&15)<<2|i>>6,f=i&63,isNaN(n)?o=f=64:isNaN(i)&&(f=64),t+=zr.charAt(s)+zr.charAt(a)+zr.charAt(o)+zr.charAt(f);return t}function Br(e){var t="",r=0,n=0,i=0,s=0,a=0,o=0,f=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)s=zr.indexOf(e.charAt(l++)),a=zr.indexOf(e.charAt(l++)),r=s<<2|a>>4,t+=String.fromCharCode(r),o=zr.indexOf(e.charAt(l++)),n=(a&15)<<4|o>>2,o!==64&&(t+=String.fromCharCode(n)),f=zr.indexOf(e.charAt(l++)),i=(o&3)<<6|f,f!==64&&(t+=String.fromCharCode(i));return t}var Le=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),Wr=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function xn(e){return Le?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function Tf(e){return Le?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var cr=function(t){return Le?Wr(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function ha(e){if(typeof ArrayBuffer>"u")return cr(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=e.charCodeAt(n)&255;return t}function Ki(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function Mg(e){if(typeof Uint8Array>"u")throw new Error("Unsupported");return new Uint8Array(e)}var mt=Le?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:Wr(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),i=0;for(t=0,r=0;t<e.length;r+=i,++t)if(i=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map(function(s){return Array.isArray(s)?s:[].slice.call(s)}))};function Bg(e){for(var t=[],r=0,n=e.length+250,i=xn(e.length+255),s=0;s<e.length;++s){var a=e.charCodeAt(s);if(a<128)i[r++]=a;else if(a<2048)i[r++]=192|a>>6&31,i[r++]=128|a&63;else if(a>=55296&&a<57344){a=(a&1023)+64;var o=e.charCodeAt(++s)&1023;i[r++]=240|a>>8&7,i[r++]=128|a>>2&63,i[r++]=128|o>>6&15|(a&3)<<4,i[r++]=128|o&63}else i[r++]=224|a>>12&15,i[r++]=128|a>>6&63,i[r++]=128|a&63;r>n&&(t.push(i.slice(0,r)),r=0,i=xn(65535),n=65530)}return t.push(i.slice(0,r)),mt(t)}var Ei=/\u0000/g,is=/[\u0001-\u0006]/g;function Un(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function ur(e,t){var r=""+e;return r.length>=t?r:tt("0",t-r.length)+r}function $o(e,t){var r=""+e;return r.length>=t?r:tt(" ",t-r.length)+r}function Is(e,t){var r=""+e;return r.length>=t?r:r+tt(" ",t-r.length)}function Ug(e,t){var r=""+Math.round(e);return r.length>=t?r:tt("0",t-r.length)+r}function Hg(e,t){var r=""+e;return r.length>=t?r:tt("0",t-r.length)+r}var Sf=Math.pow(2,32);function An(e,t){if(e>Sf||e<-Sf)return Ug(e,t);var r=Math.round(e);return Hg(r,t)}function Ns(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var yf=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Ba=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Wg(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var rt={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},Af={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Vg={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function bs(e,t,r){for(var n=e<0?-1:1,i=e*n,s=0,a=1,o=0,f=1,l=0,c=0,u=Math.floor(i);l<t&&(u=Math.floor(i),o=u*a+s,c=u*l+f,!(i-u<5e-8));)i=1/(i-u),s=a,a=o,f=l,l=c;if(c>t&&(l>t?(c=f,o=s):(c=l,o=a)),!r)return[0,n*o,c];var h=Math.floor(n*o/c);return[h,n*o-h*c,c]}function ss(e,t,r){if(e>2958465||e<0)return null;var n=e|0,i=Math.floor(86400*(e-n)),s=0,a=[],o={D:n,T:i,u:86400*(e-n)-i,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(n+=1462),o.u>.9999&&(o.u=0,++i==86400&&(o.T=i=0,++n,++o.D)),n===60)a=r?[1317,10,29]:[1900,2,29],s=3;else if(n===0)a=r?[1317,8,29]:[1900,1,0],s=6;else{n>60&&--n;var f=new Date(1900,0,1);f.setDate(f.getDate()+n-1),a=[f.getFullYear(),f.getMonth()+1,f.getDate()],s=f.getDay(),n<60&&(s=(s+6)%7),r&&(s=Yg(f,a))}return o.y=a[0],o.m=a[1],o.d=a[2],o.S=i%60,i=Math.floor(i/60),o.M=i%60,i=Math.floor(i/60),o.H=i,o.q=s,o}var tu=new Date(1899,11,31,0,0,0),jg=tu.getTime(),$g=new Date(1900,2,1,0,0,0);function ru(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=$g&&(r+=24*60*60*1e3),(r-(jg+(e.getTimezoneOffset()-tu.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function Go(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Gg(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function Kg(e){var t=e<0?12:11,r=Go(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Xg(e){var t=Go(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function zg(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=Kg(e):t===10?r=e.toFixed(10).substr(0,12):r=Xg(e),Go(Gg(r.toUpperCase()))}function mo(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):zg(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return Qr(14,ru(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Yg(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function qg(e,t,r,n){var i="",s=0,a=0,o=r.y,f,l=0;switch(e){case 98:o=r.y+543;case 121:switch(t.length){case 1:case 2:f=o%100,l=2;break;default:f=o%1e4,l=4;break}break;case 109:switch(t.length){case 1:case 2:f=r.m,l=t.length;break;case 3:return Ba[r.m-1][1];case 5:return Ba[r.m-1][0];default:return Ba[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:f=r.d,l=t.length;break;case 3:return yf[r.q][0];default:return yf[r.q][1]}break;case 104:switch(t.length){case 1:case 2:f=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:f=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:f=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?ur(r.S,t.length):(n>=2?a=n===3?1e3:100:a=n===1?10:1,s=Math.round(a*(r.S+r.u)),s>=60*a&&(s=0),t==="s"?s===0?"0":""+s/a:(i=ur(s,2+n),t==="ss"?i.substr(0,2):"."+i.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":f=r.D*24+r.H;break;case"[m]":case"[mm]":f=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":f=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=t.length===3?1:2;break;case 101:f=o,l=1;break}var c=l>0?ur(f,l):"";return c}function Yr(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var nu=/%/g;function Jg(e,t,r){var n=t.replace(nu,""),i=t.length-n.length;return Nr(e,n,r*Math.pow(10,2*i))+tt("%",i)}function Zg(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Nr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function iu(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+iu(e,-t);var i=e.indexOf(".");i===-1&&(i=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%i;if(s<0&&(s+=i),r=(t/Math.pow(10,s)).toPrecision(n+1+(i+s)%i),r.indexOf("e")===-1){var a=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(a-r.length+s):r+="E+"+(a-s);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,i)+"."+r.substr(2+i),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(o,f,l,c){return f+l+c.substr(0,(i+s)%i)+"."+c.substr(s)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var su=/# (\?+)( ?)\/( ?)(\d+)/;function Qg(e,t,r){var n=parseInt(e[4],10),i=Math.round(t*n),s=Math.floor(i/n),a=i-s*n,o=n;return r+(s===0?"":""+s)+" "+(a===0?tt(" ",e[1].length+1+e[4].length):$o(a,e[1].length)+e[2]+"/"+e[3]+ur(o,e[4].length))}function ev(e,t,r){return r+(t===0?"":""+t)+tt(" ",e[1].length+2+e[4].length)}var au=/^#*0*\.([0#]+)/,ou=/\).*[0#]/,fu=/\(###\) ###\\?-####/;function It(e){for(var t="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function Ff(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function Cf(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function tv(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function rv(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function rr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(ou)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?rr("n",n,r):"("+rr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Zg(e,t,r);if(t.indexOf("%")!==-1)return Jg(e,t,r);if(t.indexOf("E")!==-1)return iu(t,r);if(t.charCodeAt(0)===36)return"$"+rr(e,t.substr(t.charAt(1)==" "?2:1),r);var i,s,a,o,f=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+An(f,t.length);if(t.match(/^[#?]+$/))return i=An(r,0),i==="0"&&(i=""),i.length>t.length?i:It(t.substr(0,t.length-i.length))+i;if(s=t.match(su))return Qg(s,f,l);if(t.match(/^#+0+$/))return l+An(f,t.length-t.indexOf("0"));if(s=t.match(au))return i=Ff(r,s[1].length).replace(/^([^\.]+)$/,"$1."+It(s[1])).replace(/\.$/,"."+It(s[1])).replace(/\.(\d*)$/,function(x,p){return"."+p+tt("0",It(s[1]).length-p.length)}),t.indexOf("0.")!==-1?i:i.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+Ff(f,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+Yr(An(f,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+rr(e,t,-r):Yr(""+(Math.floor(r)+tv(r,s[1].length)))+"."+ur(Cf(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return rr(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return i=Un(rr(e,t.replace(/[\\-]/g,""),r)),a=0,Un(Un(t.replace(/\\/g,"")).replace(/[0#]/g,function(x){return a<i.length?i.charAt(a++):x==="0"?"0":""}));if(t.match(fu))return i=rr(e,"##########",r),"("+i.substr(0,3)+") "+i.substr(3,3)+"-"+i.substr(6);var c="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return a=Math.min(s[4].length,7),o=bs(f,Math.pow(10,a)-1,!1),i=""+l,c=Nr("n",s[1],o[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),i+=c+s[2]+"/"+s[3],c=Is(o[2],a),c.length<s[4].length&&(c=It(s[4].substr(s[4].length-c.length))+c),i+=c,i;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return a=Math.min(Math.max(s[1].length,s[4].length),7),o=bs(f,Math.pow(10,a)-1,!0),l+(o[0]||(o[1]?"":"0"))+" "+(o[1]?$o(o[1],a)+s[2]+"/"+s[3]+Is(o[2],a):tt(" ",2*a+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return i=An(r,0),t.length<=i.length?i:It(t.substr(0,t.length-i.length))+i;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){i=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),a=i.indexOf(".");var u=t.indexOf(".")-a,h=t.length-i.length-u;return It(t.substr(0,u)+i+t.substr(t.length-h))}if(s=t.match(/^00,000\.([#0]*0)$/))return a=Cf(r,s[1].length),r<0?"-"+rr(e,t,-r):Yr(rv(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(x){return"00,"+(x.length<3?ur(0,3-x.length):"")+x})+"."+ur(a,s[1].length);switch(t){case"###,##0.00":return rr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=Yr(An(f,0));return d!=="0"?l+d:"";case"###,###.00":return rr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return rr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function nv(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Nr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function iv(e,t,r){var n=t.replace(nu,""),i=t.length-n.length;return Nr(e,n,r*Math.pow(10,2*i))+tt("%",i)}function lu(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+lu(e,-t);var i=e.indexOf(".");i===-1&&(i=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%i;if(s<0&&(s+=i),r=(t/Math.pow(10,s)).toPrecision(n+1+(i+s)%i),!r.match(/[Ee]/)){var a=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(a-r.length+s):r+="E+"+(a-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(o,f,l,c){return f+l+c.substr(0,(i+s)%i)+"."+c.substr(s)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function vr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(ou)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?vr("n",n,r):"("+vr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return nv(e,t,r);if(t.indexOf("%")!==-1)return iv(e,t,r);if(t.indexOf("E")!==-1)return lu(t,r);if(t.charCodeAt(0)===36)return"$"+vr(e,t.substr(t.charAt(1)==" "?2:1),r);var i,s,a,o,f=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+ur(f,t.length);if(t.match(/^[#?]+$/))return i=""+r,r===0&&(i=""),i.length>t.length?i:It(t.substr(0,t.length-i.length))+i;if(s=t.match(su))return ev(s,f,l);if(t.match(/^#+0+$/))return l+ur(f,t.length-t.indexOf("0"));if(s=t.match(au))return i=(""+r).replace(/^([^\.]+)$/,"$1."+It(s[1])).replace(/\.$/,"."+It(s[1])),i=i.replace(/\.(\d*)$/,function(x,p){return"."+p+tt("0",It(s[1]).length-p.length)}),t.indexOf("0.")!==-1?i:i.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+(""+f).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+Yr(""+f);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+vr(e,t,-r):Yr(""+r)+"."+tt("0",s[1].length);if(s=t.match(/^#,#*,#0/))return vr(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return i=Un(vr(e,t.replace(/[\\-]/g,""),r)),a=0,Un(Un(t.replace(/\\/g,"")).replace(/[0#]/g,function(x){return a<i.length?i.charAt(a++):x==="0"?"0":""}));if(t.match(fu))return i=vr(e,"##########",r),"("+i.substr(0,3)+") "+i.substr(3,3)+"-"+i.substr(6);var c="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return a=Math.min(s[4].length,7),o=bs(f,Math.pow(10,a)-1,!1),i=""+l,c=Nr("n",s[1],o[1]),c.charAt(c.length-1)==" "&&(c=c.substr(0,c.length-1)+"0"),i+=c+s[2]+"/"+s[3],c=Is(o[2],a),c.length<s[4].length&&(c=It(s[4].substr(s[4].length-c.length))+c),i+=c,i;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return a=Math.min(Math.max(s[1].length,s[4].length),7),o=bs(f,Math.pow(10,a)-1,!0),l+(o[0]||(o[1]?"":"0"))+" "+(o[1]?$o(o[1],a)+s[2]+"/"+s[3]+Is(o[2],a):tt(" ",2*a+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return i=""+r,t.length<=i.length?i:It(t.substr(0,t.length-i.length))+i;if(s=t.match(/^([#0]+)\.([#0]+)$/)){i=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),a=i.indexOf(".");var u=t.indexOf(".")-a,h=t.length-i.length-u;return It(t.substr(0,u)+i+t.substr(t.length-h))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+vr(e,t,-r):Yr(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(x){return"00,"+(x.length<3?ur(0,3-x.length):"")+x})+"."+ur(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=Yr(""+f);return d!=="0"?l+d:"";default:if(t.match(/\.[0#?]*$/))return vr(e,t.slice(0,t.lastIndexOf(".")),r)+It(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Nr(e,t,r){return(r|0)===r?vr(e,t,r):rr(e,t,r)}function sv(e){for(var t=[],r=!1,n=0,i=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(i,n-i),i=n+1}if(t[t.length]=e.substr(i),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var cu=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function uu(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":Ns(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(n=r;e.charAt(t++)!=="]"&&t<e.length;)n+=e.charAt(t);if(n.match(cu))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function av(e,t,r,n){for(var i=[],s="",a=0,o="",f="t",l,c,u,h="H";a<e.length;)switch(o=e.charAt(a)){case"G":if(!Ns(e,a))throw new Error("unrecognized character "+o+" in "+e);i[i.length]={t:"G",v:"General"},a+=7;break;case'"':for(s="";(u=e.charCodeAt(++a))!==34&&a<e.length;)s+=String.fromCharCode(u);i[i.length]={t:"t",v:s},++a;break;case"\\":var d=e.charAt(++a),x=d==="("||d===")"?d:"t";i[i.length]={t:x,v:d},++a;break;case"_":i[i.length]={t:"t",v:" "},a+=2;break;case"@":i[i.length]={t:"T",v:t},++a;break;case"B":case"b":if(e.charAt(a+1)==="1"||e.charAt(a+1)==="2"){if(l==null&&(l=ss(t,r,e.charAt(a+1)==="2"),l==null))return"";i[i.length]={t:"X",v:e.substr(a,2)},f=o,a+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":o=o.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||l==null&&(l=ss(t,r),l==null))return"";for(s=o;++a<e.length&&e.charAt(a).toLowerCase()===o;)s+=o;o==="m"&&f.toLowerCase()==="h"&&(o="M"),o==="h"&&(o=h),i[i.length]={t:o,v:s},f=o;break;case"A":case"a":case"上":var p={t:o,v:o};if(l==null&&(l=ss(t,r)),e.substr(a,3).toUpperCase()==="A/P"?(l!=null&&(p.v=l.H>=12?"P":"A"),p.t="T",h="h",a+=3):e.substr(a,5).toUpperCase()==="AM/PM"?(l!=null&&(p.v=l.H>=12?"PM":"AM"),p.t="T",a+=5,h="h"):e.substr(a,5).toUpperCase()==="上午/下午"?(l!=null&&(p.v=l.H>=12?"下午":"上午"),p.t="T",a+=5,h="h"):(p.t="t",++a),l==null&&p.t==="T")return"";i[i.length]=p,f=o;break;case"[":for(s=o;e.charAt(a++)!=="]"&&a<e.length;)s+=e.charAt(a);if(s.slice(-1)!=="]")throw'unterminated "[" block: |'+s+"|";if(s.match(cu)){if(l==null&&(l=ss(t,r),l==null))return"";i[i.length]={t:"Z",v:s.toLowerCase()},f=s.charAt(1)}else s.indexOf("$")>-1&&(s=(s.match(/\$([^-\[\]]*)/)||[])[1]||"$",uu(e)||(i[i.length]={t:"t",v:s}));break;case".":if(l!=null){for(s=o;++a<e.length&&(o=e.charAt(a))==="0";)s+=o;i[i.length]={t:"s",v:s};break}case"0":case"#":for(s=o;++a<e.length&&"0#?.,E+-%".indexOf(o=e.charAt(a))>-1;)s+=o;i[i.length]={t:"n",v:s};break;case"?":for(s=o;e.charAt(++a)===o;)s+=o;i[i.length]={t:o,v:s},f=o;break;case"*":++a,(e.charAt(a)==" "||e.charAt(a)=="*")&&++a;break;case"(":case")":i[i.length]={t:n===1?"t":o,v:o},++a;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(s=o;a<e.length&&"0123456789".indexOf(e.charAt(++a))>-1;)s+=e.charAt(a);i[i.length]={t:"D",v:s};break;case" ":i[i.length]={t:o,v:o},++a;break;case"$":i[i.length]={t:"t",v:"$"},++a;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(o)===-1)throw new Error("unrecognized character "+o+" in "+e);i[i.length]={t:"t",v:o},++a;break}var m=0,C=0,F;for(a=i.length-1,f="t";a>=0;--a)switch(i[a].t){case"h":case"H":i[a].t=h,f="h",m<1&&(m=1);break;case"s":(F=i[a].v.match(/\.0+$/))&&(C=Math.max(C,F[0].length-1)),m<3&&(m=3);case"d":case"y":case"M":case"e":f=i[a].t;break;case"m":f==="s"&&(i[a].t="M",m<2&&(m=2));break;case"X":break;case"Z":m<1&&i[a].v.match(/[Hh]/)&&(m=1),m<2&&i[a].v.match(/[Mm]/)&&(m=2),m<3&&i[a].v.match(/[Ss]/)&&(m=3)}switch(m){case 0:break;case 1:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M),l.M>=60&&(l.M=0,++l.H);break;case 2:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M);break}var y="",R;for(a=0;a<i.length;++a)switch(i[a].t){case"t":case"T":case" ":case"D":break;case"X":i[a].v="",i[a].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":i[a].v=qg(i[a].t.charCodeAt(0),i[a].v,l,C),i[a].t="t";break;case"n":case"?":for(R=a+1;i[R]!=null&&((o=i[R].t)==="?"||o==="D"||(o===" "||o==="t")&&i[R+1]!=null&&(i[R+1].t==="?"||i[R+1].t==="t"&&i[R+1].v==="/")||i[a].t==="("&&(o===" "||o==="n"||o===")")||o==="t"&&(i[R].v==="/"||i[R].v===" "&&i[R+1]!=null&&i[R+1].t=="?"));)i[a].v+=i[R].v,i[R]={v:"",t:";"},++R;y+=i[a].v,a=R-1;break;case"G":i[a].t="t",i[a].v=mo(t,r);break}var z="",se,D;if(y.length>0){y.charCodeAt(0)==40?(se=t<0&&y.charCodeAt(0)===45?-t:t,D=Nr("n",y,se)):(se=t<0&&n>1?-t:t,D=Nr("n",y,se),se<0&&i[0]&&i[0].t=="t"&&(D=D.substr(1),i[0].v="-"+i[0].v)),R=D.length-1;var Y=i.length;for(a=0;a<i.length;++a)if(i[a]!=null&&i[a].t!="t"&&i[a].v.indexOf(".")>-1){Y=a;break}var M=i.length;if(Y===i.length&&D.indexOf("E")===-1){for(a=i.length-1;a>=0;--a)i[a]==null||"n?".indexOf(i[a].t)===-1||(R>=i[a].v.length-1?(R-=i[a].v.length,i[a].v=D.substr(R+1,i[a].v.length)):R<0?i[a].v="":(i[a].v=D.substr(0,R+1),R=-1),i[a].t="t",M=a);R>=0&&M<i.length&&(i[M].v=D.substr(0,R+1)+i[M].v)}else if(Y!==i.length&&D.indexOf("E")===-1){for(R=D.indexOf(".")-1,a=Y;a>=0;--a)if(!(i[a]==null||"n?".indexOf(i[a].t)===-1)){for(c=i[a].v.indexOf(".")>-1&&a===Y?i[a].v.indexOf(".")-1:i[a].v.length-1,z=i[a].v.substr(c+1);c>=0;--c)R>=0&&(i[a].v.charAt(c)==="0"||i[a].v.charAt(c)==="#")&&(z=D.charAt(R--)+z);i[a].v=z,i[a].t="t",M=a}for(R>=0&&M<i.length&&(i[M].v=D.substr(0,R+1)+i[M].v),R=D.indexOf(".")+1,a=Y;a<i.length;++a)if(!(i[a]==null||"n?(".indexOf(i[a].t)===-1&&a!==Y)){for(c=i[a].v.indexOf(".")>-1&&a===Y?i[a].v.indexOf(".")+1:0,z=i[a].v.substr(0,c);c<i[a].v.length;++c)R<D.length&&(z+=D.charAt(R++));i[a].v=z,i[a].t="t",M=a}}}for(a=0;a<i.length;++a)i[a]!=null&&"n?".indexOf(i[a].t)>-1&&(se=n>1&&t<0&&a>0&&i[a-1].v==="-"?-t:t,i[a].v=Nr(i[a].t,i[a].v,se),i[a].t="t");var ee="";for(a=0;a!==i.length;++a)i[a]!=null&&(ee+=i[a].v);return ee}var Of=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function Rf(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function ov(e,t){var r=sv(e),n=r.length,i=r[n-1].indexOf("@");if(n<4&&i>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||i>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=i>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=i>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=i>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var s=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,s];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var a=r[0].match(Of),o=r[1].match(Of);return Rf(t,a)?[n,r[0]]:Rf(t,o)?[n,r[1]]:[n,r[a!=null&&o!=null?2:1]]}return[n,s]}function Qr(e,t,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:rt)[e],n==null&&(n=r.table&&r.table[Af[e]]||rt[Af[e]]),n==null&&(n=Vg[e]||"General");break}if(Ns(n,0))return mo(t,r);t instanceof Date&&(t=ru(t,r.date1904));var i=ov(n,t);if(Ns(i[1]))return mo(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return av(i[1],t,r,i[0])}function hu(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(rt[r]==null){t<0&&(t=r);continue}if(rt[r]==e){t=r;break}}t<0&&(t=391)}return rt[t]=e,t}function da(e){for(var t=0;t!=392;++t)e[t]!==void 0&&hu(e[t],t)}function pa(){rt=Wg()}var du=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function fv(e){var t=typeof e=="number"?rt[e]:e;return t=t.replace(du,"(\\d+)"),new RegExp("^"+t+"$")}function lv(e,t,r){var n=-1,i=-1,s=-1,a=-1,o=-1,f=-1;(t.match(du)||[]).forEach(function(u,h){var d=parseInt(r[h+1],10);switch(u.toLowerCase().charAt(0)){case"y":n=d;break;case"d":s=d;break;case"h":a=d;break;case"s":f=d;break;case"m":a>=0?o=d:i=d;break}}),f>=0&&o==-1&&i>=0&&(o=i,i=-1);var l=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(i>=1?i:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);l.length==7&&(l="0"+l),l.length==8&&(l="20"+l);var c=("00"+(a>=0?a:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2);return a==-1&&o==-1&&f==-1?l:n==-1&&i==-1&&s==-1?c:l+"T"+c}var cv=function(){var e={};e.version="1.2.0";function t(){for(var D=0,Y=new Array(256),M=0;M!=256;++M)D=M,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,D=D&1?-306674912^D>>>1:D>>>1,Y[M]=D;return typeof Int32Array<"u"?new Int32Array(Y):Y}var r=t();function n(D){var Y=0,M=0,ee=0,re=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(ee=0;ee!=256;++ee)re[ee]=D[ee];for(ee=0;ee!=256;++ee)for(M=D[ee],Y=256+ee;Y<4096;Y+=256)M=re[Y]=M>>>8^D[M&255];var ne=[];for(ee=1;ee!=16;++ee)ne[ee-1]=typeof Int32Array<"u"?re.subarray(ee*256,ee*256+256):re.slice(ee*256,ee*256+256);return ne}var i=n(r),s=i[0],a=i[1],o=i[2],f=i[3],l=i[4],c=i[5],u=i[6],h=i[7],d=i[8],x=i[9],p=i[10],m=i[11],C=i[12],F=i[13],y=i[14];function R(D,Y){for(var M=Y^-1,ee=0,re=D.length;ee<re;)M=M>>>8^r[(M^D.charCodeAt(ee++))&255];return~M}function z(D,Y){for(var M=Y^-1,ee=D.length-15,re=0;re<ee;)M=y[D[re++]^M&255]^F[D[re++]^M>>8&255]^C[D[re++]^M>>16&255]^m[D[re++]^M>>>24]^p[D[re++]]^x[D[re++]]^d[D[re++]]^h[D[re++]]^u[D[re++]]^c[D[re++]]^l[D[re++]]^f[D[re++]]^o[D[re++]]^a[D[re++]]^s[D[re++]]^r[D[re++]];for(ee+=15;re<ee;)M=M>>>8^r[(M^D[re++])&255];return~M}function se(D,Y){for(var M=Y^-1,ee=0,re=D.length,ne=0,de=0;ee<re;)ne=D.charCodeAt(ee++),ne<128?M=M>>>8^r[(M^ne)&255]:ne<2048?(M=M>>>8^r[(M^(192|ne>>6&31))&255],M=M>>>8^r[(M^(128|ne&63))&255]):ne>=55296&&ne<57344?(ne=(ne&1023)+64,de=D.charCodeAt(ee++)&1023,M=M>>>8^r[(M^(240|ne>>8&7))&255],M=M>>>8^r[(M^(128|ne>>2&63))&255],M=M>>>8^r[(M^(128|de>>6&15|(ne&3)<<4))&255],M=M>>>8^r[(M^(128|de&63))&255]):(M=M>>>8^r[(M^(224|ne>>12&15))&255],M=M>>>8^r[(M^(128|ne>>6&63))&255],M=M>>>8^r[(M^(128|ne&63))&255]);return~M}return e.table=r,e.bstr=R,e.buf=z,e.str=se,e}(),Ke=function(){var t={};t.version="1.2.1";function r(g,w){for(var v=g.split("/"),_=w.split("/"),T=0,S=0,B=Math.min(v.length,_.length);T<B;++T){if(S=v[T].length-_[T].length)return S;if(v[T]!=_[T])return v[T]<_[T]?-1:1}return v.length-_.length}function n(g){if(g.charAt(g.length-1)=="/")return g.slice(0,-1).indexOf("/")===-1?g:n(g.slice(0,-1));var w=g.lastIndexOf("/");return w===-1?g:g.slice(0,w+1)}function i(g){if(g.charAt(g.length-1)=="/")return i(g.slice(0,-1));var w=g.lastIndexOf("/");return w===-1?g:g.slice(w+1)}function s(g,w){typeof w=="string"&&(w=new Date(w));var v=w.getHours();v=v<<6|w.getMinutes(),v=v<<5|w.getSeconds()>>>1,g.write_shift(2,v);var _=w.getFullYear()-1980;_=_<<4|w.getMonth()+1,_=_<<5|w.getDate(),g.write_shift(2,_)}function a(g){var w=g.read_shift(2)&65535,v=g.read_shift(2)&65535,_=new Date,T=v&31;v>>>=5;var S=v&15;v>>>=4,_.setMilliseconds(0),_.setFullYear(v+1980),_.setMonth(S-1),_.setDate(T);var B=w&31;w>>>=5;var Z=w&63;return w>>>=6,_.setHours(w),_.setMinutes(Z),_.setSeconds(B<<1),_}function o(g){Yt(g,0);for(var w={},v=0;g.l<=g.length-4;){var _=g.read_shift(2),T=g.read_shift(2),S=g.l+T,B={};switch(_){case 21589:v=g.read_shift(1),v&1&&(B.mtime=g.read_shift(4)),T>5&&(v&2&&(B.atime=g.read_shift(4)),v&4&&(B.ctime=g.read_shift(4))),B.mtime&&(B.mt=new Date(B.mtime*1e3));break}g.l=S,w[_]=B}return w}var f;function l(){return f||(f={})}function c(g,w){if(g[0]==80&&g[1]==75)return d0(g,w);if((g[0]|32)==109&&(g[1]|32)==105)return jh(g,w);if(g.length<512)throw new Error("CFB file size "+g.length+" < 512");var v=3,_=512,T=0,S=0,B=0,Z=0,L=0,U=[],W=g.slice(0,512);Yt(W,0);var ae=u(W);switch(v=ae[0],v){case 3:_=512;break;case 4:_=4096;break;case 0:if(ae[1]==0)return d0(g,w);default:throw new Error("Major Version: Expected 3 or 4 saw "+v)}_!==512&&(W=g.slice(0,_),Yt(W,28));var pe=g.slice(0,_);h(W,v);var ge=W.read_shift(4,"i");if(v===3&&ge!==0)throw new Error("# Directory Sectors: Expected 0 saw "+ge);W.l+=4,B=W.read_shift(4,"i"),W.l+=4,W.chk("00100000","Mini Stream Cutoff Size: "),Z=W.read_shift(4,"i"),T=W.read_shift(4,"i"),L=W.read_shift(4,"i"),S=W.read_shift(4,"i");for(var le=-1,me=0;me<109&&(le=W.read_shift(4,"i"),!(le<0));++me)U[me]=le;var Fe=d(g,_);m(L,S,Fe,_,U);var Qe=F(Fe,B,U,_);Qe[B].name="!Directory",T>0&&Z!==de&&(Qe[Z].name="!MiniFAT"),Qe[U[0]].name="!FAT",Qe.fat_addrs=U,Qe.ssz=_;var et={},Tt=[],ti=[],ri=[];y(B,Qe,Fe,Tt,T,et,ti,Z),x(ti,ri,Tt),Tt.shift();var ni={FileIndex:ti,FullPaths:ri};return w&&w.raw&&(ni.raw={header:pe,sectors:Fe}),ni}function u(g){if(g[g.l]==80&&g[g.l+1]==75)return[0,0];g.chk(be,"Header Signature: "),g.l+=16;var w=g.read_shift(2,"u");return[g.read_shift(2,"u"),w]}function h(g,w){var v=9;switch(g.l+=2,v=g.read_shift(2)){case 9:if(w!=3)throw new Error("Sector Shift: Expected 9 saw "+v);break;case 12:if(w!=4)throw new Error("Sector Shift: Expected 12 saw "+v);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+v)}g.chk("0600","Mini Sector Shift: "),g.chk("000000000000","Reserved: ")}function d(g,w){for(var v=Math.ceil(g.length/w)-1,_=[],T=1;T<v;++T)_[T-1]=g.slice(T*w,(T+1)*w);return _[v-1]=g.slice(v*w),_}function x(g,w,v){for(var _=0,T=0,S=0,B=0,Z=0,L=v.length,U=[],W=[];_<L;++_)U[_]=W[_]=_,w[_]=v[_];for(;Z<W.length;++Z)_=W[Z],T=g[_].L,S=g[_].R,B=g[_].C,U[_]===_&&(T!==-1&&U[T]!==T&&(U[_]=U[T]),S!==-1&&U[S]!==S&&(U[_]=U[S])),B!==-1&&(U[B]=_),T!==-1&&_!=U[_]&&(U[T]=U[_],W.lastIndexOf(T)<Z&&W.push(T)),S!==-1&&_!=U[_]&&(U[S]=U[_],W.lastIndexOf(S)<Z&&W.push(S));for(_=1;_<L;++_)U[_]===_&&(S!==-1&&U[S]!==S?U[_]=U[S]:T!==-1&&U[T]!==T&&(U[_]=U[T]));for(_=1;_<L;++_)if(g[_].type!==0){if(Z=_,Z!=U[Z])do Z=U[Z],w[_]=w[Z]+"/"+w[_];while(Z!==0&&U[Z]!==-1&&Z!=U[Z]);U[_]=-1}for(w[0]+="/",_=1;_<L;++_)g[_].type!==2&&(w[_]+="/")}function p(g,w,v){for(var _=g.start,T=g.size,S=[],B=_;v&&T>0&&B>=0;)S.push(w.slice(B*ne,B*ne+ne)),T-=ne,B=ln(v,B*4);return S.length===0?X(0):mt(S).slice(0,g.size)}function m(g,w,v,_,T){var S=de;if(g===de){if(w!==0)throw new Error("DIFAT chain shorter than expected")}else if(g!==-1){var B=v[g],Z=(_>>>2)-1;if(!B)return;for(var L=0;L<Z&&(S=ln(B,L*4))!==de;++L)T.push(S);m(ln(B,_-4),w-1,v,_,T)}}function C(g,w,v,_,T){var S=[],B=[];T||(T=[]);var Z=_-1,L=0,U=0;for(L=w;L>=0;){T[L]=!0,S[S.length]=L,B.push(g[L]);var W=v[Math.floor(L*4/_)];if(U=L*4&Z,_<4+U)throw new Error("FAT boundary crossed: "+L+" 4 "+_);if(!g[W])break;L=ln(g[W],U)}return{nodes:S,data:Mf([B])}}function F(g,w,v,_){var T=g.length,S=[],B=[],Z=[],L=[],U=_-1,W=0,ae=0,pe=0,ge=0;for(W=0;W<T;++W)if(Z=[],pe=W+w,pe>=T&&(pe-=T),!B[pe]){L=[];var le=[];for(ae=pe;ae>=0;){le[ae]=!0,B[ae]=!0,Z[Z.length]=ae,L.push(g[ae]);var me=v[Math.floor(ae*4/_)];if(ge=ae*4&U,_<4+ge)throw new Error("FAT boundary crossed: "+ae+" 4 "+_);if(!g[me]||(ae=ln(g[me],ge),le[ae]))break}S[pe]={nodes:Z,data:Mf([L])}}return S}function y(g,w,v,_,T,S,B,Z){for(var L=0,U=_.length?2:0,W=w[g].data,ae=0,pe=0,ge;ae<W.length;ae+=128){var le=W.slice(ae,ae+128);Yt(le,64),pe=le.read_shift(2),ge=qo(le,0,pe-U),_.push(ge);var me={name:ge,type:le.read_shift(1),color:le.read_shift(1),L:le.read_shift(4,"i"),R:le.read_shift(4,"i"),C:le.read_shift(4,"i"),clsid:le.read_shift(16),state:le.read_shift(4,"i"),start:0,size:0},Fe=le.read_shift(2)+le.read_shift(2)+le.read_shift(2)+le.read_shift(2);Fe!==0&&(me.ct=R(le,le.l-8));var Qe=le.read_shift(2)+le.read_shift(2)+le.read_shift(2)+le.read_shift(2);Qe!==0&&(me.mt=R(le,le.l-8)),me.start=le.read_shift(4,"i"),me.size=le.read_shift(4,"i"),me.size<0&&me.start<0&&(me.size=me.type=0,me.start=de,me.name=""),me.type===5?(L=me.start,T>0&&L!==de&&(w[L].name="!StreamData")):me.size>=4096?(me.storage="fat",w[me.start]===void 0&&(w[me.start]=C(v,me.start,w.fat_addrs,w.ssz)),w[me.start].name=me.name,me.content=w[me.start].data.slice(0,me.size)):(me.storage="minifat",me.size<0?me.size=0:L!==de&&me.start!==de&&w[L]&&(me.content=p(me,w[L].data,(w[Z]||{}).data))),me.content&&Yt(me.content,0),S[ge]=me,B.push(me)}}function R(g,w){return new Date((Jt(g,w+4)/1e7*Math.pow(2,32)+Jt(g,w)/1e7-11644473600)*1e3)}function z(g,w){return l(),c(f.readFileSync(g),w)}function se(g,w){var v=w&&w.type;switch(v||Le&&Buffer.isBuffer(g)&&(v="buffer"),v||"base64"){case"file":return z(g,w);case"base64":return c(cr(Br(g)),w);case"binary":return c(cr(g),w)}return c(g,w)}function D(g,w){var v=w||{},_=v.root||"Root Entry";if(g.FullPaths||(g.FullPaths=[]),g.FileIndex||(g.FileIndex=[]),g.FullPaths.length!==g.FileIndex.length)throw new Error("inconsistent CFB structure");g.FullPaths.length===0&&(g.FullPaths[0]=_+"/",g.FileIndex[0]={name:_,type:5}),v.CLSID&&(g.FileIndex[0].clsid=v.CLSID),Y(g)}function Y(g){var w="Sh33tJ5";if(!Ke.find(g,"/"+w)){var v=X(4);v[0]=55,v[1]=v[3]=50,v[2]=54,g.FileIndex.push({name:w,type:2,content:v,size:4,L:69,R:69,C:69}),g.FullPaths.push(g.FullPaths[0]+w),M(g)}}function M(g,w){D(g);for(var v=!1,_=!1,T=g.FullPaths.length-1;T>=0;--T){var S=g.FileIndex[T];switch(S.type){case 0:_?v=!0:(g.FileIndex.pop(),g.FullPaths.pop());break;case 1:case 2:case 5:_=!0,isNaN(S.R*S.L*S.C)&&(v=!0),S.R>-1&&S.L>-1&&S.R==S.L&&(v=!0);break;default:v=!0;break}}if(!(!v&&!w)){var B=new Date(1987,1,19),Z=0,L=Object.create?Object.create(null):{},U=[];for(T=0;T<g.FullPaths.length;++T)L[g.FullPaths[T]]=!0,g.FileIndex[T].type!==0&&U.push([g.FullPaths[T],g.FileIndex[T]]);for(T=0;T<U.length;++T){var W=n(U[T][0]);_=L[W],_||(U.push([W,{name:i(W).replace("/",""),type:1,clsid:Ne,ct:B,mt:B,content:null}]),L[W]=!0)}for(U.sort(function(ge,le){return r(ge[0],le[0])}),g.FullPaths=[],g.FileIndex=[],T=0;T<U.length;++T)g.FullPaths[T]=U[T][0],g.FileIndex[T]=U[T][1];for(T=0;T<U.length;++T){var ae=g.FileIndex[T],pe=g.FullPaths[T];if(ae.name=i(pe).replace("/",""),ae.L=ae.R=ae.C=-(ae.color=1),ae.size=ae.content?ae.content.length:0,ae.start=0,ae.clsid=ae.clsid||Ne,T===0)ae.C=U.length>1?1:-1,ae.size=0,ae.type=5;else if(pe.slice(-1)=="/"){for(Z=T+1;Z<U.length&&n(g.FullPaths[Z])!=pe;++Z);for(ae.C=Z>=U.length?-1:Z,Z=T+1;Z<U.length&&n(g.FullPaths[Z])!=n(pe);++Z);ae.R=Z>=U.length?-1:Z,ae.type=1}else n(g.FullPaths[T+1]||"")==n(pe)&&(ae.R=T+1),ae.type=2}}}function ee(g,w){var v=w||{};if(v.fileType=="mad")return $h(g,v);switch(M(g),v.fileType){case"zip":return Mh(g,v)}var _=function(ge){for(var le=0,me=0,Fe=0;Fe<ge.FileIndex.length;++Fe){var Qe=ge.FileIndex[Fe];if(Qe.content){var et=Qe.content.length;et>0&&(et<4096?le+=et+63>>6:me+=et+511>>9)}}for(var Tt=ge.FullPaths.length+3>>2,ti=le+7>>3,ri=le+127>>7,ni=ti+me+Tt+ri,sn=ni+127>>7,wa=sn<=109?0:Math.ceil((sn-109)/127);ni+sn+wa+127>>7>sn;)wa=++sn<=109?0:Math.ceil((sn-109)/127);var Fr=[1,wa,sn,ri,Tt,me,le,0];return ge.FileIndex[0].size=le<<6,Fr[7]=(ge.FileIndex[0].start=Fr[0]+Fr[1]+Fr[2]+Fr[3]+Fr[4]+Fr[5])+(Fr[6]+7>>3),Fr}(g),T=X(_[7]<<9),S=0,B=0;{for(S=0;S<8;++S)T.write_shift(1,ye[S]);for(S=0;S<8;++S)T.write_shift(2,0);for(T.write_shift(2,62),T.write_shift(2,3),T.write_shift(2,65534),T.write_shift(2,9),T.write_shift(2,6),S=0;S<3;++S)T.write_shift(2,0);for(T.write_shift(4,0),T.write_shift(4,_[2]),T.write_shift(4,_[0]+_[1]+_[2]+_[3]-1),T.write_shift(4,0),T.write_shift(4,4096),T.write_shift(4,_[3]?_[0]+_[1]+_[2]-1:de),T.write_shift(4,_[3]),T.write_shift(-4,_[1]?_[0]-1:de),T.write_shift(4,_[1]),S=0;S<109;++S)T.write_shift(-4,S<_[2]?_[1]+S:-1)}if(_[1])for(B=0;B<_[1];++B){for(;S<236+B*127;++S)T.write_shift(-4,S<_[2]?_[1]+S:-1);T.write_shift(-4,B===_[1]-1?de:B+1)}var Z=function(ge){for(B+=ge;S<B-1;++S)T.write_shift(-4,S+1);ge&&(++S,T.write_shift(-4,de))};for(B=S=0,B+=_[1];S<B;++S)T.write_shift(-4,Te.DIFSECT);for(B+=_[2];S<B;++S)T.write_shift(-4,Te.FATSECT);Z(_[3]),Z(_[4]);for(var L=0,U=0,W=g.FileIndex[0];L<g.FileIndex.length;++L)W=g.FileIndex[L],W.content&&(U=W.content.length,!(U<4096)&&(W.start=B,Z(U+511>>9)));for(Z(_[6]+7>>3);T.l&511;)T.write_shift(-4,Te.ENDOFCHAIN);for(B=S=0,L=0;L<g.FileIndex.length;++L)W=g.FileIndex[L],W.content&&(U=W.content.length,!(!U||U>=4096)&&(W.start=B,Z(U+63>>6)));for(;T.l&511;)T.write_shift(-4,Te.ENDOFCHAIN);for(S=0;S<_[4]<<2;++S){var ae=g.FullPaths[S];if(!ae||ae.length===0){for(L=0;L<17;++L)T.write_shift(4,0);for(L=0;L<3;++L)T.write_shift(4,-1);for(L=0;L<12;++L)T.write_shift(4,0);continue}W=g.FileIndex[S],S===0&&(W.start=W.size?W.start-1:de);var pe=S===0&&v.root||W.name;if(U=2*(pe.length+1),T.write_shift(64,pe,"utf16le"),T.write_shift(2,U),T.write_shift(1,W.type),T.write_shift(1,W.color),T.write_shift(-4,W.L),T.write_shift(-4,W.R),T.write_shift(-4,W.C),W.clsid)T.write_shift(16,W.clsid,"hex");else for(L=0;L<4;++L)T.write_shift(4,0);T.write_shift(4,W.state||0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,W.start),T.write_shift(4,W.size),T.write_shift(4,0)}for(S=1;S<g.FileIndex.length;++S)if(W=g.FileIndex[S],W.size>=4096)if(T.l=W.start+1<<9,Le&&Buffer.isBuffer(W.content))W.content.copy(T,T.l,0,W.size),T.l+=W.size+511&-512;else{for(L=0;L<W.size;++L)T.write_shift(1,W.content[L]);for(;L&511;++L)T.write_shift(1,0)}for(S=1;S<g.FileIndex.length;++S)if(W=g.FileIndex[S],W.size>0&&W.size<4096)if(Le&&Buffer.isBuffer(W.content))W.content.copy(T,T.l,0,W.size),T.l+=W.size+63&-64;else{for(L=0;L<W.size;++L)T.write_shift(1,W.content[L]);for(;L&63;++L)T.write_shift(1,0)}if(Le)T.l=T.length;else for(;T.l<T.length;)T.write_shift(1,0);return T}function re(g,w){var v=g.FullPaths.map(function(L){return L.toUpperCase()}),_=v.map(function(L){var U=L.split("/");return U[U.length-(L.slice(-1)=="/"?2:1)]}),T=!1;w.charCodeAt(0)===47?(T=!0,w=v[0].slice(0,-1)+w):T=w.indexOf("/")!==-1;var S=w.toUpperCase(),B=T===!0?v.indexOf(S):_.indexOf(S);if(B!==-1)return g.FileIndex[B];var Z=!S.match(is);for(S=S.replace(Ei,""),Z&&(S=S.replace(is,"!")),B=0;B<v.length;++B)if((Z?v[B].replace(is,"!"):v[B]).replace(Ei,"")==S||(Z?_[B].replace(is,"!"):_[B]).replace(Ei,"")==S)return g.FileIndex[B];return null}var ne=64,de=-2,be="d0cf11e0a1b11ae1",ye=[208,207,17,224,161,177,26,225],Ne="00000000000000000000000000000000",Te={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:de,FREESECT:-1,HEADER_SIGNATURE:be,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:Ne,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function Oe(g,w,v){l();var _=ee(g,v);f.writeFileSync(w,_)}function Ge(g){for(var w=new Array(g.length),v=0;v<g.length;++v)w[v]=String.fromCharCode(g[v]);return w.join("")}function nt(g,w){var v=ee(g,w);switch(w&&w.type||"buffer"){case"file":return l(),f.writeFileSync(w.filename,v),v;case"binary":return typeof v=="string"?v:Ge(v);case"base64":return ki(typeof v=="string"?v:Ge(v));case"buffer":if(Le)return Buffer.isBuffer(v)?v:Wr(v);case"array":return typeof v=="string"?cr(v):v}return v}var qe;function O(g){try{var w=g.InflateRaw,v=new w;if(v._processChunk(new Uint8Array([3,0]),v._finishFlushFlag),v.bytesRead)qe=g;else throw new Error("zlib does not expose bytesRead")}catch(_){console.error("cannot use native zlib: "+(_.message||_))}}function H(g,w){if(!qe)return u0(g,w);var v=qe.InflateRaw,_=new v,T=_._processChunk(g.slice(g.l),_._finishFlushFlag);return g.l+=_.bytesRead,T}function N(g){return qe?qe.deflateRawSync(g):ht(g)}var I=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Q=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],k=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function q(g){var w=(g<<1|g<<11)&139536|(g<<5|g<<15)&558144;return(w>>16|w>>8|w)&255}for(var j=typeof Uint8Array<"u",G=j?new Uint8Array(256):[],_e=0;_e<256;++_e)G[_e]=q(_e);function E(g,w){var v=G[g&255];return w<=8?v>>>8-w:(v=v<<8|G[g>>8&255],w<=16?v>>>16-w:(v=v<<8|G[g>>16&255],v>>>24-w))}function A(g,w){var v=w&7,_=w>>>3;return(g[_]|(v<=6?0:g[_+1]<<8))>>>v&3}function P(g,w){var v=w&7,_=w>>>3;return(g[_]|(v<=5?0:g[_+1]<<8))>>>v&7}function J(g,w){var v=w&7,_=w>>>3;return(g[_]|(v<=4?0:g[_+1]<<8))>>>v&15}function K(g,w){var v=w&7,_=w>>>3;return(g[_]|(v<=3?0:g[_+1]<<8))>>>v&31}function b(g,w){var v=w&7,_=w>>>3;return(g[_]|(v<=1?0:g[_+1]<<8))>>>v&127}function ce(g,w,v){var _=w&7,T=w>>>3,S=(1<<v)-1,B=g[T]>>>_;return v<8-_||(B|=g[T+1]<<8-_,v<16-_)||(B|=g[T+2]<<16-_,v<24-_)||(B|=g[T+3]<<24-_),B&S}function oe(g,w,v){var _=w&7,T=w>>>3;return _<=5?g[T]|=(v&7)<<_:(g[T]|=v<<_&255,g[T+1]=(v&7)>>8-_),w+3}function fe(g,w,v){var _=w&7,T=w>>>3;return v=(v&1)<<_,g[T]|=v,w+1}function ie(g,w,v){var _=w&7,T=w>>>3;return v<<=_,g[T]|=v&255,v>>>=8,g[T+1]=v,w+8}function Ee(g,w,v){var _=w&7,T=w>>>3;return v<<=_,g[T]|=v&255,v>>>=8,g[T+1]=v&255,g[T+2]=v>>>8,w+16}function xe(g,w){var v=g.length,_=2*v>w?2*v:w+5,T=0;if(v>=w)return g;if(Le){var S=Tf(_);if(g.copy)g.copy(S);else for(;T<g.length;++T)S[T]=g[T];return S}else if(j){var B=new Uint8Array(_);if(B.set)B.set(g);else for(;T<v;++T)B[T]=g[T];return B}return g.length=_,g}function ve(g){for(var w=new Array(g),v=0;v<g;++v)w[v]=0;return w}function Se(g,w,v){var _=1,T=0,S=0,B=0,Z=0,L=g.length,U=j?new Uint16Array(32):ve(32);for(S=0;S<32;++S)U[S]=0;for(S=L;S<v;++S)g[S]=0;L=g.length;var W=j?new Uint16Array(L):ve(L);for(S=0;S<L;++S)U[T=g[S]]++,_<T&&(_=T),W[S]=0;for(U[0]=0,S=1;S<=_;++S)U[S+16]=Z=Z+U[S-1]<<1;for(S=0;S<L;++S)Z=g[S],Z!=0&&(W[S]=U[Z+16]++);var ae=0;for(S=0;S<L;++S)if(ae=g[S],ae!=0)for(Z=E(W[S],_)>>_-ae,B=(1<<_+4-ae)-1;B>=0;--B)w[Z|B<<ae]=ae&15|S<<4;return _}var De=j?new Uint16Array(512):ve(512),Me=j?new Uint16Array(32):ve(32);if(!j){for(var Ie=0;Ie<512;++Ie)De[Ie]=0;for(Ie=0;Ie<32;++Ie)Me[Ie]=0}(function(){for(var g=[],w=0;w<32;w++)g.push(5);Se(g,Me,32);var v=[];for(w=0;w<=143;w++)v.push(8);for(;w<=255;w++)v.push(9);for(;w<=279;w++)v.push(7);for(;w<=287;w++)v.push(8);Se(v,De,288)})();var Pt=function(){for(var w=j?new Uint8Array(32768):[],v=0,_=0;v<k.length-1;++v)for(;_<k[v+1];++_)w[_]=v;for(;_<32768;++_)w[_]=29;var T=j?new Uint8Array(259):[];for(v=0,_=0;v<Q.length-1;++v)for(;_<Q[v+1];++_)T[_]=v;function S(Z,L){for(var U=0;U<Z.length;){var W=Math.min(65535,Z.length-U),ae=U+W==Z.length;for(L.write_shift(1,+ae),L.write_shift(2,W),L.write_shift(2,~W&65535);W-- >0;)L[L.l++]=Z[U++]}return L.l}function B(Z,L){for(var U=0,W=0,ae=j?new Uint16Array(32768):[];W<Z.length;){var pe=Math.min(65535,Z.length-W);if(pe<10){for(U=oe(L,U,+(W+pe==Z.length)),U&7&&(U+=8-(U&7)),L.l=U/8|0,L.write_shift(2,pe),L.write_shift(2,~pe&65535);pe-- >0;)L[L.l++]=Z[W++];U=L.l*8;continue}U=oe(L,U,+(W+pe==Z.length)+2);for(var ge=0;pe-- >0;){var le=Z[W];ge=(ge<<5^le)&32767;var me=-1,Fe=0;if((me=ae[ge])&&(me|=W&-32768,me>W&&(me-=32768),me<W))for(;Z[me+Fe]==Z[W+Fe]&&Fe<250;)++Fe;if(Fe>2){le=T[Fe],le<=22?U=ie(L,U,G[le+1]>>1)-1:(ie(L,U,3),U+=5,ie(L,U,G[le-23]>>5),U+=3);var Qe=le<8?0:le-4>>2;Qe>0&&(Ee(L,U,Fe-Q[le]),U+=Qe),le=w[W-me],U=ie(L,U,G[le]>>3),U-=3;var et=le<4?0:le-2>>1;et>0&&(Ee(L,U,W-me-k[le]),U+=et);for(var Tt=0;Tt<Fe;++Tt)ae[ge]=W&32767,ge=(ge<<5^Z[W])&32767,++W;pe-=Fe-1}else le<=143?le=le+48:U=fe(L,U,1),U=ie(L,U,G[le]),ae[ge]=W&32767,++W}U=ie(L,U,0)-1}return L.l=(U+7)/8|0,L.l}return function(L,U){return L.length<8?S(L,U):B(L,U)}}();function ht(g){var w=X(50+Math.floor(g.length*1.1)),v=Pt(g,w);return w.slice(0,v)}var $t=j?new Uint16Array(32768):ve(32768),Gt=j?new Uint16Array(32768):ve(32768),Vr=j?new Uint16Array(128):ve(128),ei=1,wt=1;function tr(g,w){var v=K(g,w)+257;w+=5;var _=K(g,w)+1;w+=5;var T=J(g,w)+4;w+=4;for(var S=0,B=j?new Uint8Array(19):ve(19),Z=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],L=1,U=j?new Uint8Array(8):ve(8),W=j?new Uint8Array(8):ve(8),ae=B.length,pe=0;pe<T;++pe)B[I[pe]]=S=P(g,w),L<S&&(L=S),U[S]++,w+=3;var ge=0;for(U[0]=0,pe=1;pe<=L;++pe)W[pe]=ge=ge+U[pe-1]<<1;for(pe=0;pe<ae;++pe)(ge=B[pe])!=0&&(Z[pe]=W[ge]++);var le=0;for(pe=0;pe<ae;++pe)if(le=B[pe],le!=0){ge=G[Z[pe]]>>8-le;for(var me=(1<<7-le)-1;me>=0;--me)Vr[ge|me<<le]=le&7|pe<<3}var Fe=[];for(L=1;Fe.length<v+_;)switch(ge=Vr[b(g,w)],w+=ge&7,ge>>>=3){case 16:for(S=3+A(g,w),w+=2,ge=Fe[Fe.length-1];S-- >0;)Fe.push(ge);break;case 17:for(S=3+P(g,w),w+=3;S-- >0;)Fe.push(0);break;case 18:for(S=11+b(g,w),w+=7;S-- >0;)Fe.push(0);break;default:Fe.push(ge),L<ge&&(L=ge);break}var Qe=Fe.slice(0,v),et=Fe.slice(v);for(pe=v;pe<286;++pe)Qe[pe]=0;for(pe=_;pe<30;++pe)et[pe]=0;return ei=Se(Qe,$t,286),wt=Se(et,Gt,30),w}function Zi(g,w){if(g[0]==3&&!(g[1]&3))return[xn(w),2];for(var v=0,_=0,T=Tf(w||1<<18),S=0,B=T.length>>>0,Z=0,L=0;!(_&1);){if(_=P(g,v),v+=3,_>>>1)_>>1==1?(Z=9,L=5):(v=tr(g,v),Z=ei,L=wt);else{v&7&&(v+=8-(v&7));var U=g[v>>>3]|g[(v>>>3)+1]<<8;if(v+=32,U>0)for(!w&&B<S+U&&(T=xe(T,S+U),B=T.length);U-- >0;)T[S++]=g[v>>>3],v+=8;continue}for(;;){!w&&B<S+32767&&(T=xe(T,S+32767),B=T.length);var W=ce(g,v,Z),ae=_>>>1==1?De[W]:$t[W];if(v+=ae&15,ae>>>=4,!(ae>>>8&255))T[S++]=ae;else{if(ae==256)break;ae-=257;var pe=ae<8?0:ae-4>>2;pe>5&&(pe=0);var ge=S+Q[ae];pe>0&&(ge+=ce(g,v,pe),v+=pe),W=ce(g,v,L),ae=_>>>1==1?Me[W]:Gt[W],v+=ae&15,ae>>>=4;var le=ae<4?0:ae-2>>1,me=k[ae];for(le>0&&(me+=ce(g,v,le),v+=le),!w&&B<ge&&(T=xe(T,ge+100),B=T.length);S<ge;)T[S]=T[S-me],++S}}}return w?[T,v+7>>>3]:[T.slice(0,S),v+7>>>3]}function u0(g,w){var v=g.slice(g.l||0),_=Zi(v,w);return g.l+=_[1],_[0]}function h0(g,w){if(g)typeof console<"u"&&console.error(w);else throw new Error(w)}function d0(g,w){var v=g;Yt(v,0);var _=[],T=[],S={FileIndex:_,FullPaths:T};D(S,{root:w.root});for(var B=v.length-4;(v[B]!=80||v[B+1]!=75||v[B+2]!=5||v[B+3]!=6)&&B>=0;)--B;v.l=B+4,v.l+=4;var Z=v.read_shift(2);v.l+=6;var L=v.read_shift(4);for(v.l=L,B=0;B<Z;++B){v.l+=20;var U=v.read_shift(4),W=v.read_shift(4),ae=v.read_shift(2),pe=v.read_shift(2),ge=v.read_shift(2);v.l+=8;var le=v.read_shift(4),me=o(v.slice(v.l+ae,v.l+ae+pe));v.l+=ae+pe+ge;var Fe=v.l;v.l=le+4,Lh(v,U,W,S,me),v.l=Fe}return S}function Lh(g,w,v,_,T){g.l+=2;var S=g.read_shift(2),B=g.read_shift(2),Z=a(g);if(S&8257)throw new Error("Unsupported ZIP encryption");for(var L=g.read_shift(4),U=g.read_shift(4),W=g.read_shift(4),ae=g.read_shift(2),pe=g.read_shift(2),ge="",le=0;le<ae;++le)ge+=String.fromCharCode(g[g.l++]);if(pe){var me=o(g.slice(g.l,g.l+pe));(me[21589]||{}).mt&&(Z=me[21589].mt),((T||{})[21589]||{}).mt&&(Z=T[21589].mt)}g.l+=pe;var Fe=g.slice(g.l,g.l+U);switch(B){case 8:Fe=H(g,W);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+B)}var Qe=!1;S&8&&(L=g.read_shift(4),L==134695760&&(L=g.read_shift(4),Qe=!0),U=g.read_shift(4),W=g.read_shift(4)),U!=w&&h0(Qe,"Bad compressed size: "+w+" != "+U),W!=v&&h0(Qe,"Bad uncompressed size: "+v+" != "+W),Ea(_,ge,Fe,{unsafe:!0,mt:Z})}function Mh(g,w){var v=w||{},_=[],T=[],S=X(1),B=v.compression?8:0,Z=0,L=0,U=0,W=0,ae=0,pe=g.FullPaths[0],ge=pe,le=g.FileIndex[0],me=[],Fe=0;for(L=1;L<g.FullPaths.length;++L)if(ge=g.FullPaths[L].slice(pe.length),le=g.FileIndex[L],!(!le.size||!le.content||ge=="Sh33tJ5")){var Qe=W,et=X(ge.length);for(U=0;U<ge.length;++U)et.write_shift(1,ge.charCodeAt(U)&127);et=et.slice(0,et.l),me[ae]=cv.buf(le.content,0);var Tt=le.content;B==8&&(Tt=N(Tt)),S=X(30),S.write_shift(4,67324752),S.write_shift(2,20),S.write_shift(2,Z),S.write_shift(2,B),le.mt?s(S,le.mt):S.write_shift(4,0),S.write_shift(-4,me[ae]),S.write_shift(4,Tt.length),S.write_shift(4,le.content.length),S.write_shift(2,et.length),S.write_shift(2,0),W+=S.length,_.push(S),W+=et.length,_.push(et),W+=Tt.length,_.push(Tt),S=X(46),S.write_shift(4,33639248),S.write_shift(2,0),S.write_shift(2,20),S.write_shift(2,Z),S.write_shift(2,B),S.write_shift(4,0),S.write_shift(-4,me[ae]),S.write_shift(4,Tt.length),S.write_shift(4,le.content.length),S.write_shift(2,et.length),S.write_shift(2,0),S.write_shift(2,0),S.write_shift(2,0),S.write_shift(2,0),S.write_shift(4,0),S.write_shift(4,Qe),Fe+=S.l,T.push(S),Fe+=et.length,T.push(et),++ae}return S=X(22),S.write_shift(4,101010256),S.write_shift(2,0),S.write_shift(2,0),S.write_shift(2,ae),S.write_shift(2,ae),S.write_shift(4,Fe),S.write_shift(4,W),S.write_shift(2,0),mt([mt(_),mt(T),S])}var Qi={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Bh(g,w){if(g.ctype)return g.ctype;var v=g.name||"",_=v.match(/\.([^\.]+)$/);return _&&Qi[_[1]]||w&&(_=(v=w).match(/[\.\\]([^\.\\])+$/),_&&Qi[_[1]])?Qi[_[1]]:"application/octet-stream"}function Uh(g){for(var w=ki(g),v=[],_=0;_<w.length;_+=76)v.push(w.slice(_,_+76));return v.join(`\r
`)+`\r
`}function Hh(g){var w=g.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(U){var W=U.charCodeAt(0).toString(16).toUpperCase();return"="+(W.length==1?"0"+W:W)});w=w.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),w.charAt(0)==`
`&&(w="=0D"+w.slice(1)),w=w.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var v=[],_=w.split(`\r
`),T=0;T<_.length;++T){var S=_[T];if(S.length==0){v.push("");continue}for(var B=0;B<S.length;){var Z=76,L=S.slice(B,B+Z);L.charAt(Z-1)=="="?Z--:L.charAt(Z-2)=="="?Z-=2:L.charAt(Z-3)=="="&&(Z-=3),L=S.slice(B,B+Z),B+=Z,B<S.length&&(L+="="),v.push(L)}}return v.join(`\r
`)}function Wh(g){for(var w=[],v=0;v<g.length;++v){for(var _=g[v];v<=g.length&&_.charAt(_.length-1)=="=";)_=_.slice(0,_.length-1)+g[++v];w.push(_)}for(var T=0;T<w.length;++T)w[T]=w[T].replace(/[=][0-9A-Fa-f]{2}/g,function(S){return String.fromCharCode(parseInt(S.slice(1),16))});return cr(w.join(`\r
`))}function Vh(g,w,v){for(var _="",T="",S="",B,Z=0;Z<10;++Z){var L=w[Z];if(!L||L.match(/^\s*$/))break;var U=L.match(/^(.*?):\s*([^\s].*)$/);if(U)switch(U[1].toLowerCase()){case"content-location":_=U[2].trim();break;case"content-type":S=U[2].trim();break;case"content-transfer-encoding":T=U[2].trim();break}}switch(++Z,T.toLowerCase()){case"base64":B=cr(Br(w.slice(Z).join("")));break;case"quoted-printable":B=Wh(w.slice(Z));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+T)}var W=Ea(g,_.slice(v.length),B,{unsafe:!0});S&&(W.ctype=S)}function jh(g,w){if(Ge(g.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var v=w&&w.root||"",_=(Le&&Buffer.isBuffer(g)?g.toString("binary"):Ge(g)).split(`\r
`),T=0,S="";for(T=0;T<_.length;++T)if(S=_[T],!!/^Content-Location:/i.test(S)&&(S=S.slice(S.indexOf("file")),v||(v=S.slice(0,S.lastIndexOf("/")+1)),S.slice(0,v.length)!=v))for(;v.length>0&&(v=v.slice(0,v.length-1),v=v.slice(0,v.lastIndexOf("/")+1),S.slice(0,v.length)!=v););var B=(_[1]||"").match(/boundary="(.*?)"/);if(!B)throw new Error("MAD cannot find boundary");var Z="--"+(B[1]||""),L=[],U=[],W={FileIndex:L,FullPaths:U};D(W);var ae,pe=0;for(T=0;T<_.length;++T){var ge=_[T];ge!==Z&&ge!==Z+"--"||(pe++&&Vh(W,_.slice(ae,T),v),ae=T)}return W}function $h(g,w){var v=w||{},_=v.boundary||"SheetJS";_="------="+_;for(var T=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+_.slice(2)+'"',"","",""],S=g.FullPaths[0],B=S,Z=g.FileIndex[0],L=1;L<g.FullPaths.length;++L)if(B=g.FullPaths[L].slice(S.length),Z=g.FileIndex[L],!(!Z.size||!Z.content||B=="Sh33tJ5")){B=B.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(Fe){return"_x"+Fe.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(Fe){return"_u"+Fe.charCodeAt(0).toString(16)+"_"});for(var U=Z.content,W=Le&&Buffer.isBuffer(U)?U.toString("binary"):Ge(U),ae=0,pe=Math.min(1024,W.length),ge=0,le=0;le<=pe;++le)(ge=W.charCodeAt(le))>=32&&ge<128&&++ae;var me=ae>=pe*4/5;T.push(_),T.push("Content-Location: "+(v.root||"file:///C:/SheetJS/")+B),T.push("Content-Transfer-Encoding: "+(me?"quoted-printable":"base64")),T.push("Content-Type: "+Bh(Z,B)),T.push(""),T.push(me?Hh(W):Uh(W))}return T.push(_+`--\r
`),T.join(`\r
`)}function Gh(g){var w={};return D(w,g),w}function Ea(g,w,v,_){var T=_&&_.unsafe;T||D(g);var S=!T&&Ke.find(g,w);if(!S){var B=g.FullPaths[0];w.slice(0,B.length)==B?B=w:(B.slice(-1)!="/"&&(B+="/"),B=(B+w).replace("//","/")),S={name:i(w),type:2},g.FileIndex.push(S),g.FullPaths.push(B),T||Ke.utils.cfb_gc(g)}return S.content=v,S.size=v?v.length:0,_&&(_.CLSID&&(S.clsid=_.CLSID),_.mt&&(S.mt=_.mt),_.ct&&(S.ct=_.ct)),S}function Kh(g,w){D(g);var v=Ke.find(g,w);if(v){for(var _=0;_<g.FileIndex.length;++_)if(g.FileIndex[_]==v)return g.FileIndex.splice(_,1),g.FullPaths.splice(_,1),!0}return!1}function Xh(g,w,v){D(g);var _=Ke.find(g,w);if(_){for(var T=0;T<g.FileIndex.length;++T)if(g.FileIndex[T]==_)return g.FileIndex[T].name=i(v),g.FullPaths[T]=v,!0}return!1}function zh(g){M(g,!0)}return t.find=re,t.read=se,t.parse=c,t.write=nt,t.writeFile=Oe,t.utils={cfb_new:Gh,cfb_add:Ea,cfb_del:Kh,cfb_mov:Xh,cfb_gc:zh,ReadShift:Ti,CheckField:Du,prep_blob:Yt,bconcat:mt,use_zlib:O,_deflateRaw:ht,_inflateRaw:u0,consts:Te},t}();function uv(e){return typeof e=="string"?ha(e):Array.isArray(e)?Mg(e):e}function Xi(e,t,r){if(typeof Deno<"u"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=ha(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n=r=="utf8"?Mi(t):t;if(typeof IE_SaveFile<"u")return IE_SaveFile(n,e);if(typeof Blob<"u"){var i=new Blob([uv(n)],{type:"application/octet-stream"});if(typeof navigator<"u"&&navigator.msSaveBlob)return navigator.msSaveBlob(i,e);if(typeof saveAs<"u")return saveAs(i,e);if(typeof URL<"u"&&typeof document<"u"&&document.createElement&&URL.createObjectURL){var s=URL.createObjectURL(i);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(s)},6e4),chrome.downloads.download({url:s,filename:e,saveAs:!0});var a=document.createElement("a");if(a.download!=null)return a.download=e,a.href=s,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(s)},6e4),s}}if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=Ki(t)),o.write(t),o.close(),t}catch(f){if(!f.message||!f.message.match(/onstruct/))throw f}throw new Error("cannot save file "+e)}function Et(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function Pf(e,t){for(var r=[],n=Et(e),i=0;i!==n.length;++i)r[e[n[i]][t]]==null&&(r[e[n[i]][t]]=n[i]);return r}function Ko(e){for(var t=[],r=Et(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function xa(e){for(var t=[],r=Et(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function hv(e){for(var t=[],r=Et(e),n=0;n!==r.length;++n)t[e[r[n]]]==null&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var ks=new Date(1899,11,30,0,0,0);function Vt(e,t){var r=e.getTime(),n=ks.getTime()+(e.getTimezoneOffset()-ks.getTimezoneOffset())*6e4;return(r-n)/(24*60*60*1e3)}var pu=new Date,dv=ks.getTime()+(pu.getTimezoneOffset()-ks.getTimezoneOffset())*6e4,Df=pu.getTimezoneOffset();function xu(e){var t=new Date;return t.setTime(e*24*60*60*1e3+dv),t.getTimezoneOffset()!==Df&&t.setTime(t.getTime()+(t.getTimezoneOffset()-Df)*6e4),t}var If=new Date("2017-02-19T19:06:09.000Z"),mu=isNaN(If.getFullYear())?new Date("2/19/17"):If,pv=mu.getFullYear()==2017;function kt(e,t){var r=new Date(e);if(pv)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(mu.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var i=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+i[0],+i[1]-1,+i[2],+i[3]||0,+i[4]||0,+i[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-s.getTimezoneOffset()*60*1e3)),s}function ma(e,t){if(Le&&Buffer.isBuffer(e))return e.toString("binary");if(typeof TextDecoder<"u")try{var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(s){return r[s]||s})}catch{}for(var n=[],i=0;i!=e.length;++i)n.push(String.fromCharCode(e[i]));return n.join("")}function jt(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=jt(e[r]));return t}function tt(e,t){for(var r="";r.length<t;)r+=e;return r}function br(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(n))||(n=n.replace(/[(](.*)[)]/,function(i,s){return r=-r,s}),!isNaN(t=Number(n)))?t/r:t}var xv=["january","february","march","april","may","june","july","august","september","october","november","december"];function Li(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),i=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var a=e.toLowerCase();if(a.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(a=a.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),a.length>3&&xv.indexOf(a)==-1)return r}else if(a.match(/[a-z]/))return r;return n<0||n>8099?r:(i>0||s>1)&&n!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}function Pe(e,t,r){if(e.FullPaths){if(typeof r=="string"){var n;return Le?n=Wr(r):n=Bg(r),Ke.utils.cfb_add(e,t,n)}Ke.utils.cfb_add(e,t,r)}else e.file(t,r)}function Xo(){return Ke.utils.cfb_new()}var ot=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,mv={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},zo=Ko(mv),Yo=/[&<>'"]/g,gv=/[\u0000-\u0008\u000b-\u001f]/g;function je(e){var t=e+"";return t.replace(Yo,function(r){return zo[r]}).replace(gv,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function Nf(e){return je(e).replace(/ /g,"_x0020_")}var gu=/[\u0000-\u001f]/g;function vv(e){var t=e+"";return t.replace(Yo,function(r){return zo[r]}).replace(/\n/g,"<br/>").replace(gu,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function _v(e){var t=e+"";return t.replace(Yo,function(r){return zo[r]}).replace(gu,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}function Ev(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function wv(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Ua(e){for(var t="",r=0,n=0,i=0,s=0,a=0,o=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){t+=String.fromCharCode(n);continue}if(i=e.charCodeAt(r++),n>191&&n<224){a=(n&31)<<6,a|=i&63,t+=String.fromCharCode(a);continue}if(s=e.charCodeAt(r++),n<240){t+=String.fromCharCode((n&15)<<12|(i&63)<<6|s&63);continue}a=e.charCodeAt(r++),o=((n&7)<<18|(i&63)<<12|(s&63)<<6|a&63)-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(o&1023))}return t}function bf(e){var t=xn(2*e.length),r,n,i=1,s=0,a=0,o;for(n=0;n<e.length;n+=i)i=1,(o=e.charCodeAt(n))<128?r=o:o<224?(r=(o&31)*64+(e.charCodeAt(n+1)&63),i=2):o<240?(r=(o&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),i=3):(i=4,r=(o&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,a=55296+(r>>>10&1023),r=56320+(r&1023)),a!==0&&(t[s++]=a&255,t[s++]=a>>>8,a=0),t[s++]=r%256,t[s++]=r>>>8;return t.slice(0,s).toString("ucs2")}function kf(e){return Wr(e,"binary").toString("utf8")}var as="foo bar bazâð£",wi=Le&&(kf(as)==Ua(as)&&kf||bf(as)==Ua(as)&&bf)||Ua,Mi=Le?function(e){return Wr(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,i=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,i=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(i>>18&7))),t.push(String.fromCharCode(144+(i>>12&63))),t.push(String.fromCharCode(128+(i>>6&63))),t.push(String.fromCharCode(128+(i&63)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)))}return t.join("")},Tv=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),i=0;i<e.length;++i)n=n.replace(e[i][0],e[i][1]);return n}}(),vu=/(^\s|\s$|\n)/;function gt(e,t){return"<"+e+(t.match(vu)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Bi(e){return Et(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function ue(e,t,r){return"<"+e+(r!=null?Bi(r):"")+(t!=null?(t.match(vu)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function go(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Sv(e,t){switch(typeof e){case"string":var r=ue("vt:lpwstr",je(e));return r=r.replace(/&quot;/g,"_x0022_"),r;case"number":return ue((e|0)==e?"vt:i4":"vt:r8",je(String(e)));case"boolean":return ue("vt:bool",e?"true":"false")}if(e instanceof Date)return ue("vt:filetime",go(e));throw new Error("Unable to serialize "+e)}var lt={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},qn=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],qt={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function yv(e,t){for(var r=1-2*(e[t+7]>>>7),n=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),i=e[t+6]&15,s=5;s>=0;--s)i=i*256+e[t+s];return n==2047?i==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,i+=Math.pow(2,52)),r*Math.pow(2,n-52)*i)}function Av(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,i=0,s=0,a=n?-t:t;isFinite(a)?a==0?i=s=0:(i=Math.floor(Math.log(a)/Math.LN2),s=a*Math.pow(2,52-i),i<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?i=-1022:(s-=Math.pow(2,52),i+=1023)):(i=2047,s=isNaN(t)?26985:0);for(var o=0;o<=5;++o,s/=256)e[r+o]=s&255;e[r+6]=(i&15)<<4|s&15,e[r+7]=i>>4|n}var Lf=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var i=0,s=e[0][n].length;i<s;i+=r)t.push.apply(t,e[0][n].slice(i,i+r));return t},Mf=Le?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:Wr(t)})):Lf(e)}:Lf,Bf=function(e,t,r){for(var n=[],i=t;i<r;i+=2)n.push(String.fromCharCode(ui(e,i)));return n.join("").replace(Ei,"")},qo=Le?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(Ei,""):Bf(e,t,r)}:Bf,Uf=function(e,t,r){for(var n=[],i=t;i<t+r;++i)n.push(("0"+e[i].toString(16)).slice(-2));return n.join("")},_u=Le?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):Uf(e,t,r)}:Uf,Hf=function(e,t,r){for(var n=[],i=t;i<r;i++)n.push(String.fromCharCode(Dn(e,i)));return n.join("")},zi=Le?function(t,r,n){return Buffer.isBuffer(t)?t.toString("utf8",r,n):Hf(t,r,n)}:Hf,Eu=function(e,t){var r=Jt(e,t);return r>0?zi(e,t+4,t+4+r-1):""},wu=Eu,Tu=function(e,t){var r=Jt(e,t);return r>0?zi(e,t+4,t+4+r-1):""},Su=Tu,yu=function(e,t){var r=2*Jt(e,t);return r>0?zi(e,t+4,t+4+r-1):""},Au=yu,Fu=function(t,r){var n=Jt(t,r);return n>0?qo(t,r+4,r+4+n):""},Cu=Fu,Ou=function(e,t){var r=Jt(e,t);return r>0?zi(e,t+4,t+4+r):""},Ru=Ou,Pu=function(e,t){return yv(e,t)},Ls=Pu,Jo=function(t){return Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array};Le&&(wu=function(t,r){if(!Buffer.isBuffer(t))return Eu(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Su=function(t,r){if(!Buffer.isBuffer(t))return Tu(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Au=function(t,r){if(!Buffer.isBuffer(t))return yu(t,r);var n=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n-1)},Cu=function(t,r){if(!Buffer.isBuffer(t))return Fu(t,r);var n=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n)},Ru=function(t,r){if(!Buffer.isBuffer(t))return Ou(t,r);var n=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+n)},Ls=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):Pu(t,r)},Jo=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array});var Dn=function(e,t){return e[t]},ui=function(e,t){return e[t+1]*256+e[t]},Fv=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},Jt=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},ln=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Cv=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Ti(e,t){var r="",n,i,s=[],a,o,f,l;switch(t){case"dbcs":if(l=this.l,Le&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(f=0;f<e;++f)r+=String.fromCharCode(ui(this,l)),l+=2;e*=2;break;case"utf8":r=zi(this,this.l,this.l+e);break;case"utf16le":e*=2,r=qo(this,this.l,this.l+e);break;case"wstr":return Ti.call(this,e,"dbcs");case"lpstr-ansi":r=wu(this,this.l),e=4+Jt(this,this.l);break;case"lpstr-cp":r=Su(this,this.l),e=4+Jt(this,this.l);break;case"lpwstr":r=Au(this,this.l),e=4+2*Jt(this,this.l);break;case"lpp4":e=4+Jt(this,this.l),r=Cu(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Jt(this,this.l),r=Ru(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(a=Dn(this,this.l+e++))!==0;)s.push(ns(a));r=s.join("");break;case"_wstr":for(e=0,r="";(a=ui(this,this.l+e))!==0;)s.push(ns(a)),e+=2;e+=2,r=s.join("");break;case"dbcs-cont":for(r="",l=this.l,f=0;f<e;++f){if(this.lens&&this.lens.indexOf(l)!==-1)return a=Dn(this,l),this.l=l+1,o=Ti.call(this,e-f,a?"dbcs-cont":"sbcs-cont"),s.join("")+o;s.push(ns(ui(this,l))),l+=2}r=s.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",l=this.l,f=0;f!=e;++f){if(this.lens&&this.lens.indexOf(l)!==-1)return a=Dn(this,l),this.l=l+1,o=Ti.call(this,e-f,a?"dbcs-cont":"sbcs-cont"),s.join("")+o;s.push(ns(Dn(this,l))),l+=1}r=s.join("");break;default:switch(e){case 1:return n=Dn(this,this.l),this.l++,n;case 2:return n=(t==="i"?Fv:ui)(this,this.l),this.l+=2,n;case 4:case-4:return t==="i"||!(this[this.l+3]&128)?(n=(e>0?ln:Cv)(this,this.l),this.l+=4,n):(i=Jt(this,this.l),this.l+=4,i);case 8:case-8:if(t==="f")return e==8?i=Ls(this,this.l):i=Ls([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,i;e=8;case 16:r=_u(this,this.l,e);break}}return this.l+=e,r}var Ov=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Rv=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Pv=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function Dv(e,t,r){var n=0,i=0;if(r==="dbcs"){for(i=0;i!=t.length;++i)Pv(this,t.charCodeAt(i),this.l+2*i);n=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),i=0;i!=t.length;++i)this[this.l+i]=t.charCodeAt(i)&255;n=t.length}else if(r==="hex"){for(;i<e;++i)this[this.l++]=parseInt(t.slice(2*i,2*i+2),16)||0;return this}else if(r==="utf16le"){var s=Math.min(this.l+e,this.length);for(i=0;i<Math.min(t.length,e);++i){var a=t.charCodeAt(i);this[this.l++]=a&255,this[this.l++]=a>>8}for(;this.l<s;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=t&255;break;case 2:n=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:n=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:n=4,Ov(this,t,this.l);break;case 8:if(n=8,r==="f"){Av(this,t,this.l);break}case 16:break;case-4:n=4,Rv(this,t,this.l);break}return this.l+=n,this}function Du(e,t){var r=_u(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function Yt(e,t){e.l=t,e.read_shift=Ti,e.chk=Du,e.write_shift=Dv}function Ar(e,t){e.l+=t}function X(e){var t=xn(e);return Yt(t,0),t}function Wt(){var e=[],t=Le?256:2048,r=function(l){var c=X(l);return Yt(c,0),c},n=r(t),i=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},s=function(l){return n&&l<n.length-n.l?n:(i(),n=r(Math.max(l+1,t)))},a=function(){return i(),mt(e)},o=function(l){i(),n=l,n.l==null&&(n.l=n.length),s(t)};return{next:s,push:o,end:a,_bufs:e}}function te(e,t,r,n){var i=+t,s;if(!isNaN(i)){n||(n=yS[i].p||(r||[]).length||0),s=1+(i>=128?1:0)+1,n>=128&&++s,n>=16384&&++s,n>=2097152&&++s;var a=e.next(s);i<=127?a.write_shift(1,i):(a.write_shift(1,(i&127)+128),a.write_shift(1,i>>7));for(var o=0;o!=4;++o)if(n>=128)a.write_shift(1,(n&127)+128),n>>=7;else{a.write_shift(1,n);break}n>0&&Jo(r)&&e.push(r)}}function Si(e,t,r){var n=jt(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function Wf(e,t,r){var n=jt(e);return n.s=Si(n.s,t.s,r),n.e=Si(n.e,t.s,r),n}function yi(e,t){if(e.cRel&&e.c<0)for(e=jt(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=jt(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=$e(e);return!e.cRel&&e.cRel!=null&&(r=bv(r)),!e.rRel&&e.rRel!=null&&(r=Iv(r)),r}function Ha(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+Ct(e.s.c)+":"+(e.e.cRel?"":"$")+Ct(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+_t(e.s.r)+":"+(e.e.rRel?"":"$")+_t(e.e.r):yi(e.s,t.biff)+":"+yi(e.e,t.biff)}function Zo(e){return parseInt(Nv(e),10)-1}function _t(e){return""+(e+1)}function Iv(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Nv(e){return e.replace(/\$(\d+)$/,"$1")}function Qo(e){for(var t=kv(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function Ct(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function bv(e){return e.replace(/^([A-Z])/,"$$$1")}function kv(e){return e.replace(/^\$([A-Z])/,"$1")}function Lv(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function ct(e){for(var t=0,r=0,n=0;n<e.length;++n){var i=e.charCodeAt(n);i>=48&&i<=57?t=10*t+(i-48):i>=65&&i<=90&&(r=26*r+(i-64))}return{c:r-1,r:t-1}}function $e(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function er(e){var t=e.indexOf(":");return t==-1?{s:ct(e),e:ct(e)}:{s:ct(e.slice(0,t)),e:ct(e.slice(t+1))}}function st(e,t){return typeof t>"u"||typeof t=="number"?st(e.s,e.e):(typeof e!="string"&&(e=$e(e)),typeof t!="string"&&(t=$e(t)),e==t?e:e+":"+t)}function Ye(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,i=0,s=e.length;for(r=0;n<s&&!((i=e.charCodeAt(n)-64)<1||i>26);++n)r=26*r+i;for(t.s.c=--r,r=0;n<s&&!((i=e.charCodeAt(n)-48)<0||i>9);++n)r=10*r+i;if(t.s.r=--r,n===s||i!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=s&&!((i=e.charCodeAt(n)-64)<1||i>26);++n)r=26*r+i;for(t.e.c=--r,r=0;n!=s&&!((i=e.charCodeAt(n)-48)<0||i>9);++n)r=10*r+i;return t.e.r=--r,t}function Vf(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=Qr(e.z,r?Vt(t):t)}catch{}try{return e.w=Qr((e.XF||{}).numFmtId||(r?14:0),r?Vt(t):t)}catch{return""+t}}function Ur(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Yi[e.v]||e.v:t==null?Vf(e,e.v):Vf(e,t))}function _n(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function Iu(e,t,r){var n=r||{},i=e?Array.isArray(e):n.dense,s=e||(i?[]:{}),a=0,o=0;if(s&&n.origin!=null){if(typeof n.origin=="number")a=n.origin;else{var f=typeof n.origin=="string"?ct(n.origin):n.origin;a=f.r,o=f.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var c=Ye(s["!ref"]);l.s.c=c.s.c,l.s.r=c.s.r,l.e.c=Math.max(l.e.c,c.e.c),l.e.r=Math.max(l.e.r,c.e.r),a==-1&&(l.e.r=a=c.e.r+1)}for(var u=0;u!=t.length;++u)if(t[u]){if(!Array.isArray(t[u]))throw new Error("aoa_to_sheet expects an array of arrays");for(var h=0;h!=t[u].length;++h)if(!(typeof t[u][h]>"u")){var d={v:t[u][h]},x=a+u,p=o+h;if(l.s.r>x&&(l.s.r=x),l.s.c>p&&(l.s.c=p),l.e.r<x&&(l.e.r=x),l.e.c<p&&(l.e.c=p),t[u][h]&&typeof t[u][h]=="object"&&!Array.isArray(t[u][h])&&!(t[u][h]instanceof Date))d=t[u][h];else if(Array.isArray(d.v)&&(d.f=t[u][h][1],d.v=d.v[0]),d.v===null)if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else if(n.sheetStubs)d.t="z";else continue;else typeof d.v=="number"?d.t="n":typeof d.v=="boolean"?d.t="b":d.v instanceof Date?(d.z=n.dateNF||rt[14],n.cellDates?(d.t="d",d.w=Qr(d.z,Vt(d.v))):(d.t="n",d.v=Vt(d.v),d.w=Qr(d.z,d.v))):d.t="s";if(i)s[x]||(s[x]=[]),s[x][p]&&s[x][p].z&&(d.z=s[x][p].z),s[x][p]=d;else{var m=$e({c:p,r:x});s[m]&&s[m].z&&(d.z=s[m].z),s[m]=d}}}return l.s.c<1e7&&(s["!ref"]=st(l)),s}function Jn(e,t){return Iu(null,e,t)}function Mv(e){return e.read_shift(4,"i")}function dr(e,t){return t||(t=X(4)),t.write_shift(4,e),t}function Ot(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function ut(e,t){var r=!1;return t==null&&(r=!0,t=X(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function Bv(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Uv(e,t){return t||(t=X(4)),t.write_shift(2,0),t.write_shift(2,0),t}function e0(e,t){var r=e.l,n=e.read_shift(1),i=Ot(e),s=[],a={t:i,h:i};if(n&1){for(var o=e.read_shift(4),f=0;f!=o;++f)s.push(Bv(e));a.r=s}else a.r=[{ich:0,ifnt:0}];return e.l=r+t,a}function Hv(e,t){var r=!1;return t==null&&(r=!0,t=X(15+4*e.t.length)),t.write_shift(1,0),ut(e.t,t),r?t.slice(0,t.l):t}var Wv=e0;function Vv(e,t){var r=!1;return t==null&&(r=!0,t=X(23+4*e.t.length)),t.write_shift(1,1),ut(e.t,t),t.write_shift(4,1),Uv({},t),r?t.slice(0,t.l):t}function ar(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function En(e,t){return t==null&&(t=X(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function wn(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function Tn(e,t){return t==null&&(t=X(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var jv=Ot,Nu=ut;function t0(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function Ms(e,t){var r=!1;return t==null&&(r=!0,t=X(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var $v=Ot,vo=t0,r0=Ms;function bu(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,n=t[0]&2;e.l+=4;var i=n===0?Ls([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):ln(t,0)>>2;return r?i/100:i}function ku(e,t){t==null&&(t=X(4));var r=0,n=0,i=e*100;if(e==(e|0)&&e>=-536870912&&e<1<<29?n=1:i==(i|0)&&i>=-536870912&&i<1<<29&&(n=1,r=1),n)t.write_shift(-4,((r?i:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function Lu(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function Gv(e,t){return t||(t=X(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var Sn=Lu,Zn=Gv;function Qn(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function mn(e,t){return(t||X(8)).write_shift(8,e,"f")}function Kv(e){var t={},r=e.read_shift(1),n=r>>>1,i=e.read_shift(1),s=e.read_shift(2,"i"),a=e.read_shift(1),o=e.read_shift(1),f=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=i;var l=t2[i];l&&(t.rgb=Qf(l));break;case 2:t.rgb=Qf([a,o,f]);break;case 3:t.theme=i;break}return s!=0&&(t.tint=s>0?s/32767:s/32768),t}function Bs(e,t){if(t||(t=X(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var n=e.rgb||"FFFFFF";typeof n=="number"&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}return t}function Xv(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function zv(e,t){t||(t=X(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var Mu=2,Xt=3,os=11,Us=19,fs=64,Yv=65,qv=71,Jv=4108,Zv=4126,pt=80,jf={1:{n:"CodePage",t:Mu},2:{n:"Category",t:pt},3:{n:"PresentationFormat",t:pt},4:{n:"ByteCount",t:Xt},5:{n:"LineCount",t:Xt},6:{n:"ParagraphCount",t:Xt},7:{n:"SlideCount",t:Xt},8:{n:"NoteCount",t:Xt},9:{n:"HiddenCount",t:Xt},10:{n:"MultimediaClipCount",t:Xt},11:{n:"ScaleCrop",t:os},12:{n:"HeadingPairs",t:Jv},13:{n:"TitlesOfParts",t:Zv},14:{n:"Manager",t:pt},15:{n:"Company",t:pt},16:{n:"LinksUpToDate",t:os},17:{n:"CharacterCount",t:Xt},19:{n:"SharedDoc",t:os},22:{n:"HyperlinksChanged",t:os},23:{n:"AppVersion",t:Xt,p:"version"},24:{n:"DigSig",t:Yv},26:{n:"ContentType",t:pt},27:{n:"ContentStatus",t:pt},28:{n:"Language",t:pt},29:{n:"Version",t:pt},255:{},2147483648:{n:"Locale",t:Us},2147483651:{n:"Behavior",t:Us},1919054434:{}},$f={1:{n:"CodePage",t:Mu},2:{n:"Title",t:pt},3:{n:"Subject",t:pt},4:{n:"Author",t:pt},5:{n:"Keywords",t:pt},6:{n:"Comments",t:pt},7:{n:"Template",t:pt},8:{n:"LastAuthor",t:pt},9:{n:"RevNumber",t:pt},10:{n:"EditTime",t:fs},11:{n:"LastPrinted",t:fs},12:{n:"CreatedDate",t:fs},13:{n:"ModifiedDate",t:fs},14:{n:"PageCount",t:Xt},15:{n:"WordCount",t:Xt},16:{n:"CharCount",t:Xt},17:{n:"Thumbnail",t:qv},18:{n:"Application",t:pt},19:{n:"DocSecurity",t:Xt},255:{},2147483648:{n:"Locale",t:Us},2147483651:{n:"Behavior",t:Us},1919054434:{}};function Qv(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var e2=Qv([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),t2=jt(e2),Yi={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},r2={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},ls={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function Bu(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function Uu(e,t){var r=hv(r2),n=[],i;n[n.length]=ot,n[n.length]=ue("Types",null,{xmlns:lt.CT,"xmlns:xsd":lt.xsd,"xmlns:xsi":lt.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(f){return ue("Default",null,{Extension:f[0],ContentType:f[1]})}));var s=function(f){e[f]&&e[f].length>0&&(i=e[f][0],n[n.length]=ue("Override",null,{PartName:(i[0]=="/"?"":"/")+i,ContentType:ls[f][t.bookType]||ls[f].xlsx}))},a=function(f){(e[f]||[]).forEach(function(l){n[n.length]=ue("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:ls[f][t.bookType]||ls[f].xlsx})})},o=function(f){(e[f]||[]).forEach(function(l){n[n.length]=ue("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:r[f][0]})})};return s("workbooks"),a("sheets"),a("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),a("metadata"),o("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var ke={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Hu(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function Hn(e){var t=[ot,ue("Relationships",null,{xmlns:lt.RELS})];return Et(e["!id"]).forEach(function(r){t[t.length]=ue("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Ve(e,t,r,n,i,s){if(i||(i={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,i.Id="rId"+t,i.Type=n,i.Target=r,[ke.HLINK,ke.XPATH,ke.XMISS].indexOf(i.Type)>-1&&(i.TargetMode="External"),e["!id"][i.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][i.Id]=i,e[("/"+i.Target).replace("//","/")]=i,t}function n2(e){var t=[ot];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function Gf(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function i2(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function s2(e){var t=[ot];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(Gf(e[r][0],e[r][1])),t.push(i2("",e[r][0]));return t.push(Gf("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function Wu(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+Ps.version+"</meta:generator></office:meta></office:document-meta>"}var dn=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function Wa(e,t,r,n,i){i[e]!=null||t==null||t===""||(i[e]=t,t=je(t),n[n.length]=r?ue(e,t,r):gt(e,t))}function Vu(e,t){var r=t||{},n=[ot,ue("cp:coreProperties",null,{"xmlns:cp":lt.CORE_PROPS,"xmlns:dc":lt.dc,"xmlns:dcterms":lt.dcterms,"xmlns:dcmitype":lt.dcmitype,"xmlns:xsi":lt.xsi})],i={};if(!e&&!r.Props)return n.join("");e&&(e.CreatedDate!=null&&Wa("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:go(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,i),e.ModifiedDate!=null&&Wa("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:go(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,i));for(var s=0;s!=dn.length;++s){var a=dn[s],o=r.Props&&r.Props[a[1]]!=null?r.Props[a[1]]:e?e[a[1]]:null;o===!0?o="1":o===!1?o="0":typeof o=="number"&&(o=String(o)),o!=null&&Wa(a[0],o,null,n,i)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var Wn=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],ju=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function $u(e){var t=[],r=ue;return e||(e={}),e.Application="SheetJS",t[t.length]=ot,t[t.length]=ue("Properties",null,{xmlns:lt.EXT_PROPS,"xmlns:vt":lt.vt}),Wn.forEach(function(n){if(e[n[1]]!==void 0){var i;switch(n[2]){case"string":i=je(String(e[n[1]]));break;case"bool":i=e[n[1]]?"true":"false";break}i!==void 0&&(t[t.length]=r(n[0],i))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(n){return"<vt:lpstr>"+je(n)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function Gu(e){var t=[ot,ue("Properties",null,{xmlns:lt.CUST_PROPS,"xmlns:vt":lt.vt})];if(!e)return t.join("");var r=1;return Et(e).forEach(function(i){++r,t[t.length]=ue("property",Sv(e[i]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:je(i)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var Kf={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function a2(e,t){var r=[];return Et(Kf).map(function(n){for(var i=0;i<dn.length;++i)if(dn[i][1]==n)return dn[i];for(i=0;i<Wn.length;++i)if(Wn[i][1]==n)return Wn[i];throw n}).forEach(function(n){if(e[n[1]]!=null){var i=t&&t.Props&&t.Props[n[1]]!=null?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":i=new Date(i).toISOString().replace(/\.\d*Z/,"Z");break}typeof i=="number"?i=String(i):i===!0||i===!1?i=i?"1":"0":i instanceof Date&&(i=new Date(i).toISOString().replace(/\.\d*Z/,"")),r.push(gt(Kf[n[1]]||n[1],i))}}),ue("DocumentProperties",r.join(""),{xmlns:qt.o})}function o2(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",i=[];return e&&Et(e).forEach(function(s){if(Object.prototype.hasOwnProperty.call(e,s)){for(var a=0;a<dn.length;++a)if(s==dn[a][1])return;for(a=0;a<Wn.length;++a)if(s==Wn[a][1])return;for(a=0;a<r.length;++a)if(s==r[a])return;var o=e[s],f="string";typeof o=="number"?(f="float",o=String(o)):o===!0||o===!1?(f="boolean",o=o?"1":"0"):o=String(o),i.push(ue(Nf(s),o,{"dt:dt":f}))}}),t&&Et(t).forEach(function(s){if(Object.prototype.hasOwnProperty.call(t,s)&&!(e&&Object.prototype.hasOwnProperty.call(e,s))){var a=t[s],o="string";typeof a=="number"?(o="float",a=String(a)):a===!0||a===!1?(o="boolean",a=a?"1":"0"):a instanceof Date?(o="dateTime.tz",a=a.toISOString()):a=String(a),i.push(ue(Nf(s),a,{"dt:dt":o}))}}),"<"+n+' xmlns="'+qt.o+'">'+i.join("")+"</"+n+">"}function f2(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),i=(r-n)/Math.pow(2,32);n*=1e7,i*=1e7;var s=n/Math.pow(2,32)|0;s>0&&(n=n%Math.pow(2,32),i+=s);var a=X(8);return a.write_shift(4,n),a.write_shift(4,i),a}function Xf(e,t){var r=X(4),n=X(4);switch(r.write_shift(4,e==80?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=X(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=f2(t);break;case 31:case 80:for(n=X(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return mt([r,n])}var Ku=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function l2(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function zf(e,t,r){var n=X(8),i=[],s=[],a=8,o=0,f=X(8),l=X(8);if(f.write_shift(4,2),f.write_shift(4,1200),l.write_shift(4,1),s.push(f),i.push(l),a+=8+f.length,!t){l=X(8),l.write_shift(4,0),i.unshift(l);var c=[X(4)];for(c[0].write_shift(4,e.length),o=0;o<e.length;++o){var u=e[o][0];for(f=X(8+2*(u.length+1)+(u.length%2?0:2)),f.write_shift(4,o+2),f.write_shift(4,u.length+1),f.write_shift(0,u,"dbcs");f.l!=f.length;)f.write_shift(1,0);c.push(f)}f=mt(c),s.unshift(f),a+=8+f.length}for(o=0;o<e.length;++o)if(!(t&&!t[e[o][0]])&&!(Ku.indexOf(e[o][0])>-1||ju.indexOf(e[o][0])>-1)&&e[o][1]!=null){var h=e[o][1],d=0;if(t){d=+t[e[o][0]];var x=r[d];if(x.p=="version"&&typeof h=="string"){var p=h.split(".");h=(+p[0]<<16)+(+p[1]||0)}f=Xf(x.t,h)}else{var m=l2(h);m==-1&&(m=31,h=String(h)),f=Xf(m,h)}s.push(f),l=X(8),l.write_shift(4,t?d:2+o),i.push(l),a+=8+f.length}var C=8*(s.length+1);for(o=0;o<s.length;++o)i[o].write_shift(4,C),C+=s[o].length;return n.write_shift(4,a),n.write_shift(4,s.length),mt([n].concat(i).concat(s))}function Yf(e,t,r,n,i,s){var a=X(i?68:48),o=[a];a.write_shift(2,65534),a.write_shift(2,0),a.write_shift(4,842412599),a.write_shift(16,Ke.utils.consts.HEADER_CLSID,"hex"),a.write_shift(4,i?2:1),a.write_shift(16,t,"hex"),a.write_shift(4,i?68:48);var f=zf(e,r,n);if(o.push(f),i){var l=zf(i,null,null);a.write_shift(16,s,"hex"),a.write_shift(4,68+f.length),o.push(l)}return mt(o)}function c2(e,t){t||(t=X(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function u2(e,t){return e.read_shift(t)===1}function Nt(e,t){return t||(t=X(2)),t.write_shift(2,+!!e),t}function Xu(e){return e.read_shift(2,"u")}function nr(e,t){return t||(t=X(2)),t.write_shift(2,e),t}function zu(e,t,r){return r||(r=X(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function Yu(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),i="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var s=e.read_shift(1);s&&(i="dbcs-cont")}else r.biff==12&&(i="wstr");r.biff>=2&&r.biff<=5&&(i="cpstr");var a=n?e.read_shift(n,i):"";return a}function h2(e){var t=e.t||"",r=X(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=X(2*t.length);n.write_shift(2*t.length,t,"utf16le");var i=[r,n];return mt(i)}function d2(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var i=e.read_shift(1);return i===0?n=e.read_shift(t,"sbcs-cont"):n=e.read_shift(t,"dbcs-cont"),n}function p2(e,t,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):d2(e,n,r)}function x2(e,t,r){if(r.biff>5)return p2(e,t,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function qu(e,t,r){return r||(r=X(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function qf(e,t){t||(t=X(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function m2(e){var t=X(512),r=0,n=e.Target;n.slice(0,7)=="file://"&&(n=n.slice(7));var i=n.indexOf("#"),s=i>-1?31:23;switch(n.charAt(0)){case"#":s=28;break;case".":s&=-3;break}t.write_shift(4,2),t.write_shift(4,s);var a=[8,6815827,6619237,4849780,83];for(r=0;r<a.length;++r)t.write_shift(4,a[r]);if(s==28)n=n.slice(1),qf(n,t);else if(s&2){for(a="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<a.length;++r)t.write_shift(1,parseInt(a[r],16));var o=i>-1?n.slice(0,i):n;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),s&8&&qf(i>-1?n.slice(i+1):"",t)}else{for(a="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<a.length;++r)t.write_shift(1,parseInt(a[r],16));for(var f=0;n.slice(f*3,f*3+3)=="../"||n.slice(f*3,f*3+3)=="..\\";)++f;for(t.write_shift(2,f),t.write_shift(4,n.length-3*f+1),r=0;r<n.length-3*f;++r)t.write_shift(1,n.charCodeAt(r+3*f)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function gn(e,t,r,n){return n||(n=X(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function g2(e,t,r){var n=r.biff>8?4:2,i=e.read_shift(n),s=e.read_shift(n,"i"),a=e.read_shift(n,"i");return[i,s,a]}function v2(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),i=e.read_shift(2);return{s:{c:n,r:t},e:{c:i,r}}}function Ju(e,t){return t||(t=X(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function n0(e,t,r){var n=1536,i=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,i=8;break;case"biff4":n=4,i=6;break;case"biff3":n=3,i=6;break;case"biff2":n=2,i=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var s=X(i);return s.write_shift(2,n),s.write_shift(2,t),i>4&&s.write_shift(2,29282),i>6&&s.write_shift(2,1997),i>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function _2(e,t){var r=!t||t.biff==8,n=X(r?112:54);for(n.write_shift(t.biff==8?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}function E2(e,t){var r=!t||t.biff>=8?2:1,n=X(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var i=n.slice(0,n.l);return i.l=n.l,i}function w2(e,t){var r=X(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],i=0;i<e.length;++i)n[i]=h2(e[i]);var s=mt([r].concat(n));return s.parts=[r.length].concat(n.map(function(a){return a.length})),s}function T2(){var e=X(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function S2(e){var t=X(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function y2(e,t){var r=e.name||"Arial",n=t&&t.biff==5,i=n?15+r.length:16+2*r.length,s=X(i);return s.write_shift(2,e.sz*20),s.write_shift(4,0),s.write_shift(2,400),s.write_shift(4,0),s.write_shift(2,0),s.write_shift(1,r.length),n||s.write_shift(1,1),s.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),s}function A2(e,t,r,n){var i=X(10);return gn(e,t,n,i),i.write_shift(4,r),i}function F2(e,t,r,n,i){var s=!i||i.biff==8,a=X(8+ +s+(1+s)*r.length);return gn(e,t,n,a),a.write_shift(2,r.length),s&&a.write_shift(1,1),a.write_shift((1+s)*r.length,r,s?"utf16le":"sbcs"),a}function C2(e,t,r,n){var i=r&&r.biff==5;n||(n=X(i?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(i?1:2,t.length),i||n.write_shift(1,1),n.write_shift((i?1:2)*t.length,t,i?"sbcs":"utf16le");var s=n.length>n.l?n.slice(0,n.l):n;return s.l==null&&(s.l=s.length),s}function O2(e,t){var r=t.biff==8||!t.biff?4:2,n=X(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function Jf(e,t,r,n){var i=r&&r.biff==5;n||(n=X(i?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&i&&(s|=1024),n.write_shift(4,s),n.write_shift(4,0),i||n.write_shift(4,0),n.write_shift(2,0),n}function R2(e){var t=X(8);return t.write_shift(4,0),t.write_shift(2,0),t.write_shift(2,0),t}function P2(e,t,r,n,i,s){var a=X(8);return gn(e,t,n,a),zu(r,s,a),a}function D2(e,t,r,n){var i=X(14);return gn(e,t,n,i),mn(r,i),i}function I2(e,t,r){if(r.biff<8)return N2(e,t,r);for(var n=[],i=e.l+t,s=e.read_shift(r.biff>8?4:2);s--!==0;)n.push(g2(e,r.biff>8?12:6,r));if(e.l!=i)throw new Error("Bad ExternSheet: "+e.l+" != "+i);return n}function N2(e,t,r){e[e.l+1]==3&&e[e.l]++;var n=Yu(e,t,r);return n.charCodeAt(0)==3?n.slice(1):n}function b2(e){var t=X(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)Ju(e[r],t);return t}function k2(e){var t=X(24),r=ct(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),i=0;i<16;++i)t.write_shift(1,parseInt(n[i],16));return mt([t,m2(e[1])])}function L2(e){var t=e[1].Tooltip,r=X(10+2*(t.length+1));r.write_shift(2,2048);var n=ct(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var i=0;i<t.length;++i)r.write_shift(2,t.charCodeAt(i));return r.write_shift(2,0),r}function M2(e){return e||(e=X(4)),e.write_shift(2,1),e.write_shift(2,1),e}function B2(e,t,r){if(!r.cellStyles)return Ar(e,t);var n=r&&r.biff>=12?4:2,i=e.read_shift(n),s=e.read_shift(n),a=e.read_shift(n),o=e.read_shift(n),f=e.read_shift(2);n==2&&(e.l+=2);var l={s:i,e:s,w:a,ixfe:o,flags:f};return(r.biff>=5||!r.biff)&&(l.level=f>>8&7),l}function U2(e,t){var r=X(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function H2(e){for(var t=X(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function W2(e,t,r){var n=X(15);return Ji(n,e,t),n.write_shift(8,r,"f"),n}function V2(e,t,r){var n=X(9);return Ji(n,e,t),n.write_shift(2,r),n}var j2=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Ko({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(o,f){var l=[],c=xn(1);switch(f.type){case"base64":c=cr(Br(o));break;case"binary":c=cr(o);break;case"buffer":case"array":c=o;break}Yt(c,0);var u=c.read_shift(1),h=!!(u&136),d=!1,x=!1;switch(u){case 2:break;case 3:break;case 48:d=!0,h=!0;break;case 49:d=!0,h=!0;break;case 131:break;case 139:break;case 140:x=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+u.toString(16))}var p=0,m=521;u==2&&(p=c.read_shift(2)),c.l+=3,u!=2&&(p=c.read_shift(4)),p>1048576&&(p=1e6),u!=2&&(m=c.read_shift(2));var C=c.read_shift(2),F=f.codepage||1252;u!=2&&(c.l+=16,c.read_shift(1),c[c.l]!==0&&(F=e[c[c.l]]),c.l+=1,c.l+=2),x&&(c.l+=36);for(var y=[],R={},z=Math.min(c.length,u==2?521:m-10-(d?264:0)),se=x?32:11;c.l<z&&c[c.l]!=13;)switch(R={},R.name=Ds.utils.decode(F,c.slice(c.l,c.l+se)).replace(/[\u0000\r\n].*$/g,""),c.l+=se,R.type=String.fromCharCode(c.read_shift(1)),u!=2&&!x&&(R.offset=c.read_shift(4)),R.len=c.read_shift(1),u==2&&(R.offset=c.read_shift(2)),R.dec=c.read_shift(1),R.name.length&&y.push(R),u!=2&&(c.l+=x?13:14),R.type){case"B":(!d||R.len!=8)&&f.WTF&&console.log("Skipping "+R.name+":"+R.type);break;case"G":case"P":f.WTF&&console.log("Skipping "+R.name+":"+R.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+R.type)}if(c[c.l]!==13&&(c.l=m-1),c.read_shift(1)!==13)throw new Error("DBF Terminator not found "+c.l+" "+c[c.l]);c.l=m;var D=0,Y=0;for(l[0]=[],Y=0;Y!=y.length;++Y)l[0][Y]=y[Y].name;for(;p-- >0;){if(c[c.l]===42){c.l+=C;continue}for(++c.l,l[++D]=[],Y=0,Y=0;Y!=y.length;++Y){var M=c.slice(c.l,c.l+y[Y].len);c.l+=y[Y].len,Yt(M,0);var ee=Ds.utils.decode(F,M);switch(y[Y].type){case"C":ee.trim().length&&(l[D][Y]=ee.replace(/\s+$/,""));break;case"D":ee.length===8?l[D][Y]=new Date(+ee.slice(0,4),+ee.slice(4,6)-1,+ee.slice(6,8)):l[D][Y]=ee;break;case"F":l[D][Y]=parseFloat(ee.trim());break;case"+":case"I":l[D][Y]=x?M.read_shift(-4,"i")^2147483648:M.read_shift(4,"i");break;case"L":switch(ee.trim().toUpperCase()){case"Y":case"T":l[D][Y]=!0;break;case"N":case"F":l[D][Y]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+ee+"|")}break;case"M":if(!h)throw new Error("DBF Unexpected MEMO for type "+u.toString(16));l[D][Y]="##MEMO##"+(x?parseInt(ee.trim(),10):M.read_shift(4));break;case"N":ee=ee.replace(/\u0000/g,"").trim(),ee&&ee!="."&&(l[D][Y]=+ee||0);break;case"@":l[D][Y]=new Date(M.read_shift(-8,"f")-621356832e5);break;case"T":l[D][Y]=new Date((M.read_shift(4)-2440588)*864e5+M.read_shift(4));break;case"Y":l[D][Y]=M.read_shift(4,"i")/1e4+M.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":l[D][Y]=-M.read_shift(-8,"f");break;case"B":if(d&&y[Y].len==8){l[D][Y]=M.read_shift(8,"f");break}case"G":case"P":M.l+=y[Y].len;break;case"0":if(y[Y].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+y[Y].type)}}}if(u!=2&&c.l<c.length&&c[c.l++]!=26)throw new Error("DBF EOF Marker missing "+(c.l-1)+" of "+c.length+" "+c[c.l-1].toString(16));return f&&f.sheetRows&&(l=l.slice(0,f.sheetRows)),f.DBF=y,l}function n(o,f){var l=f||{};l.dateNF||(l.dateNF="yyyymmdd");var c=Jn(r(o,l),l);return c["!cols"]=l.DBF.map(function(u){return{wch:u.len,DBF:u}}),delete l.DBF,c}function i(o,f){try{return _n(n(o,f),f)}catch(l){if(f&&f.WTF)throw l}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function a(o,f){var l=f||{};if(+l.codepage>=0&&bi(+l.codepage),l.type=="string")throw new Error("Cannot write DBF to JS string");var c=Wt(),u=$s(o,{header:1,raw:!0,cellDates:!0}),h=u[0],d=u.slice(1),x=o["!cols"]||[],p=0,m=0,C=0,F=1;for(p=0;p<h.length;++p){if(((x[p]||{}).DBF||{}).name){h[p]=x[p].DBF.name,++C;continue}if(h[p]!=null){if(++C,typeof h[p]=="number"&&(h[p]=h[p].toString(10)),typeof h[p]!="string")throw new Error("DBF Invalid column name "+h[p]+" |"+typeof h[p]+"|");if(h.indexOf(h[p])!==p){for(m=0;m<1024;++m)if(h.indexOf(h[p]+"_"+m)==-1){h[p]+="_"+m;break}}}}var y=Ye(o["!ref"]),R=[],z=[],se=[];for(p=0;p<=y.e.c-y.s.c;++p){var D="",Y="",M=0,ee=[];for(m=0;m<d.length;++m)d[m][p]!=null&&ee.push(d[m][p]);if(ee.length==0||h[p]==null){R[p]="?";continue}for(m=0;m<ee.length;++m){switch(typeof ee[m]){case"number":Y="B";break;case"string":Y="C";break;case"boolean":Y="L";break;case"object":Y=ee[m]instanceof Date?"D":"C";break;default:Y="C"}M=Math.max(M,String(ee[m]).length),D=D&&D!=Y?"C":Y}M>250&&(M=250),Y=((x[p]||{}).DBF||{}).type,Y=="C"&&x[p].DBF.len>M&&(M=x[p].DBF.len),D=="B"&&Y=="N"&&(D="N",se[p]=x[p].DBF.dec,M=x[p].DBF.len),z[p]=D=="C"||Y=="N"?M:s[D]||0,F+=z[p],R[p]=D}var re=c.next(32);for(re.write_shift(4,318902576),re.write_shift(4,d.length),re.write_shift(2,296+32*C),re.write_shift(2,F),p=0;p<4;++p)re.write_shift(4,0);for(re.write_shift(4,0|(+t[Qc]||3)<<8),p=0,m=0;p<h.length;++p)if(h[p]!=null){var ne=c.next(32),de=(h[p].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);ne.write_shift(1,de,"sbcs"),ne.write_shift(1,R[p]=="?"?"C":R[p],"sbcs"),ne.write_shift(4,m),ne.write_shift(1,z[p]||s[R[p]]||0),ne.write_shift(1,se[p]||0),ne.write_shift(1,2),ne.write_shift(4,0),ne.write_shift(1,0),ne.write_shift(4,0),ne.write_shift(4,0),m+=z[p]||s[R[p]]||0}var be=c.next(264);for(be.write_shift(4,13),p=0;p<65;++p)be.write_shift(4,0);for(p=0;p<d.length;++p){var ye=c.next(F);for(ye.write_shift(1,0),m=0;m<h.length;++m)if(h[m]!=null)switch(R[m]){case"L":ye.write_shift(1,d[p][m]==null?63:d[p][m]?84:70);break;case"B":ye.write_shift(8,d[p][m]||0,"f");break;case"N":var Ne="0";for(typeof d[p][m]=="number"&&(Ne=d[p][m].toFixed(se[m]||0)),C=0;C<z[m]-Ne.length;++C)ye.write_shift(1,32);ye.write_shift(1,Ne,"sbcs");break;case"D":d[p][m]?(ye.write_shift(4,("0000"+d[p][m].getFullYear()).slice(-4),"sbcs"),ye.write_shift(2,("00"+(d[p][m].getMonth()+1)).slice(-2),"sbcs"),ye.write_shift(2,("00"+d[p][m].getDate()).slice(-2),"sbcs")):ye.write_shift(8,"00000000","sbcs");break;case"C":var Te=String(d[p][m]!=null?d[p][m]:"").slice(0,z[m]);for(ye.write_shift(1,Te,"sbcs"),C=0;C<z[m]-Te.length;++C)ye.write_shift(1,32);break}}return c.next(1).write_shift(1,26),c.end()}return{to_workbook:i,to_sheet:n,from_sheet:a}}(),$2=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+Et(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(h,d){var x=e[d];return typeof x=="number"?wf(x):x},n=function(h,d,x){var p=d.charCodeAt(0)-32<<4|x.charCodeAt(0)-48;return p==59?h:wf(p)};e["|"]=254;function i(h,d){switch(d.type){case"base64":return s(Br(h),d);case"binary":return s(h,d);case"buffer":return s(Le&&Buffer.isBuffer(h)?h.toString("binary"):Ki(h),d);case"array":return s(ma(h),d)}throw new Error("Unrecognized type "+d.type)}function s(h,d){var x=h.split(/[\n\r]+/),p=-1,m=-1,C=0,F=0,y=[],R=[],z=null,se={},D=[],Y=[],M=[],ee=0,re;for(+d.codepage>=0&&bi(+d.codepage);C!==x.length;++C){ee=0;var ne=x[C].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),de=ne.replace(/;;/g,"\0").split(";").map(function(I){return I.replace(/\u0000/g,";")}),be=de[0],ye;if(ne.length>0)switch(be){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":de[1].charAt(0)=="P"&&R.push(ne.slice(3).replace(/;;/g,";"));break;case"C":var Ne=!1,Te=!1,Oe=!1,Ge=!1,nt=-1,qe=-1;for(F=1;F<de.length;++F)switch(de[F].charAt(0)){case"A":break;case"X":m=parseInt(de[F].slice(1))-1,Te=!0;break;case"Y":for(p=parseInt(de[F].slice(1))-1,Te||(m=0),re=y.length;re<=p;++re)y[re]=[];break;case"K":ye=de[F].slice(1),ye.charAt(0)==='"'?ye=ye.slice(1,ye.length-1):ye==="TRUE"?ye=!0:ye==="FALSE"?ye=!1:isNaN(br(ye))?isNaN(Li(ye).getDate())||(ye=kt(ye)):(ye=br(ye),z!==null&&uu(z)&&(ye=xu(ye))),Ne=!0;break;case"E":Ge=!0;var O=V_(de[F].slice(1),{r:p,c:m});y[p][m]=[y[p][m],O];break;case"S":Oe=!0,y[p][m]=[y[p][m],"S5S"];break;case"G":break;case"R":nt=parseInt(de[F].slice(1))-1;break;case"C":qe=parseInt(de[F].slice(1))-1;break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+ne)}if(Ne&&(y[p][m]&&y[p][m].length==2?y[p][m][0]=ye:y[p][m]=ye,z=null),Oe){if(Ge)throw new Error("SYLK shared formula cannot have own formula");var H=nt>-1&&y[nt][qe];if(!H||!H[1])throw new Error("SYLK shared formula cannot find base");y[p][m][1]=j_(H[1],{r:p-nt,c:m-qe})}break;case"F":var N=0;for(F=1;F<de.length;++F)switch(de[F].charAt(0)){case"X":m=parseInt(de[F].slice(1))-1,++N;break;case"Y":for(p=parseInt(de[F].slice(1))-1,re=y.length;re<=p;++re)y[re]=[];break;case"M":ee=parseInt(de[F].slice(1))/20;break;case"F":break;case"G":break;case"P":z=R[parseInt(de[F].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(M=de[F].slice(1).split(" "),re=parseInt(M[0],10);re<=parseInt(M[1],10);++re)ee=parseInt(M[2],10),Y[re-1]=ee===0?{hidden:!0}:{wch:ee},i0(Y[re-1]);break;case"C":m=parseInt(de[F].slice(1))-1,Y[m]||(Y[m]={});break;case"R":p=parseInt(de[F].slice(1))-1,D[p]||(D[p]={}),ee>0?(D[p].hpt=ee,D[p].hpx=rh(ee)):ee===0&&(D[p].hidden=!0);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+ne)}N<1&&(z=null);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+ne)}}return D.length>0&&(se["!rows"]=D),Y.length>0&&(se["!cols"]=Y),d&&d.sheetRows&&(y=y.slice(0,d.sheetRows)),[y,se]}function a(h,d){var x=i(h,d),p=x[0],m=x[1],C=Jn(p,d);return Et(m).forEach(function(F){C[F]=m[F]}),C}function o(h,d){return _n(a(h,d),d)}function f(h,d,x,p){var m="C;Y"+(x+1)+";X"+(p+1)+";K";switch(h.t){case"n":m+=h.v||0,h.f&&!h.F&&(m+=";E"+a0(h.f,{r:x,c:p}));break;case"b":m+=h.v?"TRUE":"FALSE";break;case"e":m+=h.w||h.v;break;case"d":m+='"'+(h.w||h.v)+'"';break;case"s":m+='"'+h.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return m}function l(h,d){d.forEach(function(x,p){var m="F;W"+(p+1)+" "+(p+1)+" ";x.hidden?m+="0":(typeof x.width=="number"&&!x.wpx&&(x.wpx=Hs(x.width)),typeof x.wpx=="number"&&!x.wch&&(x.wch=Ws(x.wpx)),typeof x.wch=="number"&&(m+=Math.round(x.wch))),m.charAt(m.length-1)!=" "&&h.push(m)})}function c(h,d){d.forEach(function(x,p){var m="F;";x.hidden?m+="M0;":x.hpt?m+="M"+20*x.hpt+";":x.hpx&&(m+="M"+20*Vs(x.hpx)+";"),m.length>2&&h.push(m+"R"+(p+1))})}function u(h,d){var x=["ID;PWXL;N;E"],p=[],m=Ye(h["!ref"]),C,F=Array.isArray(h),y=`\r
`;x.push("P;PGeneral"),x.push("F;P0;DG0G8;M255"),h["!cols"]&&l(x,h["!cols"]),h["!rows"]&&c(x,h["!rows"]),x.push("B;Y"+(m.e.r-m.s.r+1)+";X"+(m.e.c-m.s.c+1)+";D"+[m.s.c,m.s.r,m.e.c,m.e.r].join(" "));for(var R=m.s.r;R<=m.e.r;++R)for(var z=m.s.c;z<=m.e.c;++z){var se=$e({r:R,c:z});C=F?(h[R]||[])[z]:h[se],!(!C||C.v==null&&(!C.f||C.F))&&p.push(f(C,h,R,z))}return x.join(y)+y+p.join(y)+y+"E"+y}return{to_workbook:o,to_sheet:a,from_sheet:u}}(),G2=function(){function e(s,a){switch(a.type){case"base64":return t(Br(s),a);case"binary":return t(s,a);case"buffer":return t(Le&&Buffer.isBuffer(s)?s.toString("binary"):Ki(s),a);case"array":return t(ma(s),a)}throw new Error("Unrecognized type "+a.type)}function t(s,a){for(var o=s.split(`
`),f=-1,l=-1,c=0,u=[];c!==o.length;++c){if(o[c].trim()==="BOT"){u[++f]=[],l=0;continue}if(!(f<0)){var h=o[c].trim().split(","),d=h[0],x=h[1];++c;for(var p=o[c]||"";(p.match(/["]/g)||[]).length&1&&c<o.length-1;)p+=`
`+o[++c];switch(p=p.trim(),+d){case-1:if(p==="BOT"){u[++f]=[],l=0;continue}else if(p!=="EOD")throw new Error("Unrecognized DIF special command "+p);break;case 0:p==="TRUE"?u[f][l]=!0:p==="FALSE"?u[f][l]=!1:isNaN(br(x))?isNaN(Li(x).getDate())?u[f][l]=x:u[f][l]=kt(x):u[f][l]=br(x),++l;break;case 1:p=p.slice(1,p.length-1),p=p.replace(/""/g,'"'),p&&p.match(/^=".*"$/)&&(p=p.slice(2,-1)),u[f][l++]=p!==""?p:null;break}if(p==="EOD")break}}return a&&a.sheetRows&&(u=u.slice(0,a.sheetRows)),u}function r(s,a){return Jn(e(s,a),a)}function n(s,a){return _n(r(s,a),a)}var i=function(){var s=function(f,l,c,u,h){f.push(l),f.push(c+","+u),f.push('"'+h.replace(/"/g,'""')+'"')},a=function(f,l,c,u){f.push(l+","+c),f.push(l==1?'"'+u.replace(/"/g,'""')+'"':u)};return function(f){var l=[],c=Ye(f["!ref"]),u,h=Array.isArray(f);s(l,"TABLE",0,1,"sheetjs"),s(l,"VECTORS",0,c.e.r-c.s.r+1,""),s(l,"TUPLES",0,c.e.c-c.s.c+1,""),s(l,"DATA",0,0,"");for(var d=c.s.r;d<=c.e.r;++d){a(l,-1,0,"BOT");for(var x=c.s.c;x<=c.e.c;++x){var p=$e({r:d,c:x});if(u=h?(f[d]||[])[x]:f[p],!u){a(l,1,0,"");continue}switch(u.t){case"n":var m=u.w;!m&&u.v!=null&&(m=u.v),m==null?u.f&&!u.F?a(l,1,0,"="+u.f):a(l,1,0,""):a(l,0,m,"V");break;case"b":a(l,0,u.v?1:0,u.v?"TRUE":"FALSE");break;case"s":a(l,1,0,isNaN(u.v)?u.v:'="'+u.v+'"');break;case"d":u.w||(u.w=Qr(u.z||rt[14],Vt(kt(u.v)))),a(l,0,u.w,"V");break;default:a(l,1,0,"")}}}a(l,-1,0,"EOD");var C=`\r
`,F=l.join(C);return F}}();return{to_workbook:n,to_sheet:r,from_sheet:i}}(),Zu=function(){function e(u){return u.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(u){return u.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(u,h){for(var d=u.split(`
`),x=-1,p=-1,m=0,C=[];m!==d.length;++m){var F=d[m].trim().split(":");if(F[0]==="cell"){var y=ct(F[1]);if(C.length<=y.r)for(x=C.length;x<=y.r;++x)C[x]||(C[x]=[]);switch(x=y.r,p=y.c,F[2]){case"t":C[x][p]=e(F[3]);break;case"v":C[x][p]=+F[3];break;case"vtf":var R=F[F.length-1];case"vtc":switch(F[3]){case"nl":C[x][p]=!!+F[4];break;default:C[x][p]=+F[4];break}F[2]=="vtf"&&(C[x][p]=[C[x][p],R])}}}return h&&h.sheetRows&&(C=C.slice(0,h.sheetRows)),C}function n(u,h){return Jn(r(u,h),h)}function i(u,h){return _n(n(u,h),h)}var s=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),a=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,o=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),f="--SocialCalcSpreadsheetControlSave--";function l(u){if(!u||!u["!ref"])return"";for(var h=[],d=[],x,p="",m=er(u["!ref"]),C=Array.isArray(u),F=m.s.r;F<=m.e.r;++F)for(var y=m.s.c;y<=m.e.c;++y)if(p=$e({r:F,c:y}),x=C?(u[F]||[])[y]:u[p],!(!x||x.v==null||x.t==="z")){switch(d=["cell",p,"t"],x.t){case"s":case"str":d.push(t(x.v));break;case"n":x.f?(d[2]="vtf",d[3]="n",d[4]=x.v,d[5]=t(x.f)):(d[2]="v",d[3]=x.v);break;case"b":d[2]="vt"+(x.f?"f":"c"),d[3]="nl",d[4]=x.v?"1":"0",d[5]=t(x.f||(x.v?"TRUE":"FALSE"));break;case"d":var R=Vt(kt(x.v));d[2]="vtc",d[3]="nd",d[4]=""+R,d[5]=x.w||Qr(x.z||rt[14],R);break;case"e":continue}h.push(d.join(":"))}return h.push("sheet:c:"+(m.e.c-m.s.c+1)+":r:"+(m.e.r-m.s.r+1)+":tvf:1"),h.push("valueformat:1:text-wiki"),h.join(`
`)}function c(u){return[s,a,o,a,l(u),f].join(`
`)}return{to_workbook:i,to_sheet:n,from_sheet:c}}(),K2=function(){function e(c,u,h,d,x){x.raw?u[h][d]=c:c===""||(c==="TRUE"?u[h][d]=!0:c==="FALSE"?u[h][d]=!1:isNaN(br(c))?isNaN(Li(c).getDate())?u[h][d]=c:u[h][d]=kt(c):u[h][d]=br(c))}function t(c,u){var h=u||{},d=[];if(!c||c.length===0)return d;for(var x=c.split(/[\r\n]/),p=x.length-1;p>=0&&x[p].length===0;)--p;for(var m=10,C=0,F=0;F<=p;++F)C=x[F].indexOf(" "),C==-1?C=x[F].length:C++,m=Math.max(m,C);for(F=0;F<=p;++F){d[F]=[];var y=0;for(e(x[F].slice(0,m).trim(),d,F,y,h),y=1;y<=(x[F].length-m)/10+1;++y)e(x[F].slice(m+(y-1)*10,m+y*10).trim(),d,F,y,h)}return h.sheetRows&&(d=d.slice(0,h.sheetRows)),d}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function i(c){for(var u={},h=!1,d=0,x=0;d<c.length;++d)(x=c.charCodeAt(d))==34?h=!h:!h&&x in r&&(u[x]=(u[x]||0)+1);x=[];for(d in u)Object.prototype.hasOwnProperty.call(u,d)&&x.push([u[d],d]);if(!x.length){u=n;for(d in u)Object.prototype.hasOwnProperty.call(u,d)&&x.push([u[d],d])}return x.sort(function(p,m){return p[0]-m[0]||n[p[1]]-n[m[1]]}),r[x.pop()[1]]||44}function s(c,u){var h=u||{},d="",x=h.dense?[]:{},p={s:{c:0,r:0},e:{c:0,r:0}};c.slice(0,4)=="sep="?c.charCodeAt(5)==13&&c.charCodeAt(6)==10?(d=c.charAt(4),c=c.slice(7)):c.charCodeAt(5)==13||c.charCodeAt(5)==10?(d=c.charAt(4),c=c.slice(6)):d=i(c.slice(0,1024)):h&&h.FS?d=h.FS:d=i(c.slice(0,1024));var m=0,C=0,F=0,y=0,R=0,z=d.charCodeAt(0),se=!1,D=0,Y=c.charCodeAt(0);c=c.replace(/\r\n/mg,`
`);var M=h.dateNF!=null?fv(h.dateNF):null;function ee(){var re=c.slice(y,R),ne={};if(re.charAt(0)=='"'&&re.charAt(re.length-1)=='"'&&(re=re.slice(1,-1).replace(/""/g,'"')),re.length===0)ne.t="z";else if(h.raw)ne.t="s",ne.v=re;else if(re.trim().length===0)ne.t="s",ne.v=re;else if(re.charCodeAt(0)==61)re.charCodeAt(1)==34&&re.charCodeAt(re.length-1)==34?(ne.t="s",ne.v=re.slice(2,-1).replace(/""/g,'"')):$_(re)?(ne.t="n",ne.f=re.slice(1)):(ne.t="s",ne.v=re);else if(re=="TRUE")ne.t="b",ne.v=!0;else if(re=="FALSE")ne.t="b",ne.v=!1;else if(!isNaN(F=br(re)))ne.t="n",h.cellText!==!1&&(ne.w=re),ne.v=F;else if(!isNaN(Li(re).getDate())||M&&re.match(M)){ne.z=h.dateNF||rt[14];var de=0;M&&re.match(M)&&(re=lv(re,h.dateNF,re.match(M)||[]),de=1),h.cellDates?(ne.t="d",ne.v=kt(re,de)):(ne.t="n",ne.v=Vt(kt(re,de))),h.cellText!==!1&&(ne.w=Qr(ne.z,ne.v instanceof Date?Vt(ne.v):ne.v)),h.cellNF||delete ne.z}else ne.t="s",ne.v=re;if(ne.t=="z"||(h.dense?(x[m]||(x[m]=[]),x[m][C]=ne):x[$e({c:C,r:m})]=ne),y=R+1,Y=c.charCodeAt(y),p.e.c<C&&(p.e.c=C),p.e.r<m&&(p.e.r=m),D==z)++C;else if(C=0,++m,h.sheetRows&&h.sheetRows<=m)return!0}e:for(;R<c.length;++R)switch(D=c.charCodeAt(R)){case 34:Y===34&&(se=!se);break;case z:case 10:case 13:if(!se&&ee())break e;break}return R-y>0&&ee(),x["!ref"]=st(p),x}function a(c,u){return!(u&&u.PRN)||u.FS||c.slice(0,4)=="sep="||c.indexOf("	")>=0||c.indexOf(",")>=0||c.indexOf(";")>=0?s(c,u):Jn(t(c,u),u)}function o(c,u){var h="",d=u.type=="string"?[0,0,0,0]:i4(c,u);switch(u.type){case"base64":h=Br(c);break;case"binary":h=c;break;case"buffer":u.codepage==65001?h=c.toString("utf8"):u.codepage&&typeof Ds<"u"||(h=Le&&Buffer.isBuffer(c)?c.toString("binary"):Ki(c));break;case"array":h=ma(c);break;case"string":h=c;break;default:throw new Error("Unrecognized type "+u.type)}return d[0]==239&&d[1]==187&&d[2]==191?h=wi(h.slice(3)):u.type!="string"&&u.type!="buffer"&&u.codepage==65001?h=wi(h):u.type=="binary"&&typeof Ds<"u",h.slice(0,19)=="socialcalc:version:"?Zu.to_sheet(u.type=="string"?h:wi(h),u):a(h,u)}function f(c,u){return _n(o(c,u),u)}function l(c){for(var u=[],h=Ye(c["!ref"]),d,x=Array.isArray(c),p=h.s.r;p<=h.e.r;++p){for(var m=[],C=h.s.c;C<=h.e.c;++C){var F=$e({r:p,c:C});if(d=x?(c[p]||[])[C]:c[F],!d||d.v==null){m.push("          ");continue}for(var y=(d.w||(Ur(d),d.w)||"").slice(0,10);y.length<10;)y+=" ";m.push(y+(C===0?" ":""))}u.push(m.join(""))}return u.join(`
`)}return{to_workbook:f,to_sheet:o,from_sheet:l}}(),Zf=function(){function e(O,H,N){if(O){Yt(O,O.l||0);for(var I=N.Enum||nt;O.l<O.length;){var Q=O.read_shift(2),k=I[Q]||I[65535],q=O.read_shift(2),j=O.l+q,G=k.f&&k.f(O,q,N);if(O.l=j,H(G,k,Q))return}}}function t(O,H){switch(H.type){case"base64":return r(cr(Br(O)),H);case"binary":return r(cr(O),H);case"buffer":case"array":return r(O,H)}throw"Unsupported type "+H.type}function r(O,H){if(!O)return O;var N=H||{},I=N.dense?[]:{},Q="Sheet1",k="",q=0,j={},G=[],_e=[],E={s:{r:0,c:0},e:{r:0,c:0}},A=N.sheetRows||0;if(O[2]==0&&(O[3]==8||O[3]==9)&&O.length>=16&&O[14]==5&&O[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(O[2]==2)N.Enum=nt,e(O,function(b,ce,oe){switch(oe){case 0:N.vers=b,b>=4096&&(N.qpro=!0);break;case 6:E=b;break;case 204:b&&(k=b);break;case 222:k=b;break;case 15:case 51:N.qpro||(b[1].v=b[1].v.slice(1));case 13:case 14:case 16:oe==14&&(b[2]&112)==112&&(b[2]&15)>1&&(b[2]&15)<15&&(b[1].z=N.dateNF||rt[14],N.cellDates&&(b[1].t="d",b[1].v=xu(b[1].v))),N.qpro&&b[3]>q&&(I["!ref"]=st(E),j[Q]=I,G.push(Q),I=N.dense?[]:{},E={s:{r:0,c:0},e:{r:0,c:0}},q=b[3],Q=k||"Sheet"+(q+1),k="");var fe=N.dense?(I[b[0].r]||[])[b[0].c]:I[$e(b[0])];if(fe){fe.t=b[1].t,fe.v=b[1].v,b[1].z!=null&&(fe.z=b[1].z),b[1].f!=null&&(fe.f=b[1].f);break}N.dense?(I[b[0].r]||(I[b[0].r]=[]),I[b[0].r][b[0].c]=b[1]):I[$e(b[0])]=b[1];break}},N);else if(O[2]==26||O[2]==14)N.Enum=qe,O[2]==14&&(N.qpro=!0,O.l=0),e(O,function(b,ce,oe){switch(oe){case 204:Q=b;break;case 22:b[1].v=b[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(b[3]>q&&(I["!ref"]=st(E),j[Q]=I,G.push(Q),I=N.dense?[]:{},E={s:{r:0,c:0},e:{r:0,c:0}},q=b[3],Q="Sheet"+(q+1)),A>0&&b[0].r>=A)break;N.dense?(I[b[0].r]||(I[b[0].r]=[]),I[b[0].r][b[0].c]=b[1]):I[$e(b[0])]=b[1],E.e.c<b[0].c&&(E.e.c=b[0].c),E.e.r<b[0].r&&(E.e.r=b[0].r);break;case 27:b[14e3]&&(_e[b[14e3][0]]=b[14e3][1]);break;case 1537:_e[b[0]]=b[1],b[0]==q&&(Q=b[1]);break}},N);else throw new Error("Unrecognized LOTUS BOF "+O[2]);if(I["!ref"]=st(E),j[k||Q]=I,G.push(k||Q),!_e.length)return{SheetNames:G,Sheets:j};for(var P={},J=[],K=0;K<_e.length;++K)j[G[K]]?(J.push(_e[K]||G[K]),P[_e[K]]=j[_e[K]]||j[G[K]]):(J.push(_e[K]),P[_e[K]]={"!ref":"A1"});return{SheetNames:J,Sheets:P}}function n(O,H){var N=H||{};if(+N.codepage>=0&&bi(+N.codepage),N.type=="string")throw new Error("Cannot write WK1 to JS string");var I=Wt(),Q=Ye(O["!ref"]),k=Array.isArray(O),q=[];he(I,0,s(1030)),he(I,6,f(Q));for(var j=Math.min(Q.e.r,8191),G=Q.s.r;G<=j;++G)for(var _e=_t(G),E=Q.s.c;E<=Q.e.c;++E){G===Q.s.r&&(q[E]=Ct(E));var A=q[E]+_e,P=k?(O[G]||[])[E]:O[A];if(!(!P||P.t=="z"))if(P.t=="n")(P.v|0)==P.v&&P.v>=-32768&&P.v<=32767?he(I,13,d(G,E,P.v)):he(I,14,p(G,E,P.v));else{var J=Ur(P);he(I,15,u(G,E,J.slice(0,239)))}}return he(I,1),I.end()}function i(O,H){var N=H||{};if(+N.codepage>=0&&bi(+N.codepage),N.type=="string")throw new Error("Cannot write WK3 to JS string");var I=Wt();he(I,0,a(O));for(var Q=0,k=0;Q<O.SheetNames.length;++Q)(O.Sheets[O.SheetNames[Q]]||{})["!ref"]&&he(I,27,Ge(O.SheetNames[Q],k++));var q=0;for(Q=0;Q<O.SheetNames.length;++Q){var j=O.Sheets[O.SheetNames[Q]];if(!(!j||!j["!ref"])){for(var G=Ye(j["!ref"]),_e=Array.isArray(j),E=[],A=Math.min(G.e.r,8191),P=G.s.r;P<=A;++P)for(var J=_t(P),K=G.s.c;K<=G.e.c;++K){P===G.s.r&&(E[K]=Ct(K));var b=E[K]+J,ce=_e?(j[P]||[])[K]:j[b];if(!(!ce||ce.t=="z"))if(ce.t=="n")he(I,23,ee(P,K,q,ce.v));else{var oe=Ur(ce);he(I,22,D(P,K,q,oe.slice(0,239)))}}++q}}return he(I,1),I.end()}function s(O){var H=X(2);return H.write_shift(2,O),H}function a(O){var H=X(26);H.write_shift(2,4096),H.write_shift(2,4),H.write_shift(4,0);for(var N=0,I=0,Q=0,k=0;k<O.SheetNames.length;++k){var q=O.SheetNames[k],j=O.Sheets[q];if(!(!j||!j["!ref"])){++Q;var G=er(j["!ref"]);N<G.e.r&&(N=G.e.r),I<G.e.c&&(I=G.e.c)}}return N>8191&&(N=8191),H.write_shift(2,N),H.write_shift(1,Q),H.write_shift(1,I),H.write_shift(2,0),H.write_shift(2,0),H.write_shift(1,1),H.write_shift(1,2),H.write_shift(4,0),H.write_shift(4,0),H}function o(O,H,N){var I={s:{c:0,r:0},e:{c:0,r:0}};return H==8&&N.qpro?(I.s.c=O.read_shift(1),O.l++,I.s.r=O.read_shift(2),I.e.c=O.read_shift(1),O.l++,I.e.r=O.read_shift(2),I):(I.s.c=O.read_shift(2),I.s.r=O.read_shift(2),H==12&&N.qpro&&(O.l+=2),I.e.c=O.read_shift(2),I.e.r=O.read_shift(2),H==12&&N.qpro&&(O.l+=2),I.s.c==65535&&(I.s.c=I.e.c=I.s.r=I.e.r=0),I)}function f(O){var H=X(8);return H.write_shift(2,O.s.c),H.write_shift(2,O.s.r),H.write_shift(2,O.e.c),H.write_shift(2,O.e.r),H}function l(O,H,N){var I=[{c:0,r:0},{t:"n",v:0},0,0];return N.qpro&&N.vers!=20768?(I[0].c=O.read_shift(1),I[3]=O.read_shift(1),I[0].r=O.read_shift(2),O.l+=2):(I[2]=O.read_shift(1),I[0].c=O.read_shift(2),I[0].r=O.read_shift(2)),I}function c(O,H,N){var I=O.l+H,Q=l(O,H,N);if(Q[1].t="s",N.vers==20768){O.l++;var k=O.read_shift(1);return Q[1].v=O.read_shift(k,"utf8"),Q}return N.qpro&&O.l++,Q[1].v=O.read_shift(I-O.l,"cstr"),Q}function u(O,H,N){var I=X(7+N.length);I.write_shift(1,255),I.write_shift(2,H),I.write_shift(2,O),I.write_shift(1,39);for(var Q=0;Q<I.length;++Q){var k=N.charCodeAt(Q);I.write_shift(1,k>=128?95:k)}return I.write_shift(1,0),I}function h(O,H,N){var I=l(O,H,N);return I[1].v=O.read_shift(2,"i"),I}function d(O,H,N){var I=X(7);return I.write_shift(1,255),I.write_shift(2,H),I.write_shift(2,O),I.write_shift(2,N,"i"),I}function x(O,H,N){var I=l(O,H,N);return I[1].v=O.read_shift(8,"f"),I}function p(O,H,N){var I=X(13);return I.write_shift(1,255),I.write_shift(2,H),I.write_shift(2,O),I.write_shift(8,N,"f"),I}function m(O,H,N){var I=O.l+H,Q=l(O,H,N);if(Q[1].v=O.read_shift(8,"f"),N.qpro)O.l=I;else{var k=O.read_shift(2);R(O.slice(O.l,O.l+k),Q),O.l+=k}return Q}function C(O,H,N){var I=H&32768;return H&=-32769,H=(I?O:0)+(H>=8192?H-16384:H),(I?"":"$")+(N?Ct(H):_t(H))}var F={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},y=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function R(O,H){Yt(O,0);for(var N=[],I=0,Q="",k="",q="",j="";O.l<O.length;){var G=O[O.l++];switch(G){case 0:N.push(O.read_shift(8,"f"));break;case 1:k=C(H[0].c,O.read_shift(2),!0),Q=C(H[0].r,O.read_shift(2),!1),N.push(k+Q);break;case 2:{var _e=C(H[0].c,O.read_shift(2),!0),E=C(H[0].r,O.read_shift(2),!1);k=C(H[0].c,O.read_shift(2),!0),Q=C(H[0].r,O.read_shift(2),!1),N.push(_e+E+":"+k+Q)}break;case 3:if(O.l<O.length){console.error("WK1 premature formula end");return}break;case 4:N.push("("+N.pop()+")");break;case 5:N.push(O.read_shift(2));break;case 6:{for(var A="";G=O[O.l++];)A+=String.fromCharCode(G);N.push('"'+A.replace(/"/g,'""')+'"')}break;case 8:N.push("-"+N.pop());break;case 23:N.push("+"+N.pop());break;case 22:N.push("NOT("+N.pop()+")");break;case 20:case 21:j=N.pop(),q=N.pop(),N.push(["AND","OR"][G-20]+"("+q+","+j+")");break;default:if(G<32&&y[G])j=N.pop(),q=N.pop(),N.push(q+y[G]+j);else if(F[G]){if(I=F[G][1],I==69&&(I=O[O.l++]),I>N.length){console.error("WK1 bad formula parse 0x"+G.toString(16)+":|"+N.join("|")+"|");return}var P=N.slice(-I);N.length-=I,N.push(F[G][0]+"("+P.join(",")+")")}else return G<=7?console.error("WK1 invalid opcode "+G.toString(16)):G<=24?console.error("WK1 unsupported op "+G.toString(16)):G<=30?console.error("WK1 invalid opcode "+G.toString(16)):G<=115?console.error("WK1 unsupported function opcode "+G.toString(16)):console.error("WK1 unrecognized opcode "+G.toString(16))}}N.length==1?H[1].f=""+N[0]:console.error("WK1 bad formula parse |"+N.join("|")+"|")}function z(O){var H=[{c:0,r:0},{t:"n",v:0},0];return H[0].r=O.read_shift(2),H[3]=O[O.l++],H[0].c=O[O.l++],H}function se(O,H){var N=z(O);return N[1].t="s",N[1].v=O.read_shift(H-4,"cstr"),N}function D(O,H,N,I){var Q=X(6+I.length);Q.write_shift(2,O),Q.write_shift(1,N),Q.write_shift(1,H),Q.write_shift(1,39);for(var k=0;k<I.length;++k){var q=I.charCodeAt(k);Q.write_shift(1,q>=128?95:q)}return Q.write_shift(1,0),Q}function Y(O,H){var N=z(O);N[1].v=O.read_shift(2);var I=N[1].v>>1;if(N[1].v&1)switch(I&7){case 0:I=(I>>3)*5e3;break;case 1:I=(I>>3)*500;break;case 2:I=(I>>3)/20;break;case 3:I=(I>>3)/200;break;case 4:I=(I>>3)/2e3;break;case 5:I=(I>>3)/2e4;break;case 6:I=(I>>3)/16;break;case 7:I=(I>>3)/64;break}return N[1].v=I,N}function M(O,H){var N=z(O),I=O.read_shift(4),Q=O.read_shift(4),k=O.read_shift(2);if(k==65535)return I===0&&Q===3221225472?(N[1].t="e",N[1].v=15):I===0&&Q===3489660928?(N[1].t="e",N[1].v=42):N[1].v=0,N;var q=k&32768;return k=(k&32767)-16446,N[1].v=(1-q*2)*(Q*Math.pow(2,k+32)+I*Math.pow(2,k)),N}function ee(O,H,N,I){var Q=X(14);if(Q.write_shift(2,O),Q.write_shift(1,N),Q.write_shift(1,H),I==0)return Q.write_shift(4,0),Q.write_shift(4,0),Q.write_shift(2,65535),Q;var k=0,q=0,j=0,G=0;return I<0&&(k=1,I=-I),q=Math.log2(I)|0,I/=Math.pow(2,q-31),G=I>>>0,G&2147483648||(I/=2,++q,G=I>>>0),I-=G,G|=2147483648,G>>>=0,I*=Math.pow(2,32),j=I>>>0,Q.write_shift(4,j),Q.write_shift(4,G),q+=16383+(k?32768:0),Q.write_shift(2,q),Q}function re(O,H){var N=M(O);return O.l+=H-14,N}function ne(O,H){var N=z(O),I=O.read_shift(4);return N[1].v=I>>6,N}function de(O,H){var N=z(O),I=O.read_shift(8,"f");return N[1].v=I,N}function be(O,H){var N=de(O);return O.l+=H-10,N}function ye(O,H){return O[O.l+H-1]==0?O.read_shift(H,"cstr"):""}function Ne(O,H){var N=O[O.l++];N>H-1&&(N=H-1);for(var I="";I.length<N;)I+=String.fromCharCode(O[O.l++]);return I}function Te(O,H,N){if(!(!N.qpro||H<21)){var I=O.read_shift(1);O.l+=17,O.l+=1,O.l+=2;var Q=O.read_shift(H-21,"cstr");return[I,Q]}}function Oe(O,H){for(var N={},I=O.l+H;O.l<I;){var Q=O.read_shift(2);if(Q==14e3){for(N[Q]=[0,""],N[Q][0]=O.read_shift(2);O[O.l];)N[Q][1]+=String.fromCharCode(O[O.l]),O.l++;O.l++}}return N}function Ge(O,H){var N=X(5+O.length);N.write_shift(2,14e3),N.write_shift(2,H);for(var I=0;I<O.length;++I){var Q=O.charCodeAt(I);N[N.l++]=Q>127?95:Q}return N[N.l++]=0,N}var nt={0:{n:"BOF",f:Xu},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:o},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:h},14:{n:"NUMBER",f:x},15:{n:"LABEL",f:c},16:{n:"FORMULA",f:m},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:c},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:ye},222:{n:"SHEETNAMELP",f:Ne},65535:{n:""}},qe={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:se},23:{n:"NUMBER17",f:M},24:{n:"NUMBER18",f:Y},25:{n:"FORMULA19",f:re},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:Oe},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:ne},38:{n:"??"},39:{n:"NUMBER27",f:de},40:{n:"FORMULA28",f:be},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:ye},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:Te},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:i,to_workbook:t}}(),X2=/^\s|\s$|[\t\n\r]/;function Qu(e,t){if(!t.bookSST)return"";var r=[ot];r[r.length]=ue("sst",null,{xmlns:qn[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(e[n]!=null){var i=e[n],s="<si>";i.r?s+=i.r:(s+="<t",i.t||(i.t=""),i.t.match(X2)&&(s+=' xml:space="preserve"'),s+=">"+je(i.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function z2(e){return[e.read_shift(4),e.read_shift(4)]}function Y2(e,t){return t||(t=X(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var q2=Hv;function J2(e){var t=Wt();te(t,159,Y2(e));for(var r=0;r<e.length;++r)te(t,19,q2(e[r]));return te(t,160),t.end()}function Z2(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function eh(e){var t=0,r,n=Z2(e),i=n.length+1,s,a,o,f,l;for(r=xn(i),r[0]=n.length,s=1;s!=i;++s)r[s]=n[s-1];for(s=i-1;s>=0;--s)a=r[s],o=t&16384?1:0,f=t<<1&32767,l=o|f,t=l^a;return t^52811}var Q2=function(){function e(i,s){switch(s.type){case"base64":return t(Br(i),s);case"binary":return t(i,s);case"buffer":return t(Le&&Buffer.isBuffer(i)?i.toString("binary"):Ki(i),s);case"array":return t(ma(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){var a=s||{},o=a.dense?[]:{},f=i.match(/\\trowd.*?\\row\b/g);if(!f.length)throw new Error("RTF missing table");var l={s:{c:0,r:0},e:{c:0,r:f.length-1}};return f.forEach(function(c,u){Array.isArray(o)&&(o[u]=[]);for(var h=/\\\w+\b/g,d=0,x,p=-1;x=h.exec(c);){switch(x[0]){case"\\cell":var m=c.slice(d,h.lastIndex-x[0].length);if(m[0]==" "&&(m=m.slice(1)),++p,m.length){var C={v:m,t:"s"};Array.isArray(o)?o[u][p]=C:o[$e({r:u,c:p})]=C}break}d=h.lastIndex}p>l.e.c&&(l.e.c=p)}),o["!ref"]=st(l),o}function r(i,s){return _n(e(i,s),s)}function n(i){for(var s=["{\\rtf1\\ansi"],a=Ye(i["!ref"]),o,f=Array.isArray(i),l=a.s.r;l<=a.e.r;++l){s.push("\\trowd\\trautofit1");for(var c=a.s.c;c<=a.e.c;++c)s.push("\\cellx"+(c+1));for(s.push("\\pard\\intbl"),c=a.s.c;c<=a.e.c;++c){var u=$e({r:l,c});o=f?(i[l]||[])[c]:i[u],!(!o||o.v==null&&(!o.f||o.F))&&(s.push(" "+(o.w||(Ur(o),o.w))),s.push("\\cell"))}s.push("\\pard\\intbl\\row")}return s.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function Qf(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var e_=6,kr=e_;function Hs(e){return Math.floor((e+Math.round(128/kr)/256)*kr)}function Ws(e){return Math.floor((e-5)/kr*100+.5)/100}function _o(e){return Math.round((e*kr+5)/kr*256)/256}function i0(e){e.width?(e.wpx=Hs(e.width),e.wch=Ws(e.wpx),e.MDW=kr):e.wpx?(e.wch=Ws(e.wpx),e.width=_o(e.wch),e.MDW=kr):typeof e.wch=="number"&&(e.width=_o(e.wch),e.wpx=Hs(e.width),e.MDW=kr),e.customWidth&&delete e.customWidth}var t_=96,th=t_;function Vs(e){return e*96/th}function rh(e){return e*th/96}function r_(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)e[n]!=null&&(t[t.length]=ue("numFmt",null,{numFmtId:n,formatCode:je(e[n])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=ue("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function n_(e){var t=[];return t[t.length]=ue("cellXfs",null),e.forEach(function(r){t[t.length]=ue("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=ue("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function nh(e,t){var r=[ot,ue("styleSheet",null,{xmlns:qn[0],"xmlns:vt":lt.vt})],n;return e.SSF&&(n=r_(e.SSF))!=null&&(r[r.length]=n),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(n=n_(t.cellXfs))&&(r[r.length]=n),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function i_(e,t){var r=e.read_shift(2),n=Ot(e);return[r,n]}function s_(e,t,r){r||(r=X(6+4*t.length)),r.write_shift(2,e),ut(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),n}function a_(e,t,r){var n={};n.sz=e.read_shift(2)/20;var i=Xv(e);i.fItalic&&(n.italic=1),i.fCondense&&(n.condense=1),i.fExtend&&(n.extend=1),i.fShadow&&(n.shadow=1),i.fOutline&&(n.outline=1),i.fStrikeout&&(n.strike=1);var s=e.read_shift(2);switch(s===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var a=e.read_shift(1);a!=0&&(n.underline=a);var o=e.read_shift(1);o>0&&(n.family=o);var f=e.read_shift(1);switch(f>0&&(n.charset=f),e.l++,n.color=Kv(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=Ot(e),n}function o_(e,t){t||(t=X(25+4*32)),t.write_shift(2,e.sz*20),zv(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Bs(e.color,t);var n=0;return n=2,t.write_shift(1,n),ut(e.name,t),t.length>t.l?t.slice(0,t.l):t}var f_=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Va,l_=Ar;function el(e,t){t||(t=X(4*3+8*7+16*1)),Va||(Va=Ko(f_));var r=Va[e.patternType];r==null&&(r=40),t.write_shift(4,r);var n=0;if(r!=40)for(Bs({auto:1},t),Bs({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function c_(e,t){var r=e.l+t,n=e.read_shift(2),i=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:i}}function ih(e,t,r){r||(r=X(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function fi(e,t){return t||(t=X(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var u_=Ar;function h_(e,t){return t||(t=X(51)),t.write_shift(1,0),fi(null,t),fi(null,t),fi(null,t),fi(null,t),fi(null,t),t.length>t.l?t.slice(0,t.l):t}function d_(e,t){return t||(t=X(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,0),t.write_shift(1,0),Ms(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function p_(e,t,r){var n=X(2052);return n.write_shift(4,e),Ms(t,n),Ms(r,n),n.length>n.l?n.slice(0,n.l):n}function x_(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var i=n[0];i<=n[1];++i)t[i]!=null&&++r}),r!=0&&(te(e,615,dr(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var i=n[0];i<=n[1];++i)t[i]!=null&&te(e,44,s_(i,t[i]))}),te(e,616))}}function m_(e){var t=1;te(e,611,dr(t)),te(e,43,o_({sz:12,color:{theme:1},name:"Calibri",family:2})),te(e,612)}function g_(e){var t=2;te(e,603,dr(t)),te(e,45,el({patternType:"none"})),te(e,45,el({patternType:"gray125"})),te(e,604)}function v_(e){var t=1;te(e,613,dr(t)),te(e,46,h_()),te(e,614)}function __(e){var t=1;te(e,626,dr(t)),te(e,47,ih({numFmtId:0},65535)),te(e,627)}function E_(e,t){te(e,617,dr(t.length)),t.forEach(function(r){te(e,47,ih(r,0))}),te(e,618)}function w_(e){var t=1;te(e,619,dr(t)),te(e,48,d_({xfId:0,name:"Normal"})),te(e,620)}function T_(e){var t=0;te(e,505,dr(t)),te(e,506)}function S_(e){var t=0;te(e,508,p_(t,"TableStyleMedium9","PivotStyleMedium4")),te(e,509)}function y_(e,t){var r=Wt();return te(r,278),x_(r,e.SSF),m_(r),g_(r),v_(r),__(r),E_(r,t.cellXfs),w_(r),T_(r),S_(r),te(r,279),r.end()}function sh(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[ot];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function A_(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Ot(e)}}function F_(e){var t=X(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),ut(e.name,t),t.slice(0,t.l)}function C_(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function O_(e){var t=X(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function R_(e,t){var r=X(8+2*t.length);return r.write_shift(4,e),ut(t,r),r.slice(0,r.l)}function P_(e){return e.l+=4,e.read_shift(4)!=0}function D_(e,t){var r=X(8);return r.write_shift(4,e),r.write_shift(4,1),r}function I_(){var e=Wt();return te(e,332),te(e,334,dr(1)),te(e,335,F_({name:"XLDAPR",version:12e4,flags:3496657072})),te(e,336),te(e,339,R_(1,"XLDAPR")),te(e,52),te(e,35,dr(514)),te(e,4096,dr(0)),te(e,4097,nr(1)),te(e,36),te(e,53),te(e,340),te(e,337,D_(1)),te(e,51,O_([[1,0]])),te(e,338),te(e,333),e.end()}function ah(){var e=[ot];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function N_(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=$e(r);var n=e.read_shift(1);return n&2&&(t.l="1"),n&8&&(t.a="1"),t}var In=1024;function oh(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),i=[ue("xml",null,{"xmlns:v":qt.v,"xmlns:o":qt.o,"xmlns:x":qt.x,"xmlns:mv":qt.mv}).replace(/\/>/,">"),ue("o:shapelayout",ue("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),ue("v:shapetype",[ue("v:stroke",null,{joinstyle:"miter"}),ue("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];In<e*1e3;)In+=1e3;return t.forEach(function(s){var a=ct(s[0]),o={color2:"#BEFF82",type:"gradient"};o.type=="gradient"&&(o.angle="-180");var f=o.type=="gradient"?ue("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,l=ue("v:fill",f,o),c={on:"t",obscured:"t"};++In,i=i.concat(["<v:shape"+Bi({id:"_x0000_s"+In,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(s[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",l,ue("v:shadow",null,c),ue("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",gt("x:Anchor",[a.c+1,0,a.r+1,0,a.c+3,20,a.r+5,20].join(",")),gt("x:AutoFill","False"),gt("x:Row",String(a.r)),gt("x:Column",String(a.c)),s[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),i.push("</xml>"),i.join("")}function fh(e){var t=[ot,ue("comments",null,{xmlns:qn[0]})],r=[];return t.push("<authors>"),e.forEach(function(n){n[1].forEach(function(i){var s=je(i.a);r.indexOf(s)==-1&&(r.push(s),t.push("<author>"+s+"</author>")),i.T&&i.ID&&r.indexOf("tc="+i.ID)==-1&&(r.push("tc="+i.ID),t.push("<author>tc="+i.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(n){var i=0,s=[];if(n[1][0]&&n[1][0].T&&n[1][0].ID?i=r.indexOf("tc="+n[1][0].ID):n[1].forEach(function(f){f.a&&(i=r.indexOf(je(f.a))),s.push(f.t||"")}),t.push('<comment ref="'+n[0]+'" authorId="'+i+'"><text>'),s.length<=1)t.push(gt("t",je(s[0]||"")));else{for(var a=`Comment:
    `+s[0]+`
`,o=1;o<s.length;++o)a+=`Reply:
    `+s[o]+`
`;t.push(gt("t",je(a)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function b_(e,t,r){var n=[ot,ue("ThreadedComments",null,{xmlns:lt.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(i){var s="";(i[1]||[]).forEach(function(a,o){if(!a.T){delete a.ID;return}a.a&&t.indexOf(a.a)==-1&&t.push(a.a);var f={ref:i[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};o==0?s=f.id:f.parentId=s,a.ID=f.id,a.a&&(f.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(a.a)).slice(-12)+"}"),n.push(ue("threadedComment",gt("text",a.t||""),f))})}),n.push("</ThreadedComments>"),n.join("")}function k_(e){var t=[ot,ue("personList",null,{xmlns:lt.TCMNT,"xmlns:x":qn[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,n){t.push(ue("person",null,{displayName:r,id:"{54EE7950-7262-4200-6969-"+("000000000000"+n).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function L_(e){var t={};t.iauthor=e.read_shift(4);var r=Sn(e);return t.rfx=r.s,t.ref=$e(r.s),e.l+=16,t}function M_(e,t){return t==null&&(t=X(36)),t.write_shift(4,e[1].iauthor),Zn(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var B_=Ot;function U_(e){return ut(e.slice(0,54))}function H_(e){var t=Wt(),r=[];return te(t,628),te(t,630),e.forEach(function(n){n[1].forEach(function(i){r.indexOf(i.a)>-1||(r.push(i.a.slice(0,54)),te(t,632,U_(i.a)))})}),te(t,631),te(t,633),e.forEach(function(n){n[1].forEach(function(i){i.iauthor=r.indexOf(i.a);var s={s:ct(n[0]),e:ct(n[0])};te(t,635,M_([s,i])),i.t&&i.t.length>0&&te(t,637,Vv(i)),te(t,636),delete i.iauthor})}),te(t,634),te(t,629),t.end()}function W_(e,t){t.FullPaths.forEach(function(r,n){if(n!=0){var i=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");i.slice(-1)!=="/"&&Ke.utils.cfb_add(e,i,t.FileIndex[n].content)}})}var lh=["xlsb","xlsm","xlam","biff8","xla"],V_=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(n,i,s,a){var o=!1,f=!1;s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1)),a.length==0?o=!0:a.charAt(0)=="["&&(o=!0,a=a.slice(1,-1));var l=s.length>0?parseInt(s,10)|0:0,c=a.length>0?parseInt(a,10)|0:0;return o?c+=t.c:--c,f?l+=t.r:--l,i+(o?"":"$")+Ct(c)+(f?"":"$")+_t(l)}return function(i,s){return t=s,i.replace(e,r)}}(),s0=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,a0=function(){return function(t,r){return t.replace(s0,function(n,i,s,a,o,f){var l=Qo(a)-(s?0:r.c),c=Zo(f)-(o?0:r.r),u=c==0?"":o?c+1:"["+c+"]",h=l==0?"":s?l+1:"["+l+"]";return i+"R"+u+"C"+h})}}();function j_(e,t){return e.replace(s0,function(r,n,i,s,a,o){return n+(i=="$"?i+s:Ct(Qo(s)+t.c))+(a=="$"?a+o:_t(Zo(o)+t.r))})}function $_(e){return e.length!=1}function it(e){e.l+=1}function en(e,t){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function ch(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return uh(e);r.biff==12&&(n=4)}var i=e.read_shift(n),s=e.read_shift(n),a=en(e),o=en(e);return{s:{r:i,c:a[0],cRel:a[1],rRel:a[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function uh(e){var t=en(e),r=en(e),n=e.read_shift(1),i=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:i,cRel:r[1],rRel:r[2]}}}function G_(e,t,r){if(r.biff<8)return uh(e);var n=e.read_shift(r.biff==12?4:2),i=e.read_shift(r.biff==12?4:2),s=en(e),a=en(e);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:a[0],cRel:a[1],rRel:a[2]}}}function hh(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return K_(e);var n=e.read_shift(r&&r.biff==12?4:2),i=en(e);return{r:n,c:i[0],cRel:i[1],rRel:i[2]}}function K_(e){var t=en(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function X_(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function z_(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return Y_(e);var i=e.read_shift(n>=12?4:2),s=e.read_shift(2),a=(s&16384)>>14,o=(s&32768)>>15;if(s&=16383,o==1)for(;i>524287;)i-=1048576;if(a==1)for(;s>8191;)s=s-16384;return{r:i,c:s,cRel:a,rRel:o}}function Y_(e){var t=e.read_shift(2),r=e.read_shift(1),n=(t&32768)>>15,i=(t&16384)>>14;return t&=16383,n==1&&t>=8192&&(t=t-16384),i==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:i,rRel:n}}function q_(e,t,r){var n=(e[e.l++]&96)>>5,i=ch(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,i]}function J_(e,t,r){var n=(e[e.l++]&96)>>5,i=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}var a=ch(e,s,r);return[n,i,a]}function Z_(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function Q_(e,t,r){var n=(e[e.l++]&96)>>5,i=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}return e.l+=s,[n,i]}function eE(e,t,r){var n=(e[e.l++]&96)>>5,i=G_(e,t-1,r);return[n,i]}function tE(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function tl(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function rE(e,t,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),i=[],s=0;s<=n;++s)i.push(e.read_shift(r&&r.biff==2?1:2));return i}function nE(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function iE(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function sE(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function aE(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function dh(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function oE(e){return e.read_shift(2),dh(e)}function fE(e){return e.read_shift(2),dh(e)}function lE(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var i=hh(e,0,r);return[n,i]}function cE(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var i=z_(e,0,r);return[n,i]}function uE(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var i=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var s=hh(e,0,r);return[n,i,s]}function hE(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var i=e.read_shift(r&&r.biff<=3?1:2);return[hw[i],mh[i],n]}function dE(e,t,r){var n=e[e.l++],i=e.read_shift(1),s=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:pE(e);return[i,(s[0]===0?mh:uw)[s[1]]]}function pE(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function xE(e,t,r){e.l+=r&&r.biff==2?3:4}function mE(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),i=e.read_shift(r&&r.biff==2?1:2);return[n,i]}function gE(e){return e.l++,Yi[e.read_shift(1)]}function vE(e){return e.l++,e.read_shift(2)}function _E(e){return e.l++,e.read_shift(1)!==0}function EE(e){return e.l++,Qn(e)}function wE(e,t,r){return e.l++,Yu(e,t-1,r)}function TE(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=u2(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Yi[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Qn(e);break;case 2:r[1]=x2(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function SE(e,t,r){for(var n=e.read_shift(r.biff==12?4:2),i=[],s=0;s!=n;++s)i.push((r.biff==12?Sn:v2)(e));return i}function yE(e,t,r){var n=0,i=0;r.biff==12?(n=e.read_shift(4),i=e.read_shift(4)):(i=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--i==0&&(i=256));for(var s=0,a=[];s!=n&&(a[s]=[]);++s)for(var o=0;o!=i;++o)a[s][o]=TE(e,r.biff);return a}function AE(e,t,r){var n=e.read_shift(1)>>>5&3,i=!r||r.biff>=8?4:2,s=e.read_shift(i);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,s]}function FE(e,t,r){if(r.biff==5)return CE(e);var n=e.read_shift(1)>>>5&3,i=e.read_shift(2),s=e.read_shift(4);return[n,i,s]}function CE(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function OE(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var i=e.read_shift(r&&r.biff==2?1:2);return[n,i]}function RE(e,t,r){var n=e.read_shift(1)>>>5&3,i=e.read_shift(r&&r.biff==2?1:2);return[n,i]}function PE(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function DE(e,t,r){var n=(e[e.l++]&96)>>5,i=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6;break}return e.l+=s,[n,i]}var IE=Ar,NE=Ar,bE=Ar;function qi(e,t,r){return e.l+=2,[X_(e)]}function o0(e){return e.l+=6,[]}var kE=qi,LE=o0,ME=o0,BE=qi;function ph(e){return e.l+=2,[Xu(e),e.read_shift(2)&1]}var UE=qi,HE=ph,WE=o0,VE=qi,jE=qi,$E=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function GE(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),i=e.read_shift(2),s=e.read_shift(2),a=$E[r>>2&31];return{ixti:t,coltype:r&3,rt:a,idx:n,c:i,C:s}}function KE(e){return e.l+=2,[e.read_shift(4)]}function XE(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function zE(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function YE(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function qE(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function JE(e){return e.l+=4,[0,0]}var rl={1:{n:"PtgExp",f:mE},2:{n:"PtgTbl",f:bE},3:{n:"PtgAdd",f:it},4:{n:"PtgSub",f:it},5:{n:"PtgMul",f:it},6:{n:"PtgDiv",f:it},7:{n:"PtgPower",f:it},8:{n:"PtgConcat",f:it},9:{n:"PtgLt",f:it},10:{n:"PtgLe",f:it},11:{n:"PtgEq",f:it},12:{n:"PtgGe",f:it},13:{n:"PtgGt",f:it},14:{n:"PtgNe",f:it},15:{n:"PtgIsect",f:it},16:{n:"PtgUnion",f:it},17:{n:"PtgRange",f:it},18:{n:"PtgUplus",f:it},19:{n:"PtgUminus",f:it},20:{n:"PtgPercent",f:it},21:{n:"PtgParen",f:it},22:{n:"PtgMissArg",f:it},23:{n:"PtgStr",f:wE},26:{n:"PtgSheet",f:XE},27:{n:"PtgEndSheet",f:zE},28:{n:"PtgErr",f:gE},29:{n:"PtgBool",f:_E},30:{n:"PtgInt",f:vE},31:{n:"PtgNum",f:EE},32:{n:"PtgArray",f:tE},33:{n:"PtgFunc",f:hE},34:{n:"PtgFuncVar",f:dE},35:{n:"PtgName",f:AE},36:{n:"PtgRef",f:lE},37:{n:"PtgArea",f:q_},38:{n:"PtgMemArea",f:OE},39:{n:"PtgMemErr",f:IE},40:{n:"PtgMemNoMem",f:NE},41:{n:"PtgMemFunc",f:RE},42:{n:"PtgRefErr",f:PE},43:{n:"PtgAreaErr",f:Z_},44:{n:"PtgRefN",f:cE},45:{n:"PtgAreaN",f:eE},46:{n:"PtgMemAreaN",f:YE},47:{n:"PtgMemNoMemN",f:qE},57:{n:"PtgNameX",f:FE},58:{n:"PtgRef3d",f:uE},59:{n:"PtgArea3d",f:J_},60:{n:"PtgRefErr3d",f:DE},61:{n:"PtgAreaErr3d",f:Q_},255:{}},ZE={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},QE={1:{n:"PtgElfLel",f:ph},2:{n:"PtgElfRw",f:VE},3:{n:"PtgElfCol",f:kE},6:{n:"PtgElfRwV",f:jE},7:{n:"PtgElfColV",f:BE},10:{n:"PtgElfRadical",f:UE},11:{n:"PtgElfRadicalS",f:WE},13:{n:"PtgElfColS",f:LE},15:{n:"PtgElfColSV",f:ME},16:{n:"PtgElfRadicalLel",f:HE},25:{n:"PtgList",f:GE},29:{n:"PtgSxName",f:KE},255:{}},ew={0:{n:"PtgAttrNoop",f:JE},1:{n:"PtgAttrSemi",f:aE},2:{n:"PtgAttrIf",f:iE},4:{n:"PtgAttrChoose",f:rE},8:{n:"PtgAttrGoto",f:nE},16:{n:"PtgAttrSum",f:xE},32:{n:"PtgAttrBaxcel",f:tl},33:{n:"PtgAttrBaxcel",f:tl},64:{n:"PtgAttrSpace",f:oE},65:{n:"PtgAttrSpaceSemi",f:fE},128:{n:"PtgAttrIfError",f:sE},255:{}};function tw(e,t,r,n){if(n.biff<8)return Ar(e,t);for(var i=e.l+t,s=[],a=0;a!==r.length;++a)switch(r[a][0]){case"PtgArray":r[a][1]=yE(e,0,n),s.push(r[a][1]);break;case"PtgMemArea":r[a][2]=SE(e,r[a][1],n),s.push(r[a][2]);break;case"PtgExp":n&&n.biff==12&&(r[a][1][1]=e.read_shift(4),s.push(r[a][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[a][0]}return t=i-e.l,t!==0&&s.push(Ar(e,t)),s}function rw(e,t,r){for(var n=e.l+t,i,s,a=[];n!=e.l;)t=n-e.l,s=e[e.l],i=rl[s]||rl[ZE[s]],(s===24||s===25)&&(i=(s===24?QE:ew)[e[e.l+1]]),!i||!i.f?Ar(e,t):a.push([i.n,i.f(e,t,r)]);return a}function nw(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],i=[],s=0;s<n.length;++s){var a=n[s];if(a)switch(a[0]){case 2:i.push('"'+a[1].replace(/"/g,'""')+'"');break;default:i.push(a[1])}else i.push("")}t.push(i.join(","))}return t.join(";")}var iw={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function sw(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function xh(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var i="";if(r.biff>8)switch(e[n[0]][0]){case 357:return i=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?i:i+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return i=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?i:i+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(s){return s.Name}).join(";;");default:return e[n[0]][0][3]?(i=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?i:i+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function nl(e,t,r){var n=xh(e,t,r);return n=="#REF"?n:sw(n,r)}function Xn(e,t,r,n,i){var s=i&&i.biff||8,a={s:{c:0,r:0}},o=[],f,l,c,u=0,h=0,d,x="";if(!e[0]||!e[0][0])return"";for(var p=-1,m="",C=0,F=e[0].length;C<F;++C){var y=e[0][C];switch(y[0]){case"PtgUminus":o.push("-"+o.pop());break;case"PtgUplus":o.push("+"+o.pop());break;case"PtgPercent":o.push(o.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(f=o.pop(),l=o.pop(),p>=0){switch(e[0][p][1][0]){case 0:m=tt(" ",e[0][p][1][1]);break;case 1:m=tt("\r",e[0][p][1][1]);break;default:if(m="",i.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][p][1][0])}l=l+m,p=-1}o.push(l+iw[y[0]]+f);break;case"PtgIsect":f=o.pop(),l=o.pop(),o.push(l+" "+f);break;case"PtgUnion":f=o.pop(),l=o.pop(),o.push(l+","+f);break;case"PtgRange":f=o.pop(),l=o.pop(),o.push(l+":"+f);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":c=Si(y[1][1],a,i),o.push(yi(c,s));break;case"PtgRefN":c=r?Si(y[1][1],r,i):y[1][1],o.push(yi(c,s));break;case"PtgRef3d":u=y[1][1],c=Si(y[1][2],a,i),x=nl(n,u,i),o.push(x+"!"+yi(c,s));break;case"PtgFunc":case"PtgFuncVar":var R=y[1][0],z=y[1][1];R||(R=0),R&=127;var se=R==0?[]:o.slice(-R);o.length-=R,z==="User"&&(z=se.shift()),o.push(z+"("+se.join(",")+")");break;case"PtgBool":o.push(y[1]?"TRUE":"FALSE");break;case"PtgInt":o.push(y[1]);break;case"PtgNum":o.push(String(y[1]));break;case"PtgStr":o.push('"'+y[1].replace(/"/g,'""')+'"');break;case"PtgErr":o.push(y[1]);break;case"PtgAreaN":d=Wf(y[1][1],r?{s:r}:a,i),o.push(Ha(d,i));break;case"PtgArea":d=Wf(y[1][1],a,i),o.push(Ha(d,i));break;case"PtgArea3d":u=y[1][1],d=y[1][2],x=nl(n,u,i),o.push(x+"!"+Ha(d,i));break;case"PtgAttrSum":o.push("SUM("+o.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":h=y[1][2];var D=(n.names||[])[h-1]||(n[0]||[])[h],Y=D?D.Name:"SH33TJSNAME"+String(h);Y&&Y.slice(0,6)=="_xlfn."&&!i.xlfn&&(Y=Y.slice(6)),o.push(Y);break;case"PtgNameX":var M=y[1][1];h=y[1][2];var ee;if(i.biff<=5)M<0&&(M=-M),n[M]&&(ee=n[M][h]);else{var re="";if(((n[M]||[])[0]||[])[0]==14849||(((n[M]||[])[0]||[])[0]==1025?n[M][h]&&n[M][h].itab>0&&(re=n.SheetNames[n[M][h].itab-1]+"!"):re=n.SheetNames[h-1]+"!"),n[M]&&n[M][h])re+=n[M][h].Name;else if(n[0]&&n[0][h])re+=n[0][h].Name;else{var ne=(xh(n,M,i)||"").split(";;");ne[h-1]?re=ne[h-1]:re+="SH33TJSERRX"}o.push(re);break}ee||(ee={Name:"SH33TJSERRY"}),o.push(ee.Name);break;case"PtgParen":var de="(",be=")";if(p>=0){switch(m="",e[0][p][1][0]){case 2:de=tt(" ",e[0][p][1][1])+de;break;case 3:de=tt("\r",e[0][p][1][1])+de;break;case 4:be=tt(" ",e[0][p][1][1])+be;break;case 5:be=tt("\r",e[0][p][1][1])+be;break;default:if(i.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][p][1][0])}p=-1}o.push(de+o.pop()+be);break;case"PtgRefErr":o.push("#REF!");break;case"PtgRefErr3d":o.push("#REF!");break;case"PtgExp":c={c:y[1][1],r:y[1][0]};var ye={c:r.c,r:r.r};if(n.sharedf[$e(c)]){var Ne=n.sharedf[$e(c)];o.push(Xn(Ne,a,ye,n,i))}else{var Te=!1;for(f=0;f!=n.arrayf.length;++f)if(l=n.arrayf[f],!(c.c<l[0].s.c||c.c>l[0].e.c)&&!(c.r<l[0].s.r||c.r>l[0].e.r)){o.push(Xn(l[1],a,ye,n,i)),Te=!0;break}Te||o.push(y[1])}break;case"PtgArray":o.push("{"+nw(y[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":p=C;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":o.push("");break;case"PtgAreaErr":o.push("#REF!");break;case"PtgAreaErr3d":o.push("#REF!");break;case"PtgList":o.push("Table"+y[1].idx+"[#"+y[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(y));default:throw new Error("Unrecognized Formula Token: "+String(y))}var Oe=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(i.biff!=3&&p>=0&&Oe.indexOf(e[0][C][0])==-1){y=e[0][p];var Ge=!0;switch(y[1][0]){case 4:Ge=!1;case 0:m=tt(" ",y[1][1]);break;case 5:Ge=!1;case 1:m=tt("\r",y[1][1]);break;default:if(m="",i.WTF)throw new Error("Unexpected PtgAttrSpaceType "+y[1][0])}o.push((Ge?m:"")+o.pop()+(Ge?"":m)),p=-1}}if(o.length>1&&i.WTF)throw new Error("bad formula stack");return o[0]}function aw(e){if(e==null){var t=X(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return mn(e);return mn(0)}function ow(e,t,r,n,i){var s=gn(t,r,i),a=aw(e.v),o=X(6),f=33;o.write_shift(2,f),o.write_shift(4,0);for(var l=X(e.bf.length),c=0;c<e.bf.length;++c)l[c]=e.bf[c];var u=mt([s,a,o,l]);return u}function ga(e,t,r){var n=e.read_shift(4),i=rw(e,n,r),s=e.read_shift(4),a=s>0?tw(e,s,i,r):null;return[i,a]}var fw=ga,va=ga,lw=ga,cw=ga,uw={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},mh={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},hw={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function dw(e){var t="of:="+e.replace(s0,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function pw(e){return e.replace(/\./,"!")}var Ai=typeof Map<"u";function f0(e,t,r){var n=0,i=e.length;if(r){if(Ai?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var s=Ai?r.get(t):r[t];n<s.length;++n)if(e[s[n]].t===t)return e.Count++,s[n]}}else for(;n<i;++n)if(e[n].t===t)return e.Count++,n;return e[i]={t},e.Count++,e.Unique++,r&&(Ai?(r.has(t)||r.set(t,[]),r.get(t).push(i)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(i))),i}function _a(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(kr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?n=Ws(t.wpx):t.wch!=null&&(n=t.wch),n>-1?(r.width=_o(n),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function gh(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function nn(e,t,r){var n=r.revssf[t.z!=null?t.z:"General"],i=60,s=e.length;if(n==null&&r.ssf){for(;i<392;++i)if(r.ssf[i]==null){hu(t.z,i),r.ssf[i]=t.z,r.revssf[t.z]=n=i;break}}for(i=0;i!=s;++i)if(e[i].numFmtId===n)return i;return e[s]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function xw(e,t,r){if(e&&e["!ref"]){var n=Ye(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function mw(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+st(e[r])+'"/>';return t+"</mergeCells>"}function gw(e,t,r,n,i){var s=!1,a={},o=null;if(n.bookType!=="xlsx"&&t.vbaraw){var f=t.SheetNames[r];try{t.Workbook&&(f=t.Workbook.Sheets[r].CodeName||f)}catch{}s=!0,a.codeName=Mi(je(f))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),o=(o||"")+ue("outlinePr",null,l)}!s&&!o||(i[i.length]=ue("sheetPr",o,a))}var vw=["objects","scenarios","selectLockedCells","selectUnlockedCells"],_w=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Ew(e){var t={sheet:1};return vw.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),_w.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=eh(e.password).toString(16).toUpperCase()),ue("sheetProtection",null,t)}function ww(e){return gh(e),ue("pageMargins",null,e)}function Tw(e,t){for(var r=["<cols>"],n,i=0;i!=t.length;++i)(n=t[i])&&(r[r.length]=ue("col",null,_a(i,n)));return r[r.length]="</cols>",r.join("")}function Sw(e,t,r,n){var i=typeof e.ref=="string"?e.ref:st(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,a=er(i);a.s.r==a.e.r&&(a.e.r=er(t["!ref"]).e.r,i=st(a));for(var o=0;o<s.length;++o){var f=s[o];if(f.Name=="_xlnm._FilterDatabase"&&f.Sheet==n){f.Ref="'"+r.SheetNames[n]+"'!"+i;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),ue("autoFilter",null,{ref:i})}function yw(e,t,r,n){var i={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(i.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),ue("sheetViews",ue("sheetView",null,i),{})}function Aw(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var i="",s=e.t,a=e.v;if(e.t!=="z")switch(e.t){case"b":i=e.v?"1":"0";break;case"n":i=""+e.v;break;case"e":i=Yi[e.v];break;case"d":n&&n.cellDates?i=kt(e.v,-1).toISOString():(e=jt(e),e.t="n",i=""+(e.v=Vt(kt(e.v)))),typeof e.z>"u"&&(e.z=rt[14]);break;default:i=e.v;break}var o=gt("v",je(i)),f={r:t},l=nn(n.cellXfs,e,n);switch(l!==0&&(f.s=l),e.t){case"n":break;case"d":f.t="d";break;case"b":f.t="b";break;case"e":f.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){o=gt("v",""+f0(n.Strings,e.v,n.revStrings)),f.t="s";break}f.t="str";break}if(e.t!=s&&(e.t=s,e.v=a),typeof e.f=="string"&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=ue("f",je(e.f),c)+(e.v!=null?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(f.cm=1),ue("c",o,f)}function Fw(e,t,r,n){var i=[],s=[],a=Ye(e["!ref"]),o="",f,l="",c=[],u=0,h=0,d=e["!rows"],x=Array.isArray(e),p={r:l},m,C=-1;for(h=a.s.c;h<=a.e.c;++h)c[h]=Ct(h);for(u=a.s.r;u<=a.e.r;++u){for(s=[],l=_t(u),h=a.s.c;h<=a.e.c;++h){f=c[h]+l;var F=x?(e[u]||[])[h]:e[f];F!==void 0&&(o=Aw(F,f,e,t))!=null&&s.push(o)}(s.length>0||d&&d[u])&&(p={r:l},d&&d[u]&&(m=d[u],m.hidden&&(p.hidden=1),C=-1,m.hpx?C=Vs(m.hpx):m.hpt&&(C=m.hpt),C>-1&&(p.ht=C,p.customHeight=1),m.level&&(p.outlineLevel=m.level)),i[i.length]=ue("row",s.join(""),p))}if(d)for(;u<d.length;++u)d&&d[u]&&(p={r:u+1},m=d[u],m.hidden&&(p.hidden=1),C=-1,m.hpx?C=Vs(m.hpx):m.hpt&&(C=m.hpt),C>-1&&(p.ht=C,p.customHeight=1),m.level&&(p.outlineLevel=m.level),i[i.length]=ue("row","",p));return i.join("")}function vh(e,t,r,n){var i=[ot,ue("worksheet",null,{xmlns:qn[0],"xmlns:r":lt.r})],s=r.SheetNames[e],a=0,o="",f=r.Sheets[s];f==null&&(f={});var l=f["!ref"]||"A1",c=Ye(l);if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),l=st(c)}n||(n={}),f["!comments"]=[];var u=[];gw(f,r,e,t,i),i[i.length]=ue("dimension",null,{ref:l}),i[i.length]=yw(f,t,e,r),t.sheetFormat&&(i[i.length]=ue("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),f["!cols"]!=null&&f["!cols"].length>0&&(i[i.length]=Tw(f,f["!cols"])),i[a=i.length]="<sheetData/>",f["!links"]=[],f["!ref"]!=null&&(o=Fw(f,t),o.length>0&&(i[i.length]=o)),i.length>a+1&&(i[i.length]="</sheetData>",i[a]=i[a].replace("/>",">")),f["!protect"]&&(i[i.length]=Ew(f["!protect"])),f["!autofilter"]!=null&&(i[i.length]=Sw(f["!autofilter"],f,r,e)),f["!merges"]!=null&&f["!merges"].length>0&&(i[i.length]=mw(f["!merges"]));var h=-1,d,x=-1;return f["!links"].length>0&&(i[i.length]="<hyperlinks>",f["!links"].forEach(function(p){p[1].Target&&(d={ref:p[0]},p[1].Target.charAt(0)!="#"&&(x=Ve(n,-1,je(p[1].Target).replace(/#.*$/,""),ke.HLINK),d["r:id"]="rId"+x),(h=p[1].Target.indexOf("#"))>-1&&(d.location=je(p[1].Target.slice(h+1))),p[1].Tooltip&&(d.tooltip=je(p[1].Tooltip)),i[i.length]=ue("hyperlink",null,d))}),i[i.length]="</hyperlinks>"),delete f["!links"],f["!margins"]!=null&&(i[i.length]=ww(f["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(i[i.length]=gt("ignoredErrors",ue("ignoredError",null,{numberStoredAsText:1,sqref:l}))),u.length>0&&(x=Ve(n,-1,"../drawings/drawing"+(e+1)+".xml",ke.DRAW),i[i.length]=ue("drawing",null,{"r:id":"rId"+x}),f["!drawing"]=u),f["!comments"].length>0&&(x=Ve(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",ke.VML),i[i.length]=ue("legacyDrawing",null,{"r:id":"rId"+x}),f["!legacy"]=x),i.length>1&&(i[i.length]="</worksheet>",i[1]=i[1].replace("/>",">")),i.join("")}function Cw(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var i=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=n,s&7&&(r.level=s&7),s&16&&(r.hidden=!0),s&32&&(r.hpt=i/20),r}function Ow(e,t,r){var n=X(145),i=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var s=320;i.hpx?s=Vs(i.hpx)*20:i.hpt&&(s=i.hpt*20),n.write_shift(2,s),n.write_shift(1,0);var a=0;i.level&&(a|=i.level),i.hidden&&(a|=16),(i.hpx||i.hpt)&&(a|=32),n.write_shift(1,a),n.write_shift(1,0);var o=0,f=n.l;n.l+=4;for(var l={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10||t.e.c<c<<10)){for(var u=-1,h=-1,d=c<<10;d<c+1<<10;++d){l.c=d;var x=Array.isArray(r)?(r[l.r]||[])[l.c]:r[$e(l)];x&&(u<0&&(u=d),h=d)}u<0||(++o,n.write_shift(4,u),n.write_shift(4,h))}var p=n.l;return n.l=f,n.write_shift(4,o),n.l=p,n.length>n.l?n.slice(0,n.l):n}function Rw(e,t,r,n){var i=Ow(n,r,t);(i.length>17||(t["!rows"]||[])[n])&&te(e,0,i)}var Pw=Sn,Dw=Zn;function Iw(){}function Nw(e,t){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=jv(e),r}function bw(e,t,r){r==null&&(r=X(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var i=1;i<3;++i)r.write_shift(1,0);return Bs({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),Nu(e,r),r.slice(0,r.l)}function kw(e){var t=ar(e);return[t]}function Lw(e,t,r){return r==null&&(r=X(8)),En(t,r)}function Mw(e){var t=wn(e);return[t]}function Bw(e,t,r){return r==null&&(r=X(4)),Tn(t,r)}function Uw(e){var t=ar(e),r=e.read_shift(1);return[t,r,"b"]}function Hw(e,t,r){return r==null&&(r=X(9)),En(t,r),r.write_shift(1,e.v?1:0),r}function Ww(e){var t=wn(e),r=e.read_shift(1);return[t,r,"b"]}function Vw(e,t,r){return r==null&&(r=X(5)),Tn(t,r),r.write_shift(1,e.v?1:0),r}function jw(e){var t=ar(e),r=e.read_shift(1);return[t,r,"e"]}function $w(e,t,r){return r==null&&(r=X(9)),En(t,r),r.write_shift(1,e.v),r}function Gw(e){var t=wn(e),r=e.read_shift(1);return[t,r,"e"]}function Kw(e,t,r){return r==null&&(r=X(8)),Tn(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function Xw(e){var t=ar(e),r=e.read_shift(4);return[t,r,"s"]}function zw(e,t,r){return r==null&&(r=X(12)),En(t,r),r.write_shift(4,t.v),r}function Yw(e){var t=wn(e),r=e.read_shift(4);return[t,r,"s"]}function qw(e,t,r){return r==null&&(r=X(8)),Tn(t,r),r.write_shift(4,t.v),r}function Jw(e){var t=ar(e),r=Qn(e);return[t,r,"n"]}function Zw(e,t,r){return r==null&&(r=X(16)),En(t,r),mn(e.v,r),r}function Qw(e){var t=wn(e),r=Qn(e);return[t,r,"n"]}function eT(e,t,r){return r==null&&(r=X(12)),Tn(t,r),mn(e.v,r),r}function tT(e){var t=ar(e),r=bu(e);return[t,r,"n"]}function rT(e,t,r){return r==null&&(r=X(12)),En(t,r),ku(e.v,r),r}function nT(e){var t=wn(e),r=bu(e);return[t,r,"n"]}function iT(e,t,r){return r==null&&(r=X(8)),Tn(t,r),ku(e.v,r),r}function sT(e){var t=ar(e),r=e0(e);return[t,r,"is"]}function aT(e){var t=ar(e),r=Ot(e);return[t,r,"str"]}function oT(e,t,r){return r==null&&(r=X(12+4*e.v.length)),En(t,r),ut(e.v,r),r.length>r.l?r.slice(0,r.l):r}function fT(e){var t=wn(e),r=Ot(e);return[t,r,"str"]}function lT(e,t,r){return r==null&&(r=X(8+4*e.v.length)),Tn(t,r),ut(e.v,r),r.length>r.l?r.slice(0,r.l):r}function cT(e,t,r){var n=e.l+t,i=ar(e);i.r=r["!row"];var s=e.read_shift(1),a=[i,s,"b"];if(r.cellFormula){e.l+=2;var o=va(e,n-e.l,r);a[3]=Xn(o,null,i,r.supbooks,r)}else e.l=n;return a}function uT(e,t,r){var n=e.l+t,i=ar(e);i.r=r["!row"];var s=e.read_shift(1),a=[i,s,"e"];if(r.cellFormula){e.l+=2;var o=va(e,n-e.l,r);a[3]=Xn(o,null,i,r.supbooks,r)}else e.l=n;return a}function hT(e,t,r){var n=e.l+t,i=ar(e);i.r=r["!row"];var s=Qn(e),a=[i,s,"n"];if(r.cellFormula){e.l+=2;var o=va(e,n-e.l,r);a[3]=Xn(o,null,i,r.supbooks,r)}else e.l=n;return a}function dT(e,t,r){var n=e.l+t,i=ar(e);i.r=r["!row"];var s=Ot(e),a=[i,s,"str"];if(r.cellFormula){e.l+=2;var o=va(e,n-e.l,r);a[3]=Xn(o,null,i,r.supbooks,r)}else e.l=n;return a}var pT=Sn,xT=Zn;function mT(e,t){return t==null&&(t=X(4)),t.write_shift(4,e),t}function gT(e,t){var r=e.l+t,n=Sn(e),i=t0(e),s=Ot(e),a=Ot(e),o=Ot(e);e.l=r;var f={rfx:n,relId:i,loc:s,display:o};return a&&(f.Tooltip=a),f}function vT(e,t){var r=X(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));Zn({s:ct(e[0]),e:ct(e[0])},r),r0("rId"+t,r);var n=e[1].Target.indexOf("#"),i=n==-1?"":e[1].Target.slice(n+1);return ut(i||"",r),ut(e[1].Tooltip||"",r),ut("",r),r.slice(0,r.l)}function _T(){}function ET(e,t,r){var n=e.l+t,i=Lu(e),s=e.read_shift(1),a=[i];if(a[2]=s,r.cellFormula){var o=fw(e,n-e.l,r);a[1]=o}else e.l=n;return a}function wT(e,t,r){var n=e.l+t,i=Sn(e),s=[i];if(r.cellFormula){var a=cw(e,n-e.l,r);s[1]=a,e.l=n}else e.l=n;return s}function TT(e,t,r){r==null&&(r=X(18));var n=_a(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(n.width||10)*256),r.write_shift(4,0);var i=0;return t.hidden&&(i|=1),typeof n.width=="number"&&(i|=2),t.level&&(i|=t.level<<8),r.write_shift(2,i),r}var _h=["left","right","top","bottom","header","footer"];function ST(e){var t={};return _h.forEach(function(r){t[r]=Qn(e)}),t}function yT(e,t){return t==null&&(t=X(6*8)),gh(e),_h.forEach(function(r){mn(e[r],t)}),t}function AT(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function FT(e,t,r){r==null&&(r=X(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function CT(e){var t=X(24);return t.write_shift(4,4),t.write_shift(4,1),Zn(e,t),t}function OT(e,t){return t==null&&(t=X(16*4+2)),t.write_shift(2,e.password?eh(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function RT(){}function PT(){}function DT(e,t,r,n,i,s,a){if(t.v===void 0)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":t=jt(t),t.z=t.z||rt[14],t.v=Vt(kt(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v;break}var f={r,c:n};switch(f.s=nn(i.cellXfs,t,i),t.l&&s["!links"].push([$e(f),t.l]),t.c&&s["!comments"].push([$e(f),t.c]),t.t){case"s":case"str":return i.bookSST?(o=f0(i.Strings,t.v,i.revStrings),f.t="s",f.v=o,a?te(e,18,qw(t,f)):te(e,7,zw(t,f))):(f.t="str",a?te(e,17,lT(t,f)):te(e,6,oT(t,f))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?a?te(e,13,iT(t,f)):te(e,2,rT(t,f)):a?te(e,16,eT(t,f)):te(e,5,Zw(t,f)),!0;case"b":return f.t="b",a?te(e,15,Vw(t,f)):te(e,4,Hw(t,f)),!0;case"e":return f.t="e",a?te(e,14,Kw(t,f)):te(e,3,$w(t,f)),!0}return a?te(e,12,Bw(t,f)):te(e,1,Lw(t,f)),!0}function IT(e,t,r,n){var i=Ye(t["!ref"]||"A1"),s,a="",o=[];te(e,145);var f=Array.isArray(t),l=i.e.r;t["!rows"]&&(l=Math.max(i.e.r,t["!rows"].length-1));for(var c=i.s.r;c<=l;++c){a=_t(c),Rw(e,t,i,c);var u=!1;if(c<=i.e.r)for(var h=i.s.c;h<=i.e.c;++h){c===i.s.r&&(o[h]=Ct(h)),s=o[h]+a;var d=f?(t[c]||[])[h]:t[s];if(!d){u=!1;continue}u=DT(e,d,c,h,n,t,u)}}te(e,146)}function NT(e,t){!t||!t["!merges"]||(te(e,177,mT(t["!merges"].length)),t["!merges"].forEach(function(r){te(e,176,xT(r))}),te(e,178))}function bT(e,t){!t||!t["!cols"]||(te(e,390),t["!cols"].forEach(function(r,n){r&&te(e,60,TT(n,r))}),te(e,391))}function kT(e,t){!t||!t["!ref"]||(te(e,648),te(e,649,CT(Ye(t["!ref"]))),te(e,650))}function LT(e,t,r){t["!links"].forEach(function(n){if(n[1].Target){var i=Ve(r,-1,n[1].Target.replace(/#.*$/,""),ke.HLINK);te(e,494,vT(n,i))}}),delete t["!links"]}function MT(e,t,r,n){if(t["!comments"].length>0){var i=Ve(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",ke.VML);te(e,551,r0("rId"+i)),t["!legacy"]=i}}function BT(e,t,r,n){if(t["!autofilter"]){var i=t["!autofilter"],s=typeof i.ref=="string"?i.ref:st(i.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var a=r.Workbook.Names,o=er(s);o.s.r==o.e.r&&(o.e.r=er(t["!ref"]).e.r,s=st(o));for(var f=0;f<a.length;++f){var l=a[f];if(l.Name=="_xlnm._FilterDatabase"&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+s;break}}f==a.length&&a.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+s}),te(e,161,Zn(Ye(s))),te(e,162)}}function UT(e,t,r){te(e,133),te(e,137,FT(t,r)),te(e,138),te(e,134)}function HT(e,t){t["!protect"]&&te(e,535,OT(t["!protect"]))}function WT(e,t,r,n){var i=Wt(),s=r.SheetNames[e],a=r.Sheets[s]||{},o=s;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch{}var f=Ye(a["!ref"]||"A1");if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+(a["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575)}return a["!links"]=[],a["!comments"]=[],te(i,129),(r.vbaraw||a["!outline"])&&te(i,147,bw(o,a["!outline"])),te(i,148,Dw(f)),UT(i,a,r.Workbook),bT(i,a),IT(i,a,e,t),HT(i,a),BT(i,a,r,e),NT(i,a),LT(i,a,n),a["!margins"]&&te(i,476,yT(a["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&kT(i,a),MT(i,a,e,n),te(i,130),i.end()}function VT(e,t){e.l+=10;var r=Ot(e);return{name:r}}var jT=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function $T(e){return!e.Workbook||!e.Workbook.WBProps?"false":wv(e.Workbook.WBProps.date1904)?"true":"false"}var GT="][*?/\\".split("");function Eh(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return GT.forEach(function(n){if(e.indexOf(n)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}function KT(e,t,r){e.forEach(function(n,i){Eh(n);for(var s=0;s<i;++s)if(n==e[s])throw new Error("Duplicate Sheet Name: "+n);if(r){var a=t&&t[i]&&t[i].CodeName||n;if(a.charCodeAt(0)==95&&a.length>22)throw new Error("Bad Code Name: Worksheet"+a)}})}function XT(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];KT(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)xw(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function wh(e){var t=[ot];t[t.length]=ue("workbook",null,{xmlns:qn[0],"xmlns:r":lt.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(jT.forEach(function(o){e.Workbook.WBProps[o[0]]!=null&&e.Workbook.WBProps[o[0]]!=o[1]&&(n[o[0]]=e.Workbook.WBProps[o[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=ue("workbookPr",null,n);var i=e.Workbook&&e.Workbook.Sheets||[],s=0;if(i&&i[0]&&i[0].Hidden){for(t[t.length]="<bookViews>",s=0;s!=e.SheetNames.length&&!(!i[s]||!i[s].Hidden);++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",s=0;s!=e.SheetNames.length;++s){var a={name:je(e.SheetNames[s].slice(0,31))};if(a.sheetId=""+(s+1),a["r:id"]="rId"+(s+1),i[s])switch(i[s].Hidden){case 1:a.state="hidden";break;case 2:a.state="veryHidden";break}t[t.length]=ue("sheet",null,a)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(o){var f={name:o.Name};o.Comment&&(f.comment=o.Comment),o.Sheet!=null&&(f.localSheetId=""+o.Sheet),o.Hidden&&(f.hidden="1"),o.Ref&&(t[t.length]=ue("definedName",je(o.Ref),f))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function zT(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=vo(e),r.name=Ot(e),r}function YT(e,t){return t||(t=X(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),r0(e.strRelID,t),ut(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function qT(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var i=t>8?Ot(e):"";return i.length>0&&(r.CodeName=i),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function JT(e,t){t||(t=X(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),Nu(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function ZT(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var i=e.read_shift(4),s=$v(e),a=lw(e,0,r),o=t0(e);e.l=n;var f={Name:s,Ptg:a};return i<268435455&&(f.Sheet=i),o&&(f.Comment=o),f}function QT(e,t){te(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,i={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};te(e,156,YT(i))}te(e,144)}function eS(e,t){t||(t=X(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return ut("SheetJS",t),ut(Ps.version,t),ut(Ps.version,t),ut("7262",t),t.length>t.l?t.slice(0,t.l):t}function tS(e,t){t||(t=X(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function rS(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,n=0,i=-1,s=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&i==-1?i=n:r[n].Hidden==1&&s==-1&&(s=n);s>i||(te(e,135),te(e,158,tS(i)),te(e,136))}}function nS(e,t){var r=Wt();return te(r,131),te(r,128,eS()),te(r,153,JT(e.Workbook&&e.Workbook.WBProps||null)),rS(r,e),QT(r,e),te(r,132),r.end()}function iS(e,t,r){return(t.slice(-4)===".bin"?nS:wh)(e)}function sS(e,t,r,n,i){return(t.slice(-4)===".bin"?WT:vh)(e,r,n,i)}function aS(e,t,r){return(t.slice(-4)===".bin"?y_:nh)(e,r)}function oS(e,t,r){return(t.slice(-4)===".bin"?J2:Qu)(e,r)}function fS(e,t,r){return(t.slice(-4)===".bin"?H_:fh)(e)}function lS(e){return(e.slice(-4)===".bin"?I_:ah)()}function cS(e,t){var r=[];return e.Props&&r.push(a2(e.Props,t)),e.Custprops&&r.push(o2(e.Props,e.Custprops)),r.join("")}function uS(){return""}function hS(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(n,i){var s=[];s.push(ue("NumberFormat",null,{"ss:Format":je(rt[n.numFmtId])}));var a={"ss:ID":"s"+(21+i)};r.push(ue("Style",s.join(""),a))}),ue("Styles",r.join(""))}function Th(e){return ue("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+a0(e.Ref,{r:0,c:0})})}function dS(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var i=t[n];i.Sheet==null&&(i.Name.match(/^_xlfn\./)||r.push(Th(i)))}return ue("Names",r.join(""))}function pS(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var i=n.Workbook.Names,s=[],a=0;a<i.length;++a){var o=i[a];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(Th(o)))}return s.join("")}function xS(e,t,r,n){if(!e)return"";var i=[];if(e["!margins"]&&(i.push("<PageSetup>"),e["!margins"].header&&i.push(ue("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&i.push(ue("Footer",null,{"x:Margin":e["!margins"].footer})),i.push(ue("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),i.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)i.push(ue("Visible",n.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&!(n.Workbook.Sheets[s]&&!n.Workbook.Sheets[s].Hidden);++s);s==r&&i.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&i.push("<DisplayRightToLeft/>"),e["!protect"]&&(i.push(gt("ProtectContents","True")),e["!protect"].objects&&i.push(gt("ProtectObjects","True")),e["!protect"].scenarios&&i.push(gt("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?i.push(gt("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&i.push(gt("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(a){e["!protect"][a[0]]&&i.push("<"+a[1]+"/>")})),i.length==0?"":ue("WorksheetOptions",i.join(""),{xmlns:qt.x})}function mS(e){return e.map(function(t){var r=Ev(t.t||""),n=ue("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return ue("Comment",n,{"ss:Author":t.a})}).join("")}function gS(e,t,r,n,i,s,a){if(!e||e.v==null&&e.f==null)return"";var o={};if(e.f&&(o["ss:Formula"]="="+je(a0(e.f,a))),e.F&&e.F.slice(0,t.length)==t){var f=ct(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(f.r==a.r?"":"["+(f.r-a.r)+"]")+"C"+(f.c==a.c?"":"["+(f.c-a.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=je(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=je(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],c=0;c!=l.length;++c)l[c].s.c!=a.c||l[c].s.r!=a.r||(l[c].e.c>l[c].s.c&&(o["ss:MergeAcross"]=l[c].e.c-l[c].s.c),l[c].e.r>l[c].s.r&&(o["ss:MergeDown"]=l[c].e.r-l[c].s.r));var u="",h="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":u="Number",h=String(e.v);break;case"b":u="Boolean",h=e.v?"1":"0";break;case"e":u="Error",h=Yi[e.v];break;case"d":u="DateTime",h=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||rt[14]);break;case"s":u="String",h=_v(e.v||"");break}var d=nn(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=a.c+1;var x=e.v!=null?h:"",p=e.t=="z"?"":'<Data ss:Type="'+u+'">'+x+"</Data>";return(e.c||[]).length>0&&(p+=mS(e.c)),ue("Cell",p,o)}function vS(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=rh(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function _S(e,t,r,n){if(!e["!ref"])return"";var i=Ye(e["!ref"]),s=e["!merges"]||[],a=0,o=[];e["!cols"]&&e["!cols"].forEach(function(m,C){i0(m);var F=!!m.width,y=_a(C,m),R={"ss:Index":C+1};F&&(R["ss:Width"]=Hs(y.width)),m.hidden&&(R["ss:Hidden"]="1"),o.push(ue("Column",null,R))});for(var f=Array.isArray(e),l=i.s.r;l<=i.e.r;++l){for(var c=[vS(l,(e["!rows"]||[])[l])],u=i.s.c;u<=i.e.c;++u){var h=!1;for(a=0;a!=s.length;++a)if(!(s[a].s.c>u)&&!(s[a].s.r>l)&&!(s[a].e.c<u)&&!(s[a].e.r<l)){(s[a].s.c!=u||s[a].s.r!=l)&&(h=!0);break}if(!h){var d={r:l,c:u},x=$e(d),p=f?(e[l]||[])[u]:e[x];c.push(gS(p,x,e,t,r,n,d))}}c.push("</Row>"),c.length>2&&o.push(c.join(""))}return o.join("")}function ES(e,t,r){var n=[],i=r.SheetNames[e],s=r.Sheets[i],a=s?pS(s,t,e,r):"";return a.length>0&&n.push("<Names>"+a+"</Names>"),a=s?_S(s,t,e,r):"",a.length>0&&n.push("<Table>"+a+"</Table>"),n.push(xS(s,t,e,r)),n.join("")}function wS(e,t){t||(t={}),e.SSF||(e.SSF=jt(rt)),e.SSF&&(pa(),da(e.SSF),t.revssf=xa(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],nn(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(cS(e,t)),r.push(uS()),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(ue("Worksheet",ES(n,t,e),{"ss:Name":je(e.SheetNames[n])}));return r[2]=hS(e,t),r[3]=dS(e),ot+ue("Workbook",r.join(""),{xmlns:qt.ss,"xmlns:o":qt.o,"xmlns:x":qt.x,"xmlns:ss":qt.ss,"xmlns:dt":qt.dt,"xmlns:html":qt.html})}var ja={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function TS(e,t){var r=[],n=[],i=[],s=0,a,o=Pf(jf,"n"),f=Pf($f,"n");if(e.Props)for(a=Et(e.Props),s=0;s<a.length;++s)(Object.prototype.hasOwnProperty.call(o,a[s])?r:Object.prototype.hasOwnProperty.call(f,a[s])?n:i).push([a[s],e.Props[a[s]]]);if(e.Custprops)for(a=Et(e.Custprops),s=0;s<a.length;++s)Object.prototype.hasOwnProperty.call(e.Props||{},a[s])||(Object.prototype.hasOwnProperty.call(o,a[s])?r:Object.prototype.hasOwnProperty.call(f,a[s])?n:i).push([a[s],e.Custprops[a[s]]]);var l=[];for(s=0;s<i.length;++s)Ku.indexOf(i[s][0])>-1||ju.indexOf(i[s][0])>-1||i[s][1]!=null&&l.push(i[s]);n.length&&Ke.utils.cfb_add(t,"/SummaryInformation",Yf(n,ja.SI,f,$f)),(r.length||l.length)&&Ke.utils.cfb_add(t,"/DocumentSummaryInformation",Yf(r,ja.DSI,o,jf,l.length?l:null,ja.UDI))}function SS(e,t){var r=t||{},n=Ke.utils.cfb_new({root:"R"}),i="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":i="/Workbook",r.biff=8;break;case"biff5":i="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Ke.utils.cfb_add(n,i,Sh(e,r)),r.biff==8&&(e.Props||e.Custprops)&&TS(e,n),r.biff==8&&e.vbaraw&&W_(n,Ke.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),n}var yS={0:{f:Cw},1:{f:kw},2:{f:tT},3:{f:jw},4:{f:Uw},5:{f:Jw},6:{f:aT},7:{f:Xw},8:{f:dT},9:{f:hT},10:{f:cT},11:{f:uT},12:{f:Mw},13:{f:nT},14:{f:Gw},15:{f:Ww},16:{f:Qw},17:{f:fT},18:{f:Yw},19:{f:e0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:ZT},40:{},42:{},43:{f:a_},44:{f:i_},45:{f:l_},46:{f:u_},47:{f:c_},48:{},49:{f:Mv},50:{},51:{f:C_},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:B2},62:{f:sT},63:{f:N_},64:{f:RT},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Ar,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:AT},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Nw},148:{f:Pw,p:16},151:{f:_T},152:{},153:{f:qT},154:{},155:{},156:{f:zT},157:{},158:{},159:{T:1,f:z2},160:{T:-1},161:{T:1,f:Sn},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:pT},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:A_},336:{T:-1},337:{f:P_,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:vo},357:{},358:{},359:{},360:{T:1},361:{},362:{f:I2},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:ET},427:{f:wT},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:ST},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:Iw},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:gT},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:vo},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:B_},633:{T:1},634:{T:-1},635:{T:1,f:L_},636:{T:-1},637:{f:Wv},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:VT},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:PT},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function he(e,t,r,n){var i=t;if(!isNaN(i)){var s=n||(r||[]).length||0,a=e.next(4);a.write_shift(2,i),a.write_shift(2,s),s>0&&Jo(r)&&e.push(r)}}function AS(e,t,r,n){var i=(r||[]).length||0;if(i<=8224)return he(e,t,r,i);var s=t;if(!isNaN(s)){for(var a=r.parts||[],o=0,f=0,l=0;l+(a[o]||8224)<=8224;)l+=a[o]||8224,o++;var c=e.next(4);for(c.write_shift(2,s),c.write_shift(2,l),e.push(r.slice(f,f+l)),f+=l;f<i;){for(c=e.next(4),c.write_shift(2,60),l=0;l+(a[o]||8224)<=8224;)l+=a[o]||8224,o++;c.write_shift(2,l),e.push(r.slice(f,f+l)),f+=l}}}function Ji(e,t,r){return e||(e=X(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function FS(e,t,r,n){var i=X(9);return Ji(i,e,t),zu(r,n||"b",i),i}function CS(e,t,r){var n=X(8+2*r.length);return Ji(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function OS(e,t,r,n){if(t.v!=null)switch(t.t){case"d":case"n":var i=t.t=="d"?Vt(kt(t.v)):t.v;i==(i|0)&&i>=0&&i<65536?he(e,2,V2(r,n,i)):he(e,3,W2(r,n,i));return;case"b":case"e":he(e,5,FS(r,n,t.v,t.t));return;case"s":case"str":he(e,4,CS(r,n,(t.v||"").slice(0,255)));return}he(e,1,Ji(null,r,n))}function RS(e,t,r,n){var i=Array.isArray(t),s=Ye(t["!ref"]||"A1"),a,o="",f=[];if(s.e.c>255||s.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");s.e.c=Math.min(s.e.c,255),s.e.r=Math.min(s.e.c,16383),a=st(s)}for(var l=s.s.r;l<=s.e.r;++l){o=_t(l);for(var c=s.s.c;c<=s.e.c;++c){l===s.s.r&&(f[c]=Ct(c)),a=f[c]+o;var u=i?(t[l]||[])[c]:t[a];u&&OS(e,u,l,c)}}}function PS(e,t){for(var r=t||{},n=Wt(),i=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==r.sheet&&(i=s);if(i==0&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return he(n,r.biff==4?1033:r.biff==3?521:9,n0(e,16,r)),RS(n,e.Sheets[e.SheetNames[i]],i,r),he(n,10),n.end()}function DS(e,t,r){he(e,49,y2({sz:12,name:"Arial"},r))}function IS(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var i=n[0];i<=n[1];++i)t[i]!=null&&he(e,1054,C2(i,t[i],r))})}function NS(e,t){var r=X(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),he(e,2151,r),r=X(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),Ju(Ye(t["!ref"]||"A1"),r),r.write_shift(4,4),he(e,2152,r)}function bS(e,t){for(var r=0;r<16;++r)he(e,224,Jf({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(n){he(e,224,Jf(n,0,t))})}function kS(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];he(e,440,k2(n)),n[1].Tooltip&&he(e,2048,L2(n))}delete t["!links"]}function LS(e,t){if(t){var r=0;t.forEach(function(n,i){++r<=256&&n&&he(e,125,U2(_a(i,n),i))})}}function MS(e,t,r,n,i){var s=16+nn(i.cellXfs,t,i);if(t.v==null&&!t.bf){he(e,513,gn(r,n,s));return}if(t.bf)he(e,6,ow(t,r,n,i,s));else switch(t.t){case"d":case"n":var a=t.t=="d"?Vt(kt(t.v)):t.v;he(e,515,D2(r,n,a,s));break;case"b":case"e":he(e,517,P2(r,n,t.v,s,i,t.t));break;case"s":case"str":if(i.bookSST){var o=f0(i.Strings,t.v,i.revStrings);he(e,253,A2(r,n,o,s))}else he(e,516,F2(r,n,(t.v||"").slice(0,255),s,i));break;default:he(e,513,gn(r,n,s))}}function BS(e,t,r){var n=Wt(),i=r.SheetNames[e],s=r.Sheets[i]||{},a=(r||{}).Workbook||{},o=(a.Sheets||[])[e]||{},f=Array.isArray(s),l=t.biff==8,c,u="",h=[],d=Ye(s["!ref"]||"A1"),x=l?65536:16384;if(d.e.c>255||d.e.r>=x){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,x-1)}he(n,2057,n0(r,16,t)),he(n,13,nr(1)),he(n,12,nr(100)),he(n,15,Nt(!0)),he(n,17,Nt(!1)),he(n,16,mn(.001)),he(n,95,Nt(!0)),he(n,42,Nt(!1)),he(n,43,Nt(!1)),he(n,130,nr(1)),he(n,128,R2()),he(n,131,Nt(!1)),he(n,132,Nt(!1)),l&&LS(n,s["!cols"]),he(n,512,O2(d,t)),l&&(s["!links"]=[]);for(var p=d.s.r;p<=d.e.r;++p){u=_t(p);for(var m=d.s.c;m<=d.e.c;++m){p===d.s.r&&(h[m]=Ct(m)),c=h[m]+u;var C=f?(s[p]||[])[m]:s[c];C&&(MS(n,C,p,m,t),l&&C.l&&s["!links"].push([c,C.l]))}}var F=o.CodeName||o.name||i;return l&&he(n,574,S2((a.Views||[])[0])),l&&(s["!merges"]||[]).length&&he(n,229,b2(s["!merges"])),l&&kS(n,s),he(n,442,qu(F)),l&&NS(n,s),he(n,10),n.end()}function US(e,t,r){var n=Wt(),i=(e||{}).Workbook||{},s=i.Sheets||[],a=i.WBProps||{},o=r.biff==8,f=r.biff==5;if(he(n,2057,n0(e,5,r)),r.bookType=="xla"&&he(n,135),he(n,225,o?nr(1200):null),he(n,193,c2(2)),f&&he(n,191),f&&he(n,192),he(n,226),he(n,92,_2("SheetJS",r)),he(n,66,nr(o?1200:1252)),o&&he(n,353,nr(0)),o&&he(n,448),he(n,317,H2(e.SheetNames.length)),o&&e.vbaraw&&he(n,211),o&&e.vbaraw){var l=a.CodeName||"ThisWorkbook";he(n,442,qu(l))}he(n,156,nr(17)),he(n,25,Nt(!1)),he(n,18,Nt(!1)),he(n,19,nr(0)),o&&he(n,431,Nt(!1)),o&&he(n,444,nr(0)),he(n,61,T2()),he(n,64,Nt(!1)),he(n,141,nr(0)),he(n,34,Nt($T(e)=="true")),he(n,14,Nt(!0)),o&&he(n,439,Nt(!1)),he(n,218,nr(0)),DS(n,e,r),IS(n,e.SSF,r),bS(n,r),o&&he(n,352,Nt(!1));var c=n.end(),u=Wt();o&&he(u,140,M2()),o&&r.Strings&&AS(u,252,w2(r.Strings)),he(u,10);var h=u.end(),d=Wt(),x=0,p=0;for(p=0;p<e.SheetNames.length;++p)x+=(o?12:11)+(o?2:1)*e.SheetNames[p].length;var m=c.length+x+h.length;for(p=0;p<e.SheetNames.length;++p){var C=s[p]||{};he(d,133,E2({pos:m,hs:C.Hidden||0,dt:0,name:e.SheetNames[p]},r)),m+=t[p].length}var F=d.end();if(x!=F.length)throw new Error("BS8 "+x+" != "+F.length);var y=[];return c.length&&y.push(c),F.length&&y.push(F),h.length&&y.push(h),mt(y)}function HS(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=jt(rt)),e&&e.SSF&&(pa(),da(e.SSF),r.revssf=xa(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,l0(r),r.cellXfs=[],nn(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var i=0;i<e.SheetNames.length;++i)n[n.length]=BS(i,r,e);return n.unshift(US(e,n,r)),mt(n)}function Sh(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(!(!n||!n["!ref"])){var i=er(n["!ref"]);i.e.c>255&&typeof console<"u"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var s=t||{};switch(s.biff||2){case 8:case 5:return HS(e,t);case 4:case 3:case 2:return PS(e,t)}throw new Error("invalid type "+s.bookType+" for BIFF")}function WS(e,t,r,n){for(var i=e["!merges"]||[],s=[],a=t.s.c;a<=t.e.c;++a){for(var o=0,f=0,l=0;l<i.length;++l)if(!(i[l].s.r>r||i[l].s.c>a)&&!(i[l].e.r<r||i[l].e.c<a)){if(i[l].s.r<r||i[l].s.c<a){o=-1;break}o=i[l].e.r-i[l].s.r+1,f=i[l].e.c-i[l].s.c+1;break}if(!(o<0)){var c=$e({r,c:a}),u=n.dense?(e[r]||[])[a]:e[c],h=u&&u.v!=null&&(u.h||vv(u.w||(Ur(u),u.w)||""))||"",d={};o>1&&(d.rowspan=o),f>1&&(d.colspan=f),n.editable?h='<span contenteditable="true">'+h+"</span>":u&&(d["data-t"]=u&&u.t||"z",u.v!=null&&(d["data-v"]=u.v),u.z!=null&&(d["data-z"]=u.z),u.l&&(u.l.Target||"#").charAt(0)!="#"&&(h='<a href="'+u.l.Target+'">'+h+"</a>")),d.id=(n.id||"sjs")+"-"+c,s.push(ue("td",h,d))}}var x="<tr>";return x+s.join("")+"</tr>"}var VS='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',jS="</body></html>";function $S(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function yh(e,t){var r=t||{},n=r.header!=null?r.header:VS,i=r.footer!=null?r.footer:jS,s=[n],a=er(e["!ref"]);r.dense=Array.isArray(e),s.push($S(e,a,r));for(var o=a.s.r;o<=a.e.r;++o)s.push(WS(e,a,o,r));return s.push("</table>"+i),s.join("")}function Ah(e,t,r){var n=r||{},i=0,s=0;if(n.origin!=null)if(typeof n.origin=="number")i=n.origin;else{var a=typeof n.origin=="string"?ct(n.origin):n.origin;i=a.r,s=a.c}var o=t.getElementsByTagName("tr"),f=Math.min(n.sheetRows||1e7,o.length),l={s:{r:0,c:0},e:{r:i,c:s}};if(e["!ref"]){var c=er(e["!ref"]);l.s.r=Math.min(l.s.r,c.s.r),l.s.c=Math.min(l.s.c,c.s.c),l.e.r=Math.max(l.e.r,c.e.r),l.e.c=Math.max(l.e.c,c.e.c),i==-1&&(l.e.r=i=c.e.r+1)}var u=[],h=0,d=e["!rows"]||(e["!rows"]=[]),x=0,p=0,m=0,C=0,F=0,y=0;for(e["!cols"]||(e["!cols"]=[]);x<o.length&&p<f;++x){var R=o[x];if(il(R)){if(n.display)continue;d[p]={hidden:!0}}var z=R.children;for(m=C=0;m<z.length;++m){var se=z[m];if(!(n.display&&il(se))){var D=se.hasAttribute("data-v")?se.getAttribute("data-v"):se.hasAttribute("v")?se.getAttribute("v"):Tv(se.innerHTML),Y=se.getAttribute("data-z")||se.getAttribute("z");for(h=0;h<u.length;++h){var M=u[h];M.s.c==C+s&&M.s.r<p+i&&p+i<=M.e.r&&(C=M.e.c+1-s,h=-1)}y=+se.getAttribute("colspan")||1,((F=+se.getAttribute("rowspan")||1)>1||y>1)&&u.push({s:{r:p+i,c:C+s},e:{r:p+i+(F||1)-1,c:C+s+(y||1)-1}});var ee={t:"s",v:D},re=se.getAttribute("data-t")||se.getAttribute("t")||"";D!=null&&(D.length==0?ee.t=re||"z":n.raw||D.trim().length==0||re=="s"||(D==="TRUE"?ee={t:"b",v:!0}:D==="FALSE"?ee={t:"b",v:!1}:isNaN(br(D))?isNaN(Li(D).getDate())||(ee={t:"d",v:kt(D)},n.cellDates||(ee={t:"n",v:Vt(ee.v)}),ee.z=n.dateNF||rt[14]):ee={t:"n",v:br(D)})),ee.z===void 0&&Y!=null&&(ee.z=Y);var ne="",de=se.getElementsByTagName("A");if(de&&de.length)for(var be=0;be<de.length&&!(de[be].hasAttribute("href")&&(ne=de[be].getAttribute("href"),ne.charAt(0)!="#"));++be);ne&&ne.charAt(0)!="#"&&(ee.l={Target:ne}),n.dense?(e[p+i]||(e[p+i]=[]),e[p+i][C+s]=ee):e[$e({c:C+s,r:p+i})]=ee,l.e.c<C+s&&(l.e.c=C+s),C+=y}}++p}return u.length&&(e["!merges"]=(e["!merges"]||[]).concat(u)),l.e.r=Math.max(l.e.r,p-1+i),e["!ref"]=st(l),p>=f&&(e["!fullref"]=st((l.e.r=o.length-x+p-1+i,l))),e}function Fh(e,t){var r=t||{},n=r.dense?[]:{};return Ah(n,e,t)}function GS(e,t){return _n(Fh(e,t),t)}function il(e){var t="",r=KS(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function KS(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}var XS=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Bi({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return ot+t}}(),sl=function(){var e=function(s){return je(s).replace(/  +/g,function(a){return'<text:s text:c="'+a.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,n=function(s,a,o){var f=[];f.push('      <table:table table:name="'+je(a.SheetNames[o])+`" table:style-name="ta1">
`);var l=0,c=0,u=er(s["!ref"]||"A1"),h=s["!merges"]||[],d=0,x=Array.isArray(s);if(s["!cols"])for(c=0;c<=u.e.c;++c)f.push("        <table:table-column"+(s["!cols"][c]?' table:style-name="co'+s["!cols"][c].ods+'"':"")+`></table:table-column>
`);var p="",m=s["!rows"]||[];for(l=0;l<u.s.r;++l)p=m[l]?' table:style-name="ro'+m[l].ods+'"':"",f.push("        <table:table-row"+p+`></table:table-row>
`);for(;l<=u.e.r;++l){for(p=m[l]?' table:style-name="ro'+m[l].ods+'"':"",f.push("        <table:table-row"+p+`>
`),c=0;c<u.s.c;++c)f.push(t);for(;c<=u.e.c;++c){var C=!1,F={},y="";for(d=0;d!=h.length;++d)if(!(h[d].s.c>c)&&!(h[d].s.r>l)&&!(h[d].e.c<c)&&!(h[d].e.r<l)){(h[d].s.c!=c||h[d].s.r!=l)&&(C=!0),F["table:number-columns-spanned"]=h[d].e.c-h[d].s.c+1,F["table:number-rows-spanned"]=h[d].e.r-h[d].s.r+1;break}if(C){f.push(r);continue}var R=$e({r:l,c}),z=x?(s[l]||[])[c]:s[R];if(z&&z.f&&(F["table:formula"]=je(dw(z.f)),z.F&&z.F.slice(0,R.length)==R)){var se=er(z.F);F["table:number-matrix-columns-spanned"]=se.e.c-se.s.c+1,F["table:number-matrix-rows-spanned"]=se.e.r-se.s.r+1}if(!z){f.push(t);continue}switch(z.t){case"b":y=z.v?"TRUE":"FALSE",F["office:value-type"]="boolean",F["office:boolean-value"]=z.v?"true":"false";break;case"n":y=z.w||String(z.v||0),F["office:value-type"]="float",F["office:value"]=z.v||0;break;case"s":case"str":y=z.v==null?"":z.v,F["office:value-type"]="string";break;case"d":y=z.w||kt(z.v).toISOString(),F["office:value-type"]="date",F["office:date-value"]=kt(z.v).toISOString(),F["table:style-name"]="ce1";break;default:f.push(t);continue}var D=e(y);if(z.l&&z.l.Target){var Y=z.l.Target;Y=Y.charAt(0)=="#"?"#"+pw(Y.slice(1)):Y,Y.charAt(0)!="#"&&!Y.match(/^\w+:/)&&(Y="../"+Y),D=ue("text:a",D,{"xlink:href":Y.replace(/&/g,"&amp;")})}f.push("          "+ue("table:table-cell",ue("text:p",D,{}),F)+`
`)}f.push(`        </table:table-row>
`)}return f.push(`      </table:table>
`),f.join("")},i=function(s,a){s.push(` <office:automatic-styles>
`),s.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),s.push(`   <number:month number:style="long"/>
`),s.push(`   <number:text>/</number:text>
`),s.push(`   <number:day number:style="long"/>
`),s.push(`   <number:text>/</number:text>
`),s.push(`   <number:year/>
`),s.push(`  </number:date-style>
`);var o=0;a.SheetNames.map(function(l){return a.Sheets[l]}).forEach(function(l){if(l&&l["!cols"]){for(var c=0;c<l["!cols"].length;++c)if(l["!cols"][c]){var u=l["!cols"][c];if(u.width==null&&u.wpx==null&&u.wch==null)continue;i0(u),u.ods=o;var h=l["!cols"][c].wpx+"px";s.push('  <style:style style:name="co'+o+`" style:family="table-column">
`),s.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+h+`"/>
`),s.push(`  </style:style>
`),++o}}});var f=0;a.SheetNames.map(function(l){return a.Sheets[l]}).forEach(function(l){if(l&&l["!rows"]){for(var c=0;c<l["!rows"].length;++c)if(l["!rows"][c]){l["!rows"][c].ods=f;var u=l["!rows"][c].hpx+"px";s.push('  <style:style style:name="ro'+f+`" style:family="table-row">
`),s.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+u+`"/>
`),s.push(`  </style:style>
`),++f}}}),s.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),s.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),s.push(`  </style:style>
`),s.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),s.push(` </office:automatic-styles>
`)};return function(a,o){var f=[ot],l=Bi({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),c=Bi({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});o.bookType=="fods"?(f.push("<office:document"+l+c+`>
`),f.push(Wu().replace(/office:document-meta/g,"office:meta"))):f.push("<office:document-content"+l+`>
`),i(f,a),f.push(`  <office:body>
`),f.push(`    <office:spreadsheet>
`);for(var u=0;u!=a.SheetNames.length;++u)f.push(n(a.Sheets[a.SheetNames[u]],a,u));return f.push(`    </office:spreadsheet>
`),f.push(`  </office:body>
`),o.bookType=="fods"?f.push("</office:document>"):f.push("</office:document-content>"),f.join("")}}();function Ch(e,t){if(t.bookType=="fods")return sl(e,t);var r=Xo(),n="",i=[],s=[];return n="mimetype",Pe(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",Pe(r,n,sl(e,t)),i.push([n,"text/xml"]),s.push([n,"ContentFile"]),n="styles.xml",Pe(r,n,XS(e,t)),i.push([n,"text/xml"]),s.push([n,"StylesFile"]),n="meta.xml",Pe(r,n,ot+Wu()),i.push([n,"text/xml"]),s.push([n,"MetadataFile"]),n="manifest.rdf",Pe(r,n,s2(s)),i.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",Pe(r,n,n2(i)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function js(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function zS(e){return typeof TextEncoder<"u"?new TextEncoder().encode(e):cr(Mi(e))}function YS(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function tn(e){var t=e.reduce(function(i,s){return i+s.length},0),r=new Uint8Array(t),n=0;return e.forEach(function(i){r.set(i,n),n+=i.length}),r}function qS(e,t,r){var n=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,i=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(n&127)<<1;for(var s=0;i>=1;++s,i/=256)e[t+s]=i&255;e[t+15]|=r>=0?0:128}function Ui(e,t){var r=t?t[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),n}function We(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Vn(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function ft(e){for(var t=[],r=[0];r[0]<e.length;){var n=r[0],i=Ui(e,r),s=i&7;i=Math.floor(i/8);var a=0,o;if(i==0)break;switch(s){case 0:{for(var f=r[0];e[r[0]++]>=128;);o=e.slice(f,r[0])}break;case 5:a=4,o=e.slice(r[0],r[0]+a),r[0]+=a;break;case 1:a=8,o=e.slice(r[0],r[0]+a),r[0]+=a;break;case 2:a=Ui(e,r),o=e.slice(r[0],r[0]+a),r[0]+=a;break;case 3:case 4:default:throw new Error("PB Type ".concat(s," for Field ").concat(i," at offset ").concat(n))}var l={data:o,type:s};t[i]==null?t[i]=[l]:t[i].push(l)}return t}function dt(e){var t=[];return e.forEach(function(r,n){r.forEach(function(i){i.data&&(t.push(We(n*8+i.type)),i.type==2&&t.push(We(i.data.length)),t.push(i.data))})}),tn(t)}function or(e){for(var t,r=[],n=[0];n[0]<e.length;){var i=Ui(e,n),s=ft(e.slice(n[0],n[0]+i));n[0]+=i;var a={id:Vn(s[1][0].data),messages:[]};s[2].forEach(function(o){var f=ft(o.data),l=Vn(f[3][0].data);a.messages.push({meta:f,data:e.slice(n[0],n[0]+l)}),n[0]+=l}),(t=s[3])!=null&&t[0]&&(a.merge=Vn(s[3][0].data)>>>0>0),r.push(a)}return r}function Fn(e){var t=[];return e.forEach(function(r){var n=[];n[1]=[{data:We(r.id),type:0}],n[2]=[],r.merge!=null&&(n[3]=[{data:We(+!!r.merge),type:0}]);var i=[];r.messages.forEach(function(a){i.push(a.data),a.meta[3]=[{type:0,data:We(a.data.length)}],n[2].push({data:dt(a.meta),type:2})});var s=dt(n);t.push(We(s.length)),t.push(s),i.forEach(function(a){return t.push(a)})}),tn(t)}function JS(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=Ui(t,r),i=[];r[0]<t.length;){var s=t[r[0]]&3;if(s==0){var a=t[r[0]++]>>2;if(a<60)++a;else{var o=a-59;a=t[r[0]],o>1&&(a|=t[r[0]+1]<<8),o>2&&(a|=t[r[0]+2]<<16),o>3&&(a|=t[r[0]+3]<<24),a>>>=0,a++,r[0]+=o}i.push(t.slice(r[0],r[0]+a)),r[0]+=a;continue}else{var f=0,l=0;if(s==1?(l=(t[r[0]]>>2&7)+4,f=(t[r[0]++]&224)<<3,f|=t[r[0]++]):(l=(t[r[0]++]>>2)+1,s==2?(f=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(f=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),i=[tn(i)],f==0)throw new Error("Invalid offset 0");if(f>i[0].length)throw new Error("Invalid offset beyond length");if(l>=f)for(i.push(i[0].slice(-f)),l-=f;l>=i[i.length-1].length;)i.push(i[i.length-1]),l-=i[i.length-1].length;i.push(i[0].slice(-f,-f+l))}}var c=tn(i);if(c.length!=n)throw new Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}function fr(e){for(var t=[],r=0;r<e.length;){var n=e[r++],i=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(JS(n,e.slice(r,r+i))),r+=i}if(r!==e.length)throw new Error("data is not a valid framed stream!");return tn(t)}function Cn(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),i=new Uint8Array(4);t.push(i);var s=We(n),a=s.length;t.push(s),n<=60?(a++,t.push(new Uint8Array([n-1<<2]))):n<=256?(a+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(a+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(a+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(a+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),a+=n,i[0]=0,i[1]=a&255,i[2]=a>>8&255,i[3]=a>>16&255,r+=n}return tn(t)}function $a(e,t){var r=new Uint8Array(32),n=js(r),i=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,qS(r,i,e.v),s|=1,i+=16;break;case"b":r[1]=6,n.setFloat64(i,e.v?1:0,!0),s|=2,i+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(i,t.indexOf(e.v),!0),s|=8,i+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,s,!0),r.slice(0,i)}function Ga(e,t){var r=new Uint8Array(32),n=js(r),i=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(i,e.v,!0),s|=32,i+=8;break;case"b":r[2]=6,n.setFloat64(i,e.v?1:0,!0),s|=32,i+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(i,t.indexOf(e.v),!0),s|=16,i+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,s,!0),r.slice(0,i)}function $r(e){var t=ft(e);return Ui(t[1][0].data)}function ZS(e,t,r){var n,i,s,a;if(!((n=e[6])!=null&&n[0])||!((i=e[7])!=null&&i[0]))throw"Mutation only works on post-BNC storages!";var o=((a=(s=e[8])==null?void 0:s[0])==null?void 0:a.data)&&Vn(e[8][0].data)>0||!1;if(o)throw"Math only works with normal offsets";for(var f=0,l=js(e[7][0].data),c=0,u=[],h=js(e[4][0].data),d=0,x=[],p=0;p<t.length;++p){if(t[p]==null){l.setUint16(p*2,65535,!0),h.setUint16(p*2,65535);continue}l.setUint16(p*2,c,!0),h.setUint16(p*2,d,!0);var m,C;switch(typeof t[p]){case"string":m=$a({t:"s",v:t[p]},r),C=Ga({t:"s",v:t[p]},r);break;case"number":m=$a({t:"n",v:t[p]},r),C=Ga({t:"n",v:t[p]},r);break;case"boolean":m=$a({t:"b",v:t[p]},r),C=Ga({t:"b",v:t[p]},r);break;default:throw new Error("Unsupported value "+t[p])}u.push(m),c+=m.length,x.push(C),d+=C.length,++f}for(e[2][0].data=We(f);p<e[7][0].data.length/2;++p)l.setUint16(p*2,65535,!0),h.setUint16(p*2,65535,!0);return e[6][0].data=tn(u),e[3][0].data=tn(x),f}function QS(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=er(r["!ref"]);n.s.r=n.s.c=0;var i=!1;n.e.c>9&&(i=!0,n.e.c=9),n.e.r>49&&(i=!0,n.e.r=49),i&&console.error("The Numbers writer is currently limited to ".concat(st(n)));var s=$s(r,{range:n,header:1}),a=["~Sh33tJ5~"];s.forEach(function(H){return H.forEach(function(N){typeof N=="string"&&a.push(N)})});var o={},f=[],l=Ke.read(t.numbers,{type:"base64"});l.FileIndex.map(function(H,N){return[H,l.FullPaths[N]]}).forEach(function(H){var N=H[0],I=H[1];if(N.type==2&&N.name.match(/\.iwa/)){var Q=N.content,k=fr(Q),q=or(k);q.forEach(function(j){f.push(j.id),o[j.id]={deps:[],location:I,type:Vn(j.messages[0].meta[1][0].data)}})}}),f.sort(function(H,N){return H-N});var c=f.filter(function(H){return H>1}).map(function(H){return[H,We(H)]});l.FileIndex.map(function(H,N){return[H,l.FullPaths[N]]}).forEach(function(H){var N=H[0];if(H[1],!!N.name.match(/\.iwa/)){var I=or(fr(N.content));I.forEach(function(Q){Q.messages.forEach(function(k){c.forEach(function(q){Q.messages.some(function(j){return Vn(j.meta[1][0].data)!=11006&&YS(j.data,q[1])})&&o[q[0]].deps.push(Q.id)})})})}});for(var u=Ke.find(l,o[1].location),h=or(fr(u.content)),d,x=0;x<h.length;++x){var p=h[x];p.id==1&&(d=p)}var m=$r(ft(d.messages[0].data)[1][0].data);for(u=Ke.find(l,o[m].location),h=or(fr(u.content)),x=0;x<h.length;++x)p=h[x],p.id==m&&(d=p);for(m=$r(ft(d.messages[0].data)[2][0].data),u=Ke.find(l,o[m].location),h=or(fr(u.content)),x=0;x<h.length;++x)p=h[x],p.id==m&&(d=p);for(m=$r(ft(d.messages[0].data)[2][0].data),u=Ke.find(l,o[m].location),h=or(fr(u.content)),x=0;x<h.length;++x)p=h[x],p.id==m&&(d=p);var C=ft(d.messages[0].data);{C[6][0].data=We(n.e.r+1),C[7][0].data=We(n.e.c+1);var F=$r(C[46][0].data),y=Ke.find(l,o[F].location),R=or(fr(y.content));{for(var z=0;z<R.length&&R[z].id!=F;++z);if(R[z].id!=F)throw"Bad ColumnRowUIDMapArchive";var se=ft(R[z].messages[0].data);se[1]=[],se[2]=[],se[3]=[];for(var D=0;D<=n.e.c;++D){var Y=[];Y[1]=Y[2]=[{type:0,data:We(D+420690)}],se[1].push({type:2,data:dt(Y)}),se[2].push({type:0,data:We(D)}),se[3].push({type:0,data:We(D)})}se[4]=[],se[5]=[],se[6]=[];for(var M=0;M<=n.e.r;++M)Y=[],Y[1]=Y[2]=[{type:0,data:We(M+726270)}],se[4].push({type:2,data:dt(Y)}),se[5].push({type:0,data:We(M)}),se[6].push({type:0,data:We(M)});R[z].messages[0].data=dt(se)}y.content=Cn(Fn(R)),y.size=y.content.length,delete C[46];var ee=ft(C[4][0].data);{ee[7][0].data=We(n.e.r+1);var re=ft(ee[1][0].data),ne=$r(re[2][0].data);y=Ke.find(l,o[ne].location),R=or(fr(y.content));{if(R[0].id!=ne)throw"Bad HeaderStorageBucket";var de=ft(R[0].messages[0].data);for(M=0;M<s.length;++M){var be=ft(de[2][0].data);be[1][0].data=We(M),be[4][0].data=We(s[M].length),de[2][M]={type:de[2][0].type,data:dt(be)}}R[0].messages[0].data=dt(de)}y.content=Cn(Fn(R)),y.size=y.content.length;var ye=$r(ee[2][0].data);y=Ke.find(l,o[ye].location),R=or(fr(y.content));{if(R[0].id!=ye)throw"Bad HeaderStorageBucket";for(de=ft(R[0].messages[0].data),D=0;D<=n.e.c;++D)be=ft(de[2][0].data),be[1][0].data=We(D),be[4][0].data=We(n.e.r+1),de[2][D]={type:de[2][0].type,data:dt(be)};R[0].messages[0].data=dt(de)}y.content=Cn(Fn(R)),y.size=y.content.length;var Ne=$r(ee[4][0].data);(function(){for(var H=Ke.find(l,o[Ne].location),N=or(fr(H.content)),I,Q=0;Q<N.length;++Q){var k=N[Q];k.id==Ne&&(I=k)}var q=ft(I.messages[0].data);{q[3]=[];var j=[];a.forEach(function(E,A){j[1]=[{type:0,data:We(A)}],j[2]=[{type:0,data:We(1)}],j[3]=[{type:2,data:zS(E)}],q[3].push({type:2,data:dt(j)})})}I.messages[0].data=dt(q);var G=Fn(N),_e=Cn(G);H.content=_e,H.size=H.content.length})();var Te=ft(ee[3][0].data);{var Oe=Te[1][0];delete Te[2];var Ge=ft(Oe.data);{var nt=$r(Ge[2][0].data);(function(){for(var H=Ke.find(l,o[nt].location),N=or(fr(H.content)),I,Q=0;Q<N.length;++Q){var k=N[Q];k.id==nt&&(I=k)}var q=ft(I.messages[0].data);{delete q[6],delete Te[7];var j=new Uint8Array(q[5][0].data);q[5]=[];for(var G=0,_e=0;_e<=n.e.r;++_e){var E=ft(j);G+=ZS(E,s[_e],a),E[1][0].data=We(_e),q[5].push({data:dt(E),type:2})}q[1]=[{type:0,data:We(n.e.c+1)}],q[2]=[{type:0,data:We(n.e.r+1)}],q[3]=[{type:0,data:We(G)}],q[4]=[{type:0,data:We(n.e.r+1)}]}I.messages[0].data=dt(q);var A=Fn(N),P=Cn(A);H.content=P,H.size=H.content.length})()}Oe.data=dt(Ge)}ee[3][0].data=dt(Te)}C[4][0].data=dt(ee)}d.messages[0].data=dt(C);var qe=Fn(h),O=Cn(qe);return u.content=O,u.size=u.content.length,l}function e4(e){return function(r){for(var n=0;n!=e.length;++n){var i=e[n];r[i[0]]===void 0&&(r[i[0]]=i[1]),i[2]==="n"&&(r[i[0]]=Number(r[i[0]]))}}}function l0(e){e4([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function t4(e,t){return t.bookType=="ods"?Ch(e,t):t.bookType=="numbers"?QS(e,t):t.bookType=="xlsb"?r4(e,t):n4(e,t)}function r4(e,t){In=1024,e&&!e.SSF&&(e.SSF=jt(rt)),e&&e.SSF&&(pa(),da(e.SSF),t.revssf=xa(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ai?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",n=lh.indexOf(t.bookType)>-1,i=Bu();l0(t=t||{});var s=Xo(),a="",o=0;if(t.cellXfs=[],nn(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),a="docProps/core.xml",Pe(s,a,Vu(e.Props,t)),i.coreprops.push(a),Ve(t.rels,2,a,ke.CORE_PROPS),a="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var f=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&f.push(e.SheetNames[l]);e.Props.SheetNames=f}for(e.Props.Worksheets=e.Props.SheetNames.length,Pe(s,a,$u(e.Props)),i.extprops.push(a),Ve(t.rels,3,a,ke.EXT_PROPS),e.Custprops!==e.Props&&Et(e.Custprops||{}).length>0&&(a="docProps/custom.xml",Pe(s,a,Gu(e.Custprops)),i.custprops.push(a),Ve(t.rels,4,a,ke.CUST_PROPS)),o=1;o<=e.SheetNames.length;++o){var c={"!id":{}},u=e.Sheets[e.SheetNames[o-1]],h=(u||{})["!type"]||"sheet";switch(h){case"chart":default:a="xl/worksheets/sheet"+o+"."+r,Pe(s,a,sS(o-1,a,t,e,c)),i.sheets.push(a),Ve(t.wbrels,-1,"worksheets/sheet"+o+"."+r,ke.WS[0])}if(u){var d=u["!comments"],x=!1,p="";d&&d.length>0&&(p="xl/comments"+o+"."+r,Pe(s,p,fS(d,p)),i.comments.push(p),Ve(c,-1,"../comments"+o+"."+r,ke.CMNT),x=!0),u["!legacy"]&&x&&Pe(s,"xl/drawings/vmlDrawing"+o+".vml",oh(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}c["!id"].rId1&&Pe(s,Hu(a),Hn(c))}return t.Strings!=null&&t.Strings.length>0&&(a="xl/sharedStrings."+r,Pe(s,a,oS(t.Strings,a,t)),i.strs.push(a),Ve(t.wbrels,-1,"sharedStrings."+r,ke.SST)),a="xl/workbook."+r,Pe(s,a,iS(e,a)),i.workbooks.push(a),Ve(t.rels,1,a,ke.WB),a="xl/theme/theme1.xml",Pe(s,a,sh(e.Themes,t)),i.themes.push(a),Ve(t.wbrels,-1,"theme/theme1.xml",ke.THEME),a="xl/styles."+r,Pe(s,a,aS(e,a,t)),i.styles.push(a),Ve(t.wbrels,-1,"styles."+r,ke.STY),e.vbaraw&&n&&(a="xl/vbaProject.bin",Pe(s,a,e.vbaraw),i.vba.push(a),Ve(t.wbrels,-1,"vbaProject.bin",ke.VBA)),a="xl/metadata."+r,Pe(s,a,lS(a)),i.metadata.push(a),Ve(t.wbrels,-1,"metadata."+r,ke.XLMETA),Pe(s,"[Content_Types].xml",Uu(i,t)),Pe(s,"_rels/.rels",Hn(t.rels)),Pe(s,"xl/_rels/workbook."+r+".rels",Hn(t.wbrels)),delete t.revssf,delete t.ssf,s}function n4(e,t){In=1024,e&&!e.SSF&&(e.SSF=jt(rt)),e&&e.SSF&&(pa(),da(e.SSF),t.revssf=xa(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ai?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=lh.indexOf(t.bookType)>-1,i=Bu();l0(t=t||{});var s=Xo(),a="",o=0;if(t.cellXfs=[],nn(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),a="docProps/core.xml",Pe(s,a,Vu(e.Props,t)),i.coreprops.push(a),Ve(t.rels,2,a,ke.CORE_PROPS),a="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var f=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&f.push(e.SheetNames[l]);e.Props.SheetNames=f}e.Props.Worksheets=e.Props.SheetNames.length,Pe(s,a,$u(e.Props)),i.extprops.push(a),Ve(t.rels,3,a,ke.EXT_PROPS),e.Custprops!==e.Props&&Et(e.Custprops||{}).length>0&&(a="docProps/custom.xml",Pe(s,a,Gu(e.Custprops)),i.custprops.push(a),Ve(t.rels,4,a,ke.CUST_PROPS));var c=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var u={"!id":{}},h=e.Sheets[e.SheetNames[o-1]],d=(h||{})["!type"]||"sheet";switch(d){case"chart":default:a="xl/worksheets/sheet"+o+"."+r,Pe(s,a,vh(o-1,t,e,u)),i.sheets.push(a),Ve(t.wbrels,-1,"worksheets/sheet"+o+"."+r,ke.WS[0])}if(h){var x=h["!comments"],p=!1,m="";if(x&&x.length>0){var C=!1;x.forEach(function(F){F[1].forEach(function(y){y.T==!0&&(C=!0)})}),C&&(m="xl/threadedComments/threadedComment"+o+"."+r,Pe(s,m,b_(x,c,t)),i.threadedcomments.push(m),Ve(u,-1,"../threadedComments/threadedComment"+o+"."+r,ke.TCMNT)),m="xl/comments"+o+"."+r,Pe(s,m,fh(x)),i.comments.push(m),Ve(u,-1,"../comments"+o+"."+r,ke.CMNT),p=!0}h["!legacy"]&&p&&Pe(s,"xl/drawings/vmlDrawing"+o+".vml",oh(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}u["!id"].rId1&&Pe(s,Hu(a),Hn(u))}return t.Strings!=null&&t.Strings.length>0&&(a="xl/sharedStrings."+r,Pe(s,a,Qu(t.Strings,t)),i.strs.push(a),Ve(t.wbrels,-1,"sharedStrings."+r,ke.SST)),a="xl/workbook."+r,Pe(s,a,wh(e)),i.workbooks.push(a),Ve(t.rels,1,a,ke.WB),a="xl/theme/theme1.xml",Pe(s,a,sh(e.Themes,t)),i.themes.push(a),Ve(t.wbrels,-1,"theme/theme1.xml",ke.THEME),a="xl/styles."+r,Pe(s,a,nh(e,t)),i.styles.push(a),Ve(t.wbrels,-1,"styles."+r,ke.STY),e.vbaraw&&n&&(a="xl/vbaProject.bin",Pe(s,a,e.vbaraw),i.vba.push(a),Ve(t.wbrels,-1,"vbaProject.bin",ke.VBA)),a="xl/metadata."+r,Pe(s,a,ah()),i.metadata.push(a),Ve(t.wbrels,-1,"metadata."+r,ke.XLMETA),c.length>1&&(a="xl/persons/person.xml",Pe(s,a,k_(c)),i.people.push(a),Ve(t.wbrels,-1,"persons/person.xml",ke.PEOPLE)),Pe(s,"[Content_Types].xml",Uu(i,t)),Pe(s,"_rels/.rels",Hn(t.rels)),Pe(s,"xl/_rels/workbook."+r+".rels",Hn(t.wbrels)),delete t.revssf,delete t.ssf,s}function i4(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Br(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Oh(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Xi(t.file,Ke.write(e,{type:Le?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Ke.write(e,t)}function s4(e,t){var r=jt(t||{}),n=t4(e,r);return a4(n,r)}function a4(e,t){var r={},n=Le?"nodebuffer":typeof Uint8Array<"u"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var i=e.FullPaths?Ke.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno<"u"&&typeof i=="string"){if(t.type=="binary"||t.type=="base64")return i;i=new Uint8Array(ha(i))}return t.password&&typeof encrypt_agile<"u"?Oh(encrypt_agile(i,t.password),t):t.type==="file"?Xi(t.file,i):t.type=="string"?wi(i):i}function o4(e,t){var r=t||{},n=SS(e,r);return Oh(n,r)}function _r(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return ki(Mi(n));case"binary":return Mi(n);case"string":return e;case"file":return Xi(t.file,n,"utf8");case"buffer":return Le?Wr(n,"utf8"):typeof TextEncoder<"u"?new TextEncoder().encode(n):_r(n,{type:"binary"}).split("").map(function(i){return i.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function f4(e,t){switch(t.type){case"base64":return ki(e);case"binary":return e;case"string":return e;case"file":return Xi(t.file,e,"binary");case"buffer":return Le?Wr(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function cs(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return t.type=="base64"?ki(r):t.type=="string"?wi(r):r;case"file":return Xi(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function Rh(e,t){Lg(),XT(e);var r=jt(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var n=Rh(e,r);return r.type="array",ha(n)}var i=0;if(r.sheet&&(typeof r.sheet=="number"?i=r.sheet:i=e.SheetNames.indexOf(r.sheet),!e.SheetNames[i]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return _r(wS(e,r),r);case"slk":case"sylk":return _r($2.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"htm":case"html":return _r(yh(e.Sheets[e.SheetNames[i]],r),r);case"txt":return f4(Ph(e.Sheets[e.SheetNames[i]],r),r);case"csv":return _r(c0(e.Sheets[e.SheetNames[i]],r),r,"\uFEFF");case"dif":return _r(G2.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"dbf":return cs(j2.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"prn":return _r(K2.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"rtf":return _r(Q2.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"eth":return _r(Zu.from_sheet(e.Sheets[e.SheetNames[i]],r),r);case"fods":return _r(Ch(e,r),r);case"wk1":return cs(Zf.sheet_to_wk1(e.Sheets[e.SheetNames[i]],r),r);case"wk3":return cs(Zf.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),cs(Sh(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),o4(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return s4(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function l4(e,t,r,n,i,s,a,o){var f=_t(r),l=o.defval,c=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),u=!0,h=i===1?[]:{};if(i!==1)if(Object.defineProperty)try{Object.defineProperty(h,"__rowNum__",{value:r,enumerable:!1})}catch{h.__rowNum__=r}else h.__rowNum__=r;if(!a||e[r])for(var d=t.s.c;d<=t.e.c;++d){var x=a?e[r][d]:e[n[d]+f];if(x===void 0||x.t===void 0){if(l===void 0)continue;s[d]!=null&&(h[s[d]]=l);continue}var p=x.v;switch(x.t){case"z":if(p==null)break;continue;case"e":p=p==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+x.t)}if(s[d]!=null){if(p==null)if(x.t=="e"&&p===null)h[s[d]]=null;else if(l!==void 0)h[s[d]]=l;else if(c&&p===null)h[s[d]]=null;else continue;else h[s[d]]=c&&(x.t!=="n"||x.t==="n"&&o.rawNumbers!==!1)?p:Ur(x,p,o);p!=null&&(u=!1)}}return{row:h,isempty:u}}function $s(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,i=1,s=[],a=0,o="",f={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},c=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)?n=3:l.header==null&&(n=0),typeof c){case"string":f=Ye(c);break;case"number":f=Ye(e["!ref"]),f.s.r=c;break;default:f=c}n>0&&(i=0);var u=_t(f.s.r),h=[],d=[],x=0,p=0,m=Array.isArray(e),C=f.s.r,F=0,y={};m&&!e[C]&&(e[C]=[]);var R=l.skipHidden&&e["!cols"]||[],z=l.skipHidden&&e["!rows"]||[];for(F=f.s.c;F<=f.e.c;++F)if(!(R[F]||{}).hidden)switch(h[F]=Ct(F),r=m?e[C][F]:e[h[F]+u],n){case 1:s[F]=F-f.s.c;break;case 2:s[F]=h[F];break;case 3:s[F]=l.header[F-f.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),o=a=Ur(r,null,l),p=y[a]||0,!p)y[a]=1;else{do o=a+"_"+p++;while(y[o]);y[a]=p,y[o]=1}s[F]=o}for(C=f.s.r+i;C<=f.e.r;++C)if(!(z[C]||{}).hidden){var se=l4(e,f,C,h,n,s,m,l);(se.isempty===!1||(n===1?l.blankrows!==!1:l.blankrows))&&(d[x++]=se.row)}return d.length=x,d}var al=/"/g;function c4(e,t,r,n,i,s,a,o){for(var f=!0,l=[],c="",u=_t(r),h=t.s.c;h<=t.e.c;++h)if(n[h]){var d=o.dense?(e[r]||[])[h]:e[n[h]+u];if(d==null)c="";else if(d.v!=null){f=!1,c=""+(o.rawNumbers&&d.t=="n"?d.v:Ur(d,null,o));for(var x=0,p=0;x!==c.length;++x)if((p=c.charCodeAt(x))===i||p===s||p===34||o.forceQuotes){c='"'+c.replace(al,'""')+'"';break}c=="ID"&&(c='"ID"')}else d.f!=null&&!d.F?(f=!1,c="="+d.f,c.indexOf(",")>=0&&(c='"'+c.replace(al,'""')+'"')):c="";l.push(c)}return o.blankrows===!1&&f?null:l.join(a)}function c0(e,t){var r=[],n=t??{};if(e==null||e["!ref"]==null)return"";var i=Ye(e["!ref"]),s=n.FS!==void 0?n.FS:",",a=s.charCodeAt(0),o=n.RS!==void 0?n.RS:`
`,f=o.charCodeAt(0),l=new RegExp((s=="|"?"\\|":s)+"+$"),c="",u=[];n.dense=Array.isArray(e);for(var h=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],x=i.s.c;x<=i.e.c;++x)(h[x]||{}).hidden||(u[x]=Ct(x));for(var p=0,m=i.s.r;m<=i.e.r;++m)(d[m]||{}).hidden||(c=c4(e,i,m,u,a,f,s,n),c!=null&&(n.strip&&(c=c.replace(l,"")),(c||n.blankrows!==!1)&&r.push((p++?o:"")+c)));return delete n.dense,r.join("")}function Ph(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=c0(e,t);return r}function u4(e){var t="",r,n="";if(e==null||e["!ref"]==null)return[];var i=Ye(e["!ref"]),s="",a=[],o,f=[],l=Array.isArray(e);for(o=i.s.c;o<=i.e.c;++o)a[o]=Ct(o);for(var c=i.s.r;c<=i.e.r;++c)for(s=_t(c),o=i.s.c;o<=i.e.c;++o)if(t=a[o]+s,r=l?(e[c]||[])[o]:e[t],n="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;n=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}f[f.length]=t+"="+n}return f}function Dh(e,t,r){var n=r||{},i=+!n.skipHeader,s=e||{},a=0,o=0;if(s&&n.origin!=null)if(typeof n.origin=="number")a=n.origin;else{var f=typeof n.origin=="string"?ct(n.origin):n.origin;a=f.r,o=f.c}var l,c={s:{c:0,r:0},e:{c:o,r:a+t.length-1+i}};if(s["!ref"]){var u=Ye(s["!ref"]);c.e.c=Math.max(c.e.c,u.e.c),c.e.r=Math.max(c.e.r,u.e.r),a==-1&&(a=u.e.r+1,c.e.r=a+t.length-1+i)}else a==-1&&(a=0,c.e.r=t.length-1+i);var h=n.header||[],d=0;t.forEach(function(p,m){Et(p).forEach(function(C){(d=h.indexOf(C))==-1&&(h[d=h.length]=C);var F=p[C],y="z",R="",z=$e({c:o+d,r:a+m+i});l=Hi(s,z),F&&typeof F=="object"&&!(F instanceof Date)?s[z]=F:(typeof F=="number"?y="n":typeof F=="boolean"?y="b":typeof F=="string"?y="s":F instanceof Date?(y="d",n.cellDates||(y="n",F=Vt(F)),R=n.dateNF||rt[14]):F===null&&n.nullError&&(y="e",F=0),l?(l.t=y,l.v=F,delete l.w,delete l.R,R&&(l.z=R)):s[z]=l={t:y,v:F},R&&(l.z=R))})}),c.e.c=Math.max(c.e.c,o+h.length-1);var x=_t(a);if(i)for(d=0;d<h.length;++d)s[Ct(d+o)+x]={t:"s",v:h[d]};return s["!ref"]=st(c),s}function h4(e,t){return Dh(null,e,t)}function Hi(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var n=ct(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Hi(e,$e(t)):Hi(e,$e({r:t,c:r||0}))}function d4(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function p4(){return{SheetNames:[],Sheets:{}}}function x4(e,t,r,n){var i=1;if(!r)for(;i<=65535&&e.SheetNames.indexOf(r="Sheet"+i)!=-1;++i,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);i=s&&+s[2]||0;var a=s&&s[1]||r;for(++i;i<=65535&&e.SheetNames.indexOf(r=a+i)!=-1;++i);}if(Eh(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function m4(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=d4(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function g4(e,t){return e.z=t,e}function Ih(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function v4(e,t,r){return Ih(e,"#"+t,r)}function _4(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function E4(e,t,r,n){for(var i=typeof t!="string"?t:Ye(t),s=typeof t=="string"?t:st(t),a=i.s.r;a<=i.e.r;++a)for(var o=i.s.c;o<=i.e.c;++o){var f=Hi(e,a,o);f.t="n",f.F=s,delete f.v,a==i.s.r&&o==i.s.c&&(f.f=r,n&&(f.D=!0))}return e}var Ka={encode_col:Ct,encode_row:_t,encode_cell:$e,encode_range:st,decode_col:Qo,decode_row:Zo,split_cell:Lv,decode_cell:ct,decode_range:er,format_cell:Ur,sheet_add_aoa:Iu,sheet_add_json:Dh,sheet_add_dom:Ah,aoa_to_sheet:Jn,json_to_sheet:h4,table_to_sheet:Fh,table_to_book:GS,sheet_to_csv:c0,sheet_to_txt:Ph,sheet_to_json:$s,sheet_to_html:yh,sheet_to_formulae:u4,sheet_to_row_object_array:$s,sheet_get_cell:Hi,book_new:p4,book_append_sheet:x4,book_set_sheet_visibility:m4,cell_set_number_format:g4,cell_set_hyperlink:Ih,cell_set_internal_link:v4,cell_add_comment:_4,sheet_set_array_formula:E4,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}},li=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Nh={exports:{}};(function(e,t){(function(r,n){n()})(li,function(){function r(l,c){return typeof c>"u"?c={autoBom:!1}:typeof c!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),c={autoBom:!c}),c.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(l.type)?new Blob(["\uFEFF",l],{type:l.type}):l}function n(l,c,u){var h=new XMLHttpRequest;h.open("GET",l),h.responseType="blob",h.onload=function(){f(h.response,c,u)},h.onerror=function(){console.error("could not download file")},h.send()}function i(l){var c=new XMLHttpRequest;c.open("HEAD",l,!1);try{c.send()}catch{}return 200<=c.status&&299>=c.status}function s(l){try{l.dispatchEvent(new MouseEvent("click"))}catch{var c=document.createEvent("MouseEvents");c.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),l.dispatchEvent(c)}}var a=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof li=="object"&&li.global===li?li:void 0,o=a.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),f=a.saveAs||(typeof window!="object"||window!==a?function(){}:"download"in HTMLAnchorElement.prototype&&!o?function(l,c,u){var h=a.URL||a.webkitURL,d=document.createElement("a");c=c||l.name||"download",d.download=c,d.rel="noopener",typeof l=="string"?(d.href=l,d.origin===location.origin?s(d):i(d.href)?n(l,c,u):s(d,d.target="_blank")):(d.href=h.createObjectURL(l),setTimeout(function(){h.revokeObjectURL(d.href)},4e4),setTimeout(function(){s(d)},0))}:"msSaveOrOpenBlob"in navigator?function(l,c,u){if(c=c||l.name||"download",typeof l!="string")navigator.msSaveOrOpenBlob(r(l,u),c);else if(i(l))n(l,c,u);else{var h=document.createElement("a");h.href=l,h.target="_blank",setTimeout(function(){s(h)})}}:function(l,c,u,h){if(h=h||open("","_blank"),h&&(h.document.title=h.document.body.innerText="downloading..."),typeof l=="string")return n(l,c,u);var d=l.type==="application/octet-stream",x=/constructor/i.test(a.HTMLElement)||a.safari,p=/CriOS\/[\d]+/.test(navigator.userAgent);if((p||d&&x||o)&&typeof FileReader<"u"){var m=new FileReader;m.onloadend=function(){var y=m.result;y=p?y:y.replace(/^data:[^;]*;/,"data:attachment/file;"),h?h.location.href=y:location=y,h=null},m.readAsDataURL(l)}else{var C=a.URL||a.webkitURL,F=C.createObjectURL(l);h?h.location=F:location.href=F,h=null,setTimeout(function(){C.revokeObjectURL(F)},4e4)}});a.saveAs=f.saveAs=f,e.exports=f})})(Nh);var w4=Nh.exports;const T4={name:"AdminDashboard",data(){return{wechatAccounts:[],isLoading:!1,loadError:!1,errorMessage:"",deletingIds:new Set}},async mounted(){await this.loadData()},methods:{async loadData(){this.isLoading=!0,this.loadError=!1,this.errorMessage="";try{const e=await Ze.get("/api/wechat");this.wechatAccounts=e.data.wechatAccounts||[]}catch(e){console.error("加载数据失败:",e),this.loadError=!0,this.errorMessage="加载数据失败，请重试"}finally{this.isLoading=!1}},async refreshData(){await this.loadData()},formatTime(e){return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},async copyWechatId(e,t){let r=null;if(t&&t.currentTarget)r=t.currentTarget;else if(t&&t.target)r=t.target;else{console.warn("复制操作缺少事件对象，尝试查找按钮");const i=document.querySelectorAll(".copy-btn");r=Array.from(i).find(s=>{var a,o;return((o=(a=s.closest(".account-card"))==null?void 0:a.querySelector(".wechat-id"))==null?void 0:o.textContent)===e})}if(!r){console.error("无法找到复制按钮");return}const n=r.textContent;try{await navigator.clipboard.writeText(e),this.showCopySuccess(r,n)}catch(i){console.error("复制失败，使用降级方案:",i);try{const s=document.createElement("textarea");s.value=e,s.style.position="fixed",s.style.opacity="0",s.style.left="-9999px",document.body.appendChild(s),s.select(),s.setSelectionRange(0,99999),document.execCommand("copy"),document.body.removeChild(s),this.showCopySuccess(r,n)}catch(s){console.error("降级复制方案也失败:",s),this.showCopyError(r,n)}}},showCopySuccess(e,t){e.textContent="已复制",e.style.background="#4CAF50",e.style.transform="scale(0.95)",setTimeout(()=>{e.textContent=t,e.style.background="",e.style.transform=""},1500)},showCopyError(e,t){e.textContent="复制失败",e.style.background="#ff4757",setTimeout(()=>{e.textContent=t,e.style.background=""},2e3)},async deleteWechatId(e){if(confirm(`确定要删除微信号 "${e.wechatId}" 吗？`)){this.deletingIds.add(e.id);try{const t=await Ze.delete(`/api/wechat/${e.id}`);t.data.success?(this.wechatAccounts=this.wechatAccounts.filter(r=>r.id!==e.id),console.log(`删除成功: ${e.wechatId}`)):(console.error("删除失败:",t.data.message),alert(t.data.message||"删除失败，请重试"))}catch(t){console.error("删除微信号失败:",t),alert("删除失败，请重试")}finally{this.deletingIds.delete(e.id)}}},exportToExcel(){if(this.wechatAccounts.length===0){alert("暂无数据可导出");return}try{const e=this.wechatAccounts.map((l,c)=>({序号:c+1,微信号:l.wechatId,提交时间:this.formatTime(l.submitTime),IP地址:l.ipAddress||"未知",记录ID:l.id})),t=Ka.book_new(),r=Ka.json_to_sheet(e),n=[{wch:8},{wch:20},{wch:20},{wch:18},{wch:40}];r["!cols"]=n,Ka.book_append_sheet(t,r,"微信号列表");const a=`微信号列表_${new Date().toISOString().slice(0,19).replace(/[:-]/g,"").replace("T","_")}.xlsx`,o=Rh(t,{bookType:"xlsx",type:"array"}),f=new Blob([o],{type:"application/octet-stream"});w4.saveAs(f,a),console.log(`导出成功: ${a}`)}catch(e){console.error("导出Excel失败:",e),alert("导出失败，请重试")}},logout(){localStorage.removeItem("adminLoggedIn"),this.$router.push("/admin").catch(e=>{console.error("路由跳转失败:",e)})}}},S4={class:"admin-dashboard"},y4={class:"container"},A4={class:"header"},F4={class:"stats"},C4={class:"stat-card"},O4={class:"stat-number"},R4={class:"accounts-section"},P4={class:"section-header"},D4={class:"header-actions"},I4=["disabled"],N4=["disabled"],b4={key:0,class:"loading"},k4={key:1,class:"error-state"},L4={class:"error-message"},M4={key:2,class:"empty-state"},B4={key:3,class:"accounts-list"},U4={class:"account-number"},H4={class:"account-info"},W4={class:"wechat-id"},V4={class:"submit-time"},j4={class:"ip-address"},$4={class:"action-buttons"},G4=["onClick"],K4=["onClick","disabled"];function X4(e,t,r,n,i,s){return Bt(),zt("div",S4,[we("div",y4,[we("div",A4,[t[4]||(t[4]=we("h1",{class:"title"},"微信号管理后台",-1)),we("button",{onClick:t[0]||(t[0]=(...a)=>s.logout&&s.logout(...a)),class:"logout-btn"},"退出登录")]),we("div",F4,[we("div",C4,[we("div",O4,Mt(i.wechatAccounts.length),1),t[5]||(t[5]=we("div",{class:"stat-label"},"总收集数量",-1))])]),we("div",R4,[we("div",P4,[t[6]||(t[6]=we("h2",null,"微信号列表",-1)),we("div",D4,[we("button",{onClick:t[1]||(t[1]=(...a)=>s.exportToExcel&&s.exportToExcel(...a)),class:"export-btn",disabled:i.isLoading||i.wechatAccounts.length===0}," 导出Excel ",8,I4),we("button",{onClick:t[2]||(t[2]=(...a)=>s.refreshData&&s.refreshData(...a)),class:"refresh-btn",disabled:i.isLoading},Mt(i.isLoading?"刷新中...":"刷新"),9,N4)])]),i.isLoading?(Bt(),zt("div",b4,"加载中...")):i.loadError?(Bt(),zt("div",k4,[we("p",L4,Mt(i.errorMessage),1),we("button",{onClick:t[3]||(t[3]=(...a)=>s.refreshData&&s.refreshData(...a)),class:"retry-btn"},"重试")])):i.wechatAccounts.length===0?(Bt(),zt("div",M4,t[7]||(t[7]=[we("p",null,"暂无收集到的微信号",-1)]))):(Bt(),zt("div",B4,[(Bt(!0),zt(wr,null,td(i.wechatAccounts,(a,o)=>(Bt(),zt("div",{key:a.id,class:"account-card"},[we("div",U4,Mt(o+1),1),we("div",H4,[we("div",W4,Mt(a.wechatId),1),we("div",V4,Mt(s.formatTime(a.submitTime)),1),we("div",j4," IP: "+Mt(a.ipAddress||"未知"),1)]),we("div",$4,[we("button",{onClick:f=>s.copyWechatId(a.wechatId,f),class:"copy-btn"}," 复制 ",8,G4),we("button",{onClick:f=>s.deleteWechatId(a),class:"delete-btn",disabled:i.deletingIds.has(a.id)},Mt(i.deletingIds.has(a.id)?"删除中...":"删除"),9,K4)])]))),128))]))])])])}const z4=ji(T4,[["render",X4],["__scopeId","data-v-9317f8c5"]]),Y4=[{path:"/",redirect:"/login"},{path:"/login",name:"Login",component:hg},{path:"/welcome",name:"Welcome",component:Tg},{path:"/admin",name:"AdminLogin",component:Ng},{path:"/admin/dashboard",name:"AdminDashboard",component:z4,meta:{requiresAuth:!0}}],bh=yx({history:Qp(),routes:Y4});bh.beforeEach((e,t,r)=>{if(e.meta.requiresAuth&&!localStorage.getItem("adminLoggedIn")){r("/admin");return}r()});const kh=mp(Tp);kh.use(bh);kh.mount("#app");
