<template>
  <div class="admin-users">
    <div class="users-header">
      <div class="header-actions">
        <button @click="showAddModal = true" class="add-btn">
          <span class="btn-icon">➕</span>
          <span class="btn-text">添加用户</span>
        </button>
        <button @click="refreshData" class="refresh-btn" :disabled="isLoading">
          <span class="btn-icon">🔄</span>
          <span class="btn-text">{{ isLoading ? "刷新中..." : "刷新" }}</span>
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="loading">加载中...</div>

    <div v-else-if="loadError" class="error-state">
      <p class="error-message">{{ errorMessage }}</p>
      <button @click="refreshData" class="retry-btn">重试</button>
    </div>

    <div v-else class="users-list">
      <div class="users-table">
        <div class="table-header">
          <div class="header-cell">用户名</div>
          <div class="header-cell">姓名</div>
          <div class="header-cell">角色</div>
          <div class="header-cell">创建时间</div>
          <div class="header-cell">最后登录</div>
          <div class="header-cell">操作</div>
        </div>

        <div v-for="user in users" :key="user.id" class="table-row">
          <div class="table-cell">{{ user.username }}</div>
          <div class="table-cell">{{ user.name }}</div>
          <div class="table-cell">
            <span class="role-badge" :class="user.role">
              {{ getRoleText(user.role) }}
            </span>
          </div>
          <div class="table-cell">{{ formatTime(user.createTime) }}</div>
          <div class="table-cell">
            {{
              user.lastLoginTime ? formatTime(user.lastLoginTime) : "从未登录"
            }}
          </div>
          <div class="table-cell">
            <div class="action-buttons">
              <button @click="changePassword(user)" class="change-pwd-btn">
                🔑 改密码
              </button>
              <button
                v-if="user.role !== 'super_admin'"
                @click="deleteUser(user)"
                class="delete-btn"
                :disabled="deletingIds.has(user.id)"
              >
                {{ deletingIds.has(user.id) ? "删除中..." : "🗑️ 删除" }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加用户模态框 -->
    <div v-if="showAddModal" class="modal-overlay" @click="closeAddModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>添加新用户</h3>
          <button @click="closeAddModal" class="close-btn">✕</button>
        </div>

        <form @submit.prevent="addUser" class="modal-form">
          <div class="form-group">
            <label>用户名</label>
            <input
              v-model="newUser.username"
              type="text"
              placeholder="请输入用户名"
              class="form-input"
              required
            />
          </div>

          <div class="form-group">
            <label>姓名</label>
            <input
              v-model="newUser.name"
              type="text"
              placeholder="请输入姓名"
              class="form-input"
              required
            />
          </div>

          <div class="form-group">
            <label>角色</label>
            <select v-model="newUser.role" class="form-select" required>
              <option value="">请选择角色</option>
              <option value="admin">管理员</option>
              <option value="user">普通用户</option>
            </select>
          </div>

          <div class="form-group">
            <label>密码</label>
            <input
              v-model="newUser.password"
              type="password"
              placeholder="请输入密码"
              class="form-input"
              required
            />
          </div>

          <div class="form-actions">
            <button type="button" @click="closeAddModal" class="cancel-btn">
              取消
            </button>
            <button type="submit" class="submit-btn" :disabled="isAdding">
              {{ isAdding ? "添加中..." : "添加" }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 修改密码模态框 -->
    <div
      v-if="showPasswordModal"
      class="modal-overlay"
      @click="closePasswordModal"
    >
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>修改密码 - {{ currentUser?.name }}</h3>
          <button @click="closePasswordModal" class="close-btn">✕</button>
        </div>

        <form @submit.prevent="updatePassword" class="modal-form">
          <div class="form-group">
            <label>新密码</label>
            <input
              v-model="newPassword"
              type="password"
              placeholder="请输入新密码"
              class="form-input"
              required
            />
          </div>

          <div class="form-actions">
            <button
              type="button"
              @click="closePasswordModal"
              class="cancel-btn"
            >
              取消
            </button>
            <button
              type="submit"
              class="submit-btn"
              :disabled="isUpdatingPassword"
            >
              {{ isUpdatingPassword ? "修改中..." : "修改" }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  name: "AdminUsers",
  data() {
    return {
      users: [],
      isLoading: false,
      loadError: false,
      errorMessage: "",
      deletingIds: new Set(),

      // 添加用户相关
      showAddModal: false,
      isAdding: false,
      newUser: {
        username: "",
        name: "",
        role: "",
        password: "",
      },

      // 修改密码相关
      showPasswordModal: false,
      isUpdatingPassword: false,
      currentUser: null,
      newPassword: "",
    };
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      this.isLoading = true;
      this.loadError = false;
      this.errorMessage = "";

      try {
        const response = await axios.get("/api/admin/users");
        this.users = response.data.admins || [];
      } catch (error) {
        console.error("加载用户列表失败:", error);
        this.loadError = true;
        this.errorMessage = "加载用户列表失败，请重试";
      } finally {
        this.isLoading = false;
      }
    },

    async refreshData() {
      await this.loadData();
    },

    getRoleText(role) {
      const roleMap = {
        super_admin: "超级管理员",
        admin: "管理员",
        user: "普通用户",
      };
      return roleMap[role] || "未知角色";
    },

    formatTime(timeString) {
      const date = new Date(timeString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    closeAddModal() {
      this.showAddModal = false;
      this.newUser = {
        username: "",
        name: "",
        role: "",
        password: "",
      };
    },

    async addUser() {
      this.isAdding = true;

      try {
        const response = await axios.post("/api/admin/users", this.newUser);

        if (response.data.success) {
          alert("用户添加成功");
          this.closeAddModal();
          await this.loadData();
        } else {
          alert(response.data.message || "添加失败");
        }
      } catch (error) {
        console.error("添加用户失败:", error);
        alert(error.response?.data?.message || "添加用户失败，请重试");
      } finally {
        this.isAdding = false;
      }
    },

    changePassword(user) {
      this.currentUser = user;
      this.newPassword = "";
      this.showPasswordModal = true;
    },

    closePasswordModal() {
      this.showPasswordModal = false;
      this.currentUser = null;
      this.newPassword = "";
    },

    async updatePassword() {
      this.isUpdatingPassword = true;

      try {
        const response = await axios.put(
          `/api/admin/users/${this.currentUser.id}/password`,
          {
            newPassword: this.newPassword,
          }
        );

        if (response.data.success) {
          alert("密码修改成功");
          this.closePasswordModal();
        } else {
          alert(response.data.message || "修改失败");
        }
      } catch (error) {
        console.error("修改密码失败:", error);
        alert(error.response?.data?.message || "修改密码失败，请重试");
      } finally {
        this.isUpdatingPassword = false;
      }
    },

    async deleteUser(user) {
      if (!confirm(`确定要删除用户 "${user.name}" 吗？`)) {
        return;
      }

      this.deletingIds.add(user.id);

      try {
        const response = await axios.delete(`/api/admin/users/${user.id}`);

        if (response.data.success) {
          alert("用户删除成功");
          await this.loadData();
        } else {
          alert(response.data.message || "删除失败");
        }
      } catch (error) {
        console.error("删除用户失败:", error);
        alert(error.response?.data?.message || "删除用户失败，请重试");
      } finally {
        this.deletingIds.delete(user.id);
      }
    },
  },
};
</script>

<style scoped>
.admin-users {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.users-header {
  padding: 20px 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.add-btn,
.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s;
}

.add-btn {
  background: #28a745;
  color: white;
}

.add-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.refresh-btn {
  background: #667eea;
  color: white;
}

.refresh-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading,
.error-state {
  padding: 40px;
  text-align: center;
  color: #666;
}

.error-message {
  color: #ff4757;
  margin-bottom: 20px;
  font-size: 16px;
}

.retry-btn {
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.retry-btn:hover {
  background: #5a6fd8;
}

.users-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1.5fr 1.5fr 1.5fr;
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
}

.header-cell {
  padding: 15px 20px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1.5fr 1.5fr 1.5fr;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.3s;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-cell {
  padding: 20px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.role-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

.role-badge.super_admin {
  background: #ff6b6b;
  color: white;
}

.role-badge.admin {
  background: #4ecdc4;
  color: white;
}

.role-badge.user {
  background: #95a5a6;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.change-pwd-btn,
.delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.change-pwd-btn {
  background: #ffc107;
  color: #333;
}

.change-pwd-btn:hover {
  background: #e0a800;
  transform: translateY(-1px);
}

.delete-btn {
  background: #ff4757;
  color: white;
}

.delete-btn:hover:not(:disabled) {
  background: #ff3838;
  transform: translateY(-1px);
}

.delete-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #333;
}

.modal-form {
  padding: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-input,
.form-select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn {
  background: #667eea;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .users-header {
    padding: 15px 20px;
  }

  .header-actions {
    flex-direction: column;
  }

  .add-btn,
  .refresh-btn {
    justify-content: center;
  }

  .users-table {
    overflow-x: auto;
  }

  .table-header,
  .table-row {
    grid-template-columns: 120px 100px 100px 150px 150px 150px;
    min-width: 770px;
  }

  .header-cell,
  .table-cell {
    padding: 12px 10px;
    font-size: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .change-pwd-btn,
  .delete-btn {
    font-size: 10px;
    padding: 4px 8px;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-form {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
